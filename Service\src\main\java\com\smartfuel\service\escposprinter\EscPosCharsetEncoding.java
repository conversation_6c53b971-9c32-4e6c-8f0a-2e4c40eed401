package com.smartfuel.service.escposprinter;

public class EscPosCharsetEncoding {
    private byte[] charsetCommand;

    private String charsetName;

    public EscPosCharsetEncoding(String paramString, int paramInt) {
        this.charsetName = paramString;
        this.charsetCommand = new byte[] { 27, 116, (byte)paramInt };
    }

    public byte[] getCommand() {
        return this.charsetCommand;
    }

    public String getName() {
        return this.charsetName;
    }
}