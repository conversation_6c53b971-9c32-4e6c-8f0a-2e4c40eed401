package com.smartfuel.service.models.forecourt;

import java.util.ArrayList;

public class FuelPoint {
    ArrayList<GradePrice> GradePrices;

    String Id;

    String State;

    boolean nozzleLifted;

    public ArrayList<GradePrice> getGradePrices() {
        return this.GradePrices;
    }

    public String getId() {
        return this.Id;
    }

    public String getState() {
        return this.State;
    }

    public void setGradePrices(ArrayList<GradePrice> paramArrayList) {
        this.GradePrices = paramArrayList;
    }

    public void setId(String paramString) {
        this.Id = paramString;
    }

    public void setState(String paramString) {
        this.State = paramString;
    }

    public boolean isNozzleLifted() {
        return nozzleLifted;
    }

    public void setNozzleLifted(boolean nozzleLifted) {
        this.nozzleLifted = nozzleLifted;
    }
}
