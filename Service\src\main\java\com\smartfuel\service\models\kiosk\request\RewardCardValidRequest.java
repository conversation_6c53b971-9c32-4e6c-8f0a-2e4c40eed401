package com.smartfuel.service.models.kiosk.request;

import com.smartfuel.service.IKioskApiService;
import com.smartfuel.service.sqlite.models.RewardCardDiscount;
import com.smartfuel.service.utils.ApiException;
import com.smartfuel.service.utils.ApiServiceHelper;

import java.util.List;

public class RewardCardValidRequest {

    private String track2;

    public RewardCardValidRequest(String track2){
        this.track2 = track2;
    }

    public static List<RewardCardDiscount> send(IKioskApiService kioskApiService,
                                                String track2) throws ApiException {
        RewardCardValidRequest request = new RewardCardValidRequest(track2);
        return ApiServiceHelper.executeAPI(kioskApiService.validRewardCard(request));
    }

    public String getTrack2() {
        return track2;
    }

    public void setTrack2(String track2) {
        this.track2 = track2;
    }
}
