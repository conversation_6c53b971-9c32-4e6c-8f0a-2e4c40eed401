package com.smartfuel.service.logger;

public class Log {
    public static void i(String tag, String message){
        LoggerService.log(LogcatReader.Level.INFO, tag, message, null);
    }

    public static void e(String tag, String message, Throwable throwable){
        LoggerService.log(LogcatReader.Level.ERROR, tag, message, throwable);
    }

    public static void e(String tag, Throwable throwable){
        LoggerService.log(LogcatReader.Level.ERROR, tag, "", throwable);
    }

    public static void e(String tag, String message){
        LoggerService.log(LogcatReader.Level.ERROR, tag, message, new Exception(message));
    }

    public static void d(String tag, String message){
        LoggerService.log(LogcatReader.Level.DEBUG, tag, message, null);
    }

    public static void v(String tag, String message){
        LoggerService.log(LogcatReader.Level.VERBOSE, tag, message, null);
    }

    public static void w(String tag, String message){
        LoggerService.log(LogcatReader.Level.WARNING, tag, message, null);
    }

    public static void w(String tag, Throwable throwable){
        LoggerService.log(LogcatReader.Level.WARNING, tag, "", throwable);
    }
}
