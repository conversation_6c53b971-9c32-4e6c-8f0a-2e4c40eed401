package com.smartfuel.service.pax;

import com.google.gson.Gson;
import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.connection.IDeviceConnection;
import com.smartfuel.service.models.transaction.IM30Transaction;


public class Terminal{
    public TerminalCommands terminalCommands = null;


    public Terminal (IDeviceConnection terminalConnection) throws ConnectionException
    {
        this(terminalConnection != null ? new TerminalCommands(terminalConnection) : null);
    }
    public Terminal(TerminalCommands terminalCommands) throws ConnectionException {

        if (terminalCommands != null) {
            this.terminalCommands = terminalCommands.connect();
        }
    }

    public boolean isConnected(){
        if (this.terminalCommands != null) {
            return this.terminalCommands.isConnected();
        }
        return false;
    }

    public Terminal disconnectTerminal() {
        if (this.terminalCommands != null) {
            this.terminalCommands.disconnect();
            this.terminalCommands = null;
        }
        return this;
    }

    public Terminal connectTerminal() throws ConnectionException {
        if (terminalCommands != null) {
            terminalCommands.connect();
        }
        return this;
    }
    public Terminal sendTerminalCommand(IM30Transaction transaction) throws ConnectionException {
        Gson gson = new Gson();
        this.terminalCommands.sendCommand(gson.toJson(transaction));

        return this;
    }
    public Terminal readTerminalMessages() throws ConnectionException {
        this.terminalCommands.readCommand();
        return this;
    }
}

