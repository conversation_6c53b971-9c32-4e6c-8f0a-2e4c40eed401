package com.smartfuel.service.posmaster;

import android.content.Context;

import com.smartfuel.service.models.posmaster.OPTExport;
import com.smartfuel.service.models.posmaster.Order;
import com.smartfuel.service.models.posmaster.FuelPriceUpdate;
import com.smartfuel.service.sqlite.models.Transaction;

import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

public interface IPOSMaster {
    void HeartBeat(Context serviceContext,final IPOSMasterCallBack<FuelPriceUpdate> callBack);
    void CatalogApplied(final Context serviceContext, final String CatalogId, final String CatalogType);
    boolean TransactionDataUpload(final Context serviceContext,final OPTExport exportData);
}
