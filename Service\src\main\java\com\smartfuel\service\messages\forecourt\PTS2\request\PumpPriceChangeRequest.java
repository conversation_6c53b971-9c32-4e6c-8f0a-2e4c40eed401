package com.smartfuel.service.messages.forecourt.PTS2.request;

import java.math.BigDecimal;
import java.util.ArrayList;

public class PumpPriceChangeRequest {
    private ArrayList<Packet> Packets;
    private String Protocol;
    public PumpPriceChangeRequest(ArrayList<FuelGrade> fuelGradePrices){
        Data d = new Data();
        d.FuelGrades = fuelGradePrices;

        Packet p = new Packet();
        p.setId(1);
        p.setType("SetFuelGradesConfiguration");
        p.setData(d);

        this.Protocol = "jsonPTS";

        Packets = new ArrayList<Packet>();
        this.Packets.add(p);
    }
    public class Packet {
        public int getId() {
            return Id;
        }

        public void setId(int id) {
            this.Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            this.Type = type;
        }

        private int Id;
        private String Type;

        public Data getData() {
            return Data;
        }

        public void setData(Data data) {
            Data = data;
        }

        private Data Data;
    }
    public class Data{
        public ArrayList<FuelGrade> FuelGrades;
    }
    public static class FuelGrade{
       private int Id;
       private String Name;
       private float Price;
       private float ExpansionCoefficient;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            this.Id = id;
        }

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            this.Name = name;
        }

        public double getPrice() {
            return Price;
        }

        public void setPrice(float price) {
            this.Price = price/100F;
        }

        public double getExpansionCoefficient() {
            return ExpansionCoefficient;
        }

        public void setExpansionCoefficient(float expansionCoefficient) {
            this.ExpansionCoefficient = expansionCoefficient;
        }
    }

}
