package com.smartfuel.service.forecourtcontroller;

import com.smartfuel.service.logger.Log;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.FuelTransaction;
import com.smartfuel.service.models.forecourt.GradePrice;
import com.smartfuel.service.models.forecourt.GradePrices;
import com.smartfuel.service.sqlite.models.ForecourtControllerTransaction;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class FakeService extends ForecourtController implements IForecourtController {

    private static final String TAG = FakeService.class.getSimpleName();

    private final long TimeWait = 5000;

    private final Callback callbackHandler;

    private HashMap<String, String> configurationGradeMap;

    private ArrayList<FuelPoint> configuredFuelPoints;

    private GradePrices configuredGradePrices;

    private String myOperationMode;
    private String myPosIpAddress;

    private Map<Integer, String> prepaidAmount;

    <T> FakeService(int posId, String paramString, int port, String operationMode, String posIpAddress, Callback<T> paramCallback) {
        this.myOperationMode = operationMode;
        if(this.myOperationMode.isEmpty())
            this.myOperationMode = "single"; // Single is the default operation mode if none is provided.

        this.myPosIpAddress = posIpAddress;
        if(this.myPosIpAddress == null)
            this.myPosIpAddress = "";

        prepaidAmount = new HashMap<>();

        this.callbackHandler = paramCallback;
    }

    private void performTimeWait(){
        try {
            Thread.sleep(TimeWait);
        }catch (Exception e){}
    }

    private void setupFuelPoints() {
        if (this.configuredFuelPoints == null) // There are no configured fuelling points yet
        {
            this.configuredFuelPoints = new ArrayList<>();
        }

        int point = 1;
        for (Map.Entry<String, String> entry : configurationGradeMap.entrySet()) {
            String domsGradeId = entry.getKey();
            String domsGradeName = entry.getValue();

            if(domsGradeName.equalsIgnoreCase("n/a"))
                continue;

            FuelPoint fuelPoint = new FuelPoint();
            fuelPoint.setId(String.format("%1$" + 2 + "s", point).replace(' ', '0'));
            fuelPoint.setState("02H");

            ArrayList<GradePrice> arrayList = new ArrayList();

            GradePrice gradePrice = new GradePrice();
            gradePrice.setId(domsGradeId);
            gradePrice.setLabel("FG_" + domsGradeId);
            arrayList.add(gradePrice);

            fuelPoint.setGradePrices(arrayList);

            FuelPoint currentFP = this.configuredFuelPoints.stream().filter(fuelPoint1 -> fuelPoint.getId().equals(fuelPoint1.getId())).findFirst().orElse(null);
            int i = this.configuredFuelPoints.indexOf(currentFP);
            if (currentFP == null) // fuelpoint not found in the configured array list - add
            {
                this.configuredFuelPoints.add(fuelPoint);
            } else // fuelpoint found in the configured array list - update
            {
                this.configuredFuelPoints.set(i, fuelPoint);
            }
            point++;
        }
    }

    private void setupGradePrices() {
        GradePrices configuredPrices = new GradePrices();
        configuredPrices.setGradePrices(new ArrayList<GradePrice>());

        configuredPrices.setPriceSetId(1);

        //configuredPrices.setPriceGroupId();

        for (Map.Entry<String, String> entry : configurationGradeMap.entrySet()) {
            String domsGradeId = entry.getKey();
            String domsGradeName = entry.getValue();

            if(domsGradeName.equalsIgnoreCase("n/a"))
                continue;

            GradePrice gradePrice = new GradePrice();
            gradePrice.setId(domsGradeId);
            gradePrice.setLabel("FG_" + domsGradeId);
            gradePrice.setName(domsGradeName);
            gradePrice.setPrice(Float.valueOf(Float.parseFloat("1234") / 10.0F));

            configuredPrices.getGradePrices().add(gradePrice);

            this.configuredGradePrices = configuredPrices;

            for (FuelPoint fuelPoint : this.configuredFuelPoints) {
                if (fuelPoint.getGradePrices() != null)
                    for (GradePrice gradePrice1 : fuelPoint.getGradePrices()) {
                        if (gradePrice1.getId().equals(domsGradeId))
                            gradePrice1.setPrice(gradePrice.getPrice());
                    }
            }
        }
    }

    @Override
    public <T> void AuthoriseFuellingPoint(String posId, T response) {
        Log.i(TAG, "AuthoriseFuellingPoint");
        //performTimeWait();
        try {
            this.callbackHandler.FuelPointAuthorized(response.toString());
        } catch (InterruptedException e) {
            Log.e(TAG, e);
        }
        performTimeWait();
        int count = (int)(Integer.parseInt(prepaidAmount.get(Integer.parseInt(response.toString())))/1000.0);
        for(int i = 0; i < count; i++)
            performTimeWait();
        this.callbackHandler.FuelTransactionComplete(configuredFuelPoints.stream().filter(x -> Integer.parseInt(x.getId()) == Integer.parseInt(response.toString())).findFirst().get());
    }

    @Override
    public void CancelFuelPointAuthorisation(String posId, String fuelpointId) {
        Log.i(TAG, "CancelFuelPointAuthorisation");
    }

    @Override
    public void CompleteFuelTransaction(String posId, String fuelpointId, String trnSeqNumber, long volume, long fueldispensedAmount) {
        Log.i(TAG, "CompleteFuelTransaction");
        prepaidAmount.remove(Integer.parseInt(fuelpointId));
        try {
            this.callbackHandler.FuellingStatusUpdate(configuredFuelPoints.stream().filter(x -> Integer.parseInt(x.getId()) == Integer.parseInt(fuelpointId)).findFirst().get());
        } catch (InterruptedException e) {
            Log.e(TAG, e);
        }
    }

    @Override
    public void FetchFuelTransaction(String posId, int fuelpointId, String trnSeqNumber) {
        Log.i(TAG, "FetchFuelTransaction");
        performTimeWait();
        this.callbackHandler.ClearFuelTransactionBuffer(configuredFuelPoints.stream().filter(x -> Integer.parseInt(x.getId()) == fuelpointId).findFirst().get());
    }

    @Override
    public void GetFuelPriceData() {
        Log.i(TAG, "GetFuelPriceData");
    }

    @Override
    public void Heartbeat() {
        performTimeWait();
        checkConnectionStatusResponse();
    }

    @Override
    public void Logon(String posId, String applicationId, String clientVersion) {
        Log.i(TAG, "Logon");
        setupFuelPoints();
        setupGradePrices();
        this.callbackHandler.ForecourtControllerLoginComplete();
        performTimeWait();
        try {
            this.callbackHandler.SetupComplete();
        } catch (InterruptedException e) {
            Log.e(TAG, e);
        }
    }

    @Override
    public void PreparePrepaidTransaction(String posId, String fuelpointId, String gradeId, String prepaidAmount) {
        //performTimeWait();
        try {
            this.prepaidAmount.put(Integer.parseInt(fuelpointId), prepaidAmount);
            this.callbackHandler.FuellingStatusUpdate(configuredFuelPoints.stream().filter(x -> Integer.parseInt(x.getId()) == Integer.parseInt(fuelpointId)).findFirst().get());
            performTimeWait();
            performTimeWait();
            this.callbackHandler.FuelPointPrePaidSetupComplete(fuelpointId);
        } catch (InterruptedException e) {
            Log.e(TAG, e);
        }
    }

    @Override
    public void setFuelGradeNames(Map fuelGradeNames) {
        configurationGradeMap = (HashMap<String, String>) fuelGradeNames;
    }

    @Override
    public void ChangeFuelPrices(String priceSetId, ArrayList<String> priceGroupIds, ArrayList<String> fuelGradeIds, ArrayList<String> priceGroups, String priceActivationDateTime) {
        for (int i = 0; i < fuelGradeIds.size(); i++) {
            ArrayList<GradePrice> gps = this.configuredGradePrices.getGradePrices();
            for (GradePrice gp: gps) {
                if(gp.getId().equals(fuelGradeIds.get(i))){
                    gp.setPrice(Float.valueOf(Float.parseFloat(priceGroups.get(i)) / 10.0F));

                    for (FuelPoint fuelPoint : this.configuredFuelPoints) {
                        if (fuelPoint.getGradePrices() != null)
                            for (GradePrice gradePrice1 : fuelPoint.getGradePrices()) {
                                if (gradePrice1.getId().equals(gp.getId()))
                                    gradePrice1.setPrice(gp.getPrice());
                            }
                    }
                }
            }
        }
        try {
            this.callbackHandler.ForecourtControllerReady();
        } catch (InterruptedException e) {
            Log.e(TAG, e);
        }
    }

    @Override
    public ArrayList<FuelPoint> getConfiguredFuelPoints() {
        return this.configuredFuelPoints;
    }

    @Override
    public GradePrices getConfiguredGradePrices() {
        if (configuredGradePrices != null)
            return this.configuredGradePrices;
        else
            return new GradePrices();
    }

    @Override
    public String parseResponse(String response, String lookupfield) throws IOException {
        return null;
    }

    public static boolean isReachableByPing(String ipAddress) {
        try {
            String pingCmd = "ping -c 1 " + ipAddress;
            Runtime runtime = Runtime.getRuntime();
            Process process = runtime.exec(pingCmd);
            int exitValue = process.waitFor();
            return (exitValue == 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private void checkConnectionStatusResponse() {
        try {
            boolean connected = false;
            boolean connectedPosDevice = false;
            String[] posIps = myPosIpAddress.split("\\,");
            for (String item : posIps) {
                if(isReachableByPing(item)){
                    connectedPosDevice = true;
                }
            }
            connected = true;

            if (connected && !connectedPosDevice && myOperationMode.equals("single"))
                callbackHandler.ShowHomeScreen();
            if (connectedPosDevice && myOperationMode.equals("single"))
                callbackHandler.ShowStandbyScreen();

        } catch (Exception e) {
            Log.w(TAG, e);
            e.printStackTrace();
        }
    }

    @Override
    public <T> ForecourtControllerTransaction getTransactionData(T response) {
        FuelPoint fp = (FuelPoint)response;
        GradePrice gp = fp.getGradePrices().get(0);

        ForecourtControllerTransaction forecourtControllerTransaction = new ForecourtControllerTransaction();
        forecourtControllerTransaction.setTime(System.currentTimeMillis());
        forecourtControllerTransaction.setFpId(Integer.parseInt(fp.getId()));
        forecourtControllerTransaction.setFpLockId(0);
        forecourtControllerTransaction.setFcGradeId(gp.getId());
        if(prepaidAmount.containsKey(Integer.parseInt(fp.getId()))) {
            //forecourtControllerTransaction.setFpMainState("01H");
            forecourtControllerTransaction.setFpMainState("04H");
        }else{
            forecourtControllerTransaction.setFpMainState("02H");
        }
        forecourtControllerTransaction.setFpSubState2("");
        forecourtControllerTransaction.setFpSubState4("");
        forecourtControllerTransaction.setFuellingDataMonE(0);
        forecourtControllerTransaction.setFuellingDataVolE(0);
        if(forecourtControllerTransaction.getFpMainState().equals("04H"))
            forecourtControllerTransaction.setFpSubState2("NozzleLifted");
        return forecourtControllerTransaction;
    }

    @Override
    public <T> FuelTransaction getFuelTransactionDetails(T response) {
        FuelPoint fp = (FuelPoint)response;
        GradePrice gp = fp.getGradePrices().get(0);//getConfiguredGradePrices().getGradePrices().stream().filter(x -> Integer.parseInt(x.getId()) == Integer.parseInt(prepaidGradeId)).findFirst().get();
        FuelTransaction ft = new FuelTransaction();
        ft.setFuelPointId(Integer.parseInt(fp.getId()));
        ft.setSeqNumber(1 + "");
        ft.setFuelGradeId(gp.getId());
        ft.setMoneyDue(Long.parseLong(prepaidAmount.get(Integer.parseInt(fp.getId()))));
        ft.setVolume((long) ((Long.parseLong(prepaidAmount.get(Integer.parseInt(fp.getId())))/gp.getPrice())*100.0));
        ft.setPrice((long)(gp.getPrice()*10.0));
        return ft;
    }
}

