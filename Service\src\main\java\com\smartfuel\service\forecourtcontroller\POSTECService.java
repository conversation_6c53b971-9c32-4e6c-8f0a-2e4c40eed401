package com.smartfuel.service.forecourtcontroller;


import com.smartfuel.service.logger.Log;
import com.smartfuel.service.messages.forecourt.POSTEC.enums.PumpStatus;
import com.smartfuel.service.messages.forecourt.POSTEC.enums.TransactionType;
import com.smartfuel.service.messages.forecourt.POSTEC.request.AuthoriseDispenserRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.request.ConfirmTransactionRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.request.DispenserExtendedStatusRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.request.DispenserInfoRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.request.DownloadTransactionRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.request.GetGradePriceRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.request.GradePriceSetRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.request.GradeTitleRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.request.HoseGradeTankAssociationRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.request.LogonRequest;
import com.smartfuel.service.messages.forecourt.POSTEC.response.AuthoriseDispenserResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.ConfirmTransactionResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.DispenserExtendedStatusResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.DispenserInfoResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.DispenserStatusUpdate;
import com.smartfuel.service.messages.forecourt.POSTEC.response.DownloadTransactionResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.GetGradePriceResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.GradeTitleResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.HoseGradeTankAssociationResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.LoginClientUpdate;
import com.smartfuel.service.messages.forecourt.POSTEC.response.LogonResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.PCCResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.SetGradePriceResponse;
import com.smartfuel.service.messages.forecourt.POSTEC.response.StationConfigResponse;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.FuelTransaction;
import com.smartfuel.service.models.forecourt.GradePrice;
import com.smartfuel.service.models.forecourt.GradePrices;
import com.smartfuel.service.network.Constant;
import com.smartfuel.service.network.SocketConfig;
import com.smartfuel.service.network.TCPMessenger;
import com.smartfuel.service.network.UDPMessenger;
import com.smartfuel.service.sqlite.models.ForecourtControllerTransaction;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class POSTECService extends ForecourtController implements IForecourtController, TCPMessenger.Callback<String>, UDPMessenger.Callback<String> {
    private String forecourtControllerIP;
    private final PSS5000Service.Callback callbackHandler;
    private final SocketConfig socketConfig;
    private int gradeCount;
    private int dispenserCount;
    private GradePrices configuredGradePrices;
    private ArrayList<FuelPoint> configuredFuelPoints;
    private Thread udpNetwork;
    private int hoseGradeTankReqCount = 0;
    private int hoseGradeTankRespCount = 0;
    private int posId;
    private boolean isGradeMapped = false;
    private HashMap<String, String> configurationGradeMap;
    private HashMap<String, String> PostecGradeMap;
    private HashMap<String, String> DomsGradeMap;
    private HashSet<Integer> fuelPointAuthorised = new HashSet<>();

    private String myOperationMode;
    private String myPosReference;

    <T> POSTECService(String ipAddress, int port, String operationMode, String posReference, PSS5000Service.Callback<T> callbackHandler) {
        this.forecourtControllerIP = ipAddress;
        this.callbackHandler = callbackHandler;

        this.myOperationMode = operationMode;
        if(this.myOperationMode.isEmpty())
            this.myOperationMode = "single"; // Single is the default operation mode if none is provided.

        this.myPosReference = posReference;
        if(this.myPosReference == null)
            this.myPosReference = "";

        socketConfig = new SocketConfig(port, Constant.DEFAULT_TIME_OUT);
    }

    public void onUDPResponse(String response) throws InterruptedException {
        PCCResponse pccResponse = new PCCResponse(response);
        String[] messageInString = (pccResponse.getData()).split(" ");
        String responseFunctionName = messageInString[messageInString.length - 1];

        String responseType = pccResponse.getUdpResponseType();
        if (responseType.contains("Dispenser_status_update")) {
            this.callbackHandler.FuellingStatusUpdate(response);
        } else if (responseFunctionName.contains("Client_Login_Update")) {
            checkConnectionStatusResponse(response);
        } else if (responseFunctionName.contains("Status_Semaphore_Update")) {
            //TODO : Status Semaphore Update
        } else if(responseFunctionName.contains("Invalid")){
            Log.e("POSTEC UDP Error",response);
            throw new InterruptedException("Invalid POSTEC UDP Response");
        } else {
            Log.e("POSTEC UDP Response Unknown",response);
            throw new InterruptedException("Unknown POSTEC UDP Response");
        }
    }

    private void checkConnectionStatusResponse(String response){
        try {

            LoginClientUpdate clientUpdate = new LoginClientUpdate(response);
            boolean isLoggedOn = false;
            boolean connectedPosDevice = false;
            String[] posIps = myPosReference.split("\\,");

            for (int clientNode : clientUpdate.getClinetNodeNumber()) {
                if (clientNode == posId) {
                    isLoggedOn = true;
                }
                if(Arrays.stream(posIps).anyMatch((clientNode + "")::equals)){
                    connectedPosDevice = true;
                }
            }
            //if (!isLoggedOn) {
            //    Logon(posId + "", "OPT" + posId, "1.0.0");
            //}

            Log.d("checkConnectionStatusResponse", "Operation Mode " + myOperationMode);
            Log.d("checkConnectionStatusResponse", "POS Reference " + myPosReference);
            Log.d("checkConnectionStatusResponse", "Connected with POS " + connectedPosDevice);

            if (!isLoggedOn)
                Logon(posId + "", "OPT" + posId, "1.0.0");
                //callbackHandler.LogonRequired();
            //when the device is in multi operation mode - there are no additional events required - operates as designed

            //This device is connected, no other device is connected and it's in single device mode - then notify App to show Home Screen
            if (isLoggedOn && !connectedPosDevice && myOperationMode.equals("single"))
                callbackHandler.ShowHomeScreen();
            //This device is connected, other devices are connected and it's in single device mode - then notify App to show Standby Screen
            if (connectedPosDevice && myOperationMode.equals("single"))
                callbackHandler.ShowStandbyScreen();

        } catch (Exception e) {
            Log.w("checkConnectionStatusResponse", e);
            e.printStackTrace();
        }
    }

    public void onResponse(String response) throws InterruptedException {
        Log.i("POSTEC Response", response);
        PCCResponse pccResponse = new PCCResponse(response);
        Log.w("PCCResponse",pccResponse.getTcpResponseType());
        switch (pccResponse.getTcpResponseType()) {
            case "Invalid_Request":
            case "Invalid_Reques":
                Log.w("OnResponse",pccResponse.getTcpResponseType());
                break;
            case "Login_Client_Node":
                //validate that the login was successfully completed - LogonResponse will throw a Runtime Exception if the status response is not 2000
                //01 001 9970 09 2000 SB12.30 S6.36 17/03/16 Globalÿÿ H4.7 Ser#000012A067F5 0 Login_Client_Node
                try {
                    LogonResponse logonResponse = new LogonResponse(response);
                    if (configuredFuelPoints == null && configuredGradePrices == null) {
                        readConfiguration(logonResponse.getNodeNumber());
                        this.callbackHandler.ForecourtControllerLoginComplete();
                    }
                    Log.i("POSTEC", "Logon Complete");
                } catch (RuntimeException re) {
                    Log.e("POSTEC controller login failed", re);
                }
                break;

            case "Req_Station_Config":
                StationConfigResponse stationConfigResponse = new StationConfigResponse(response);
                // setup the number of dispensers and grades
                setupFuelPoints(stationConfigResponse);
                Log.i("Station Config - Dispenser Count:", String.valueOf(stationConfigResponse.getDispenserCount()));
                Log.i("Station Config - Grade Count:", String.valueOf(stationConfigResponse.getGradeCount()));
                // setup the site grades, names and prices
                //this.gradeCount = stationConfigResponse.getGradeCount()-1; // the grade count will be used to call and populate the grade price configuration.
                // for each grade - send a message to get the grade prices

                break;
            case "Set_Operating_Schedule":
                //readConfiguration();
                break;
            case "Set_Grade_Prices":
                SetGradePriceResponse gradePriceResponse = new SetGradePriceResponse(response);
                break;
            case "Req_Disp_Ext_Status":
                this.callbackHandler.FuelTransactionComplete(new DispenserExtendedStatusResponse(response));
                break;
            case "Download_Transaction":
                this.callbackHandler.ClearFuelTransactionBuffer(new DownloadTransactionResponse(response));
                break;
            case "Req_Grade_Prices":
                //01 002 1422 02 2000 01 0 0 Req_Grade_Prices
                setupGradePrices(new GetGradePriceResponse(response));
                break;
            case "Req_Grade_Title":
                //01 034 0123 04 2000 04 Super Req_Grade_Title
                setupGradeTitle(new GradeTitleResponse(response));
                break;
            case "Authorise_Dispenser_Ex":
                //01 034 0123 04 2000 04 Super Req_Grade_Title
                this.callbackHandler.FuelPointAuthorized(new AuthoriseDispenserResponse(response).getDispenser() + "");
                break;
            case "Req_Dispenser_Info":
                DispenserInfoResponse dispResponse = new DispenserInfoResponse(response);
                /*
                For each hose at this dispenser get the send the message to get the grade details
                 */
                for (int x = 1; x <= dispResponse.getNumHoses(); x++) {
                    HoseGradeTankAssociationRequest hgtaReq = new HoseGradeTankAssociationRequest(posId, dispResponse.getDispenserNumber(), x);
                    TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, hgtaReq.toString()), String.class, this);
                    TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
                    hoseGradeTankReqCount++;
                }
                break;
            case "Confirm_Transaction":
                ConfirmTransactionResponse confirmTransactionResponse = new ConfirmTransactionResponse(response);
                //fuelPointAuthorised.remove(confirmTransactionResponse.getDispenserNumber());
                break;
            case "Req_Hose_Grade_Tank_Ass":
                hoseGradeTankRespCount++;
                HoseGradeTankAssociationResponse hgtaResponse = new HoseGradeTankAssociationResponse(response);
                // using this response locate the associated dispenser in the configured fuel points and assign the grade details
                hgtaResponse.getDispenserNumber();
                FuelPoint currentFP = this.configuredFuelPoints.stream().filter(fuelPoint -> hgtaResponse.getDispenserNumber().equals(fuelPoint.getId())).findFirst().orElse(null);
                GradePrice currentGradePrice = this.configuredGradePrices.getGradePrices().stream().filter(gradePrice -> hgtaResponse.getGradeNumber().equals(gradePrice.getId())).findFirst().orElse(null);

                //Ensure only unique grades are add to a fuel point
                GradePrice currentFPGrade = currentFP.getGradePrices().stream().filter(gradePrice -> currentGradePrice.getId().equals(gradePrice.getId())).findFirst().orElse(null);
                if (currentFPGrade == null) {
                    currentFP.getGradePrices().add(currentGradePrice);
                    currentFPGrade = currentFP.getGradePrices().stream().filter(gradePrice -> currentGradePrice.getId().equals(gradePrice.getId())).findFirst().orElse(null);
                }
                if (currentFP.getId().equals(String.valueOf(this.dispenserCount)) && currentFPGrade.getId().equals(String.valueOf(this.gradeCount))) {
                    this.callbackHandler.ForecourtControllerReady();
                }
                if (hoseGradeTankReqCount == hoseGradeTankRespCount) {
                    this.callbackHandler.SetupComplete();
                    readUDPMessages(this);
                    updatePriceThread();
                }

                break;
            default:
                String exceptionMessage = "POSTEC Controller Failed: " + response;
                Log.e("RESPONSE", response);
                this.callbackHandler.Error(new Throwable(response));
                break;
        }

        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
        Thread.sleep(400);
        //this.callbackHandler.SetupComplete();
    }

//    private void setupOperatingSchedule(int nodeNumber, int scheduleId) {
//        SetOperatingScheduleRequest request;
//
//        request = new SetOperatingScheduleRequest(nodeNumber, scheduleId);
//        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, request.toString()), String.class, this);
//        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
//
//    }

    /*private void readUDPMessages(UDPMessenger.Callback<String> callback) {
        UDPMessenger messenger = UDPMessenger.getDefaultInstance();
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                while (true) {
                    messenger.readCommand(String.valueOf(socketConfig.port), String.class, callback);
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        };

        udpNetwork = new Thread(runnable);
        udpNetwork.start();

    }*/
    private void updatePriceThread(){
        ScheduledExecutorService scheduledFuelPriceUpdate = Executors.newSingleThreadScheduledExecutor();
        scheduledFuelPriceUpdate.scheduleAtFixedRate(() -> {
            try {
                for (GradePrice gradePrice : configuredGradePrices.getGradePrices()) {
                    for (Map.Entry<String, String> entry : PostecGradeMap.entrySet()) {
                        if (gradePrice.getId().equals(entry.getValue())) {
                            com.smartfuel.service.messages.forecourt.POSTEC.request.GetGradePriceRequest request = new GetGradePriceRequest(posId, Integer.parseInt(entry.getKey()));
                            TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, request.toString()), String.class, this);
                            TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                Log.e("OPTService", "Kiosk Upload Failed", e);
            }
        }, 60, 60, TimeUnit.SECONDS);
    }
    private void readUDPMessages(UDPMessenger.Callback<String> callback) {
        UDPMessenger messenger = UDPMessenger.getDefaultInstance();
        messenger.readCommand(String.valueOf(socketConfig.port), String.class, callback);
    }

    private void readConfiguration(int posId) throws InterruptedException {
        // send messages to get:
        // - Controller site configuration
        // The event handler will manage the setup and further events

        com.smartfuel.service.messages.forecourt.POSTEC.request.StationConfigRequest stationConfigRequest;
        stationConfigRequest = new com.smartfuel.service.messages.forecourt.POSTEC.request.StationConfigRequest(posId); //TODO ; Read the PosId from the configuration setup

        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, stationConfigRequest.toString()), String.class, this);
        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);

    }

    private void setupFuelPoints(com.smartfuel.service.messages.forecourt.POSTEC.response.StationConfigResponse response) {
/*
Use the StationConfigResponse to get the total dispenser count and total grade count.
Will need to maintain the total counts - to track completion progress.
For Each Dispenser - call the DispenserInfoRequest
For Each Grade - call the GradePriceRequest
Use a loop in this method to track setup completion
 */
        this.dispenserCount = response.getDispenserCount();
        this.gradeCount = response.getGradeCount();
        this.configuredFuelPoints = new ArrayList<>();

        this.configuredGradePrices = new GradePrices();
        this.configuredGradePrices.setGradePrices(new ArrayList<GradePrice>());
        this.configuredGradePrices.setPriceSetId(1);

        for (int x = 1; x <= this.gradeCount; x++) {
            com.smartfuel.service.messages.forecourt.POSTEC.request.GetGradePriceRequest request = new GetGradePriceRequest(posId, x);
            TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, request.toString()), String.class, this);
            TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
        }

        for (int y = 1; y <= this.dispenserCount; y++) {
            FuelPoint fuelPoint = new FuelPoint();
            fuelPoint.setId(String.format("%02d", y));
            fuelPoint.setState("02H");
            fuelPoint.setGradePrices(new ArrayList<GradePrice>());
            this.configuredFuelPoints.add(fuelPoint);
            DispenserInfoRequest request = new DispenserInfoRequest(posId, y, 1);
            TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, request.toString()), String.class, this);
            TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
        }
    }

    private void setupGradeTitle(com.smartfuel.service.messages.forecourt.POSTEC.response.GradeTitleResponse gradeTitleResponse) {
        for (GradePrice gradePrice : this.configuredGradePrices.getGradePrices()) {
            if (gradeTitleResponse.getGradeNumber() == Integer.parseInt(gradePrice.getId())) {
                gradePrice.setName(gradeTitleResponse.getGradeTitle().replace('ÿ', ' ').trim());
                break;
            }
        }
    }

    private void setupGradePrices(com.smartfuel.service.messages.forecourt.POSTEC.response.GetGradePriceResponse gradePriceResponse) {
        //TODO: Remove mock implementation for grades and prices
        //GradePrices configuredPrices = new GradePrices();
        //configuredPrices.setGradePrices(new ArrayList<GradePrice>());
        //configuredPrices.setPriceSetId(1);
        if (hoseGradeTankReqCount == hoseGradeTankRespCount && hoseGradeTankReqCount > 0 && hoseGradeTankRespCount > 0) {
            updateFuelPrice(gradePriceResponse.getGradeNumber(), gradePriceResponse.getSchedule1Price());
        } else {
            GradePrice gradePrice = new GradePrice();
            gradePrice.setId(String.format("%02d", gradePriceResponse.getGradeNumber()));
            gradePrice.setLabel("FG_" + String.format("%02d", gradePriceResponse.getGradeNumber()));
            gradePrice.setPrice(Float.valueOf(Float.parseFloat(String.valueOf(gradePriceResponse.getSchedule1Price() / 10.0F))));


            //configuredPrices.getGradePrices().add(gradePrice);
            this.configuredGradePrices.getGradePrices().add(gradePrice);
            //this.configuredGradePrices = configuredPrices;
            gradeNameRequest(gradePriceResponse.getNodeNumber(), Integer.parseInt(gradePrice.getId()));
        }
    }

    private void gradeNameRequest(int posId, int gradeNumber) {
        GradeTitleRequest request = new GradeTitleRequest(posId, gradeNumber);
        String str = request.toString();

        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), String.class, this);
        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
    }

    @Override
    public void onError(Throwable throwable) {
        if (throwable != null)
            this.callbackHandler.Error(throwable);
    }

    @Override
    public <T> void AuthoriseFuellingPoint(String posId, T data) {
        Log.i("Auth Fuelling Point", data.getClass().getName());
    }

    @Override
    public void CancelFuelPointAuthorisation(String posId, String fuelpointId) {

    }


    @Override
    public void CompleteFuelTransaction(String posId, String fuelpointId, String trnSeqNumber, long volume, long fueldispensedAmount) {
        ConfirmTransaction(Integer.parseInt(fuelpointId));
    }

    private void ConfirmTransaction(int dispenserNumber) {
        ConfirmTransactionRequest request = new ConfirmTransactionRequest(posId, dispenserNumber, TransactionType.SaleDownload);
        String str = request.toString();

        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), String.class, this);
        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
    }

    @Override
    public void FetchFuelTransaction(String posId, int fuelpointId, String trnSeqNumber) {
        downloadTransaction(fuelpointId);
    }

    @Override
    public void GetFuelPriceData() {

    }

    @Override
    public void Heartbeat() {
        String str = "Client Keepalive Msg" + "\r\n";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), String.class, this);
    }

    @Override
    public void Logon(String posId, String ApplicationId, String clientVersion) throws InterruptedException {
        LogonRequest request = new LogonRequest(Integer.parseInt(posId), ApplicationId, clientVersion);
        String str = request.toString();

        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), String.class, this);
        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
        this.posId = Integer.parseInt(posId);
    }


    @Override
    public void PreparePrepaidTransaction(String posId, String fuelpointId, String gradeId, String prepaidAmount) {
        fuelPointAuthorised.add(Integer.parseInt(fuelpointId));
        AuthoriseDispenserRequest request = new AuthoriseDispenserRequest(Integer.parseInt(posId), Integer.parseInt(fuelpointId), Long.parseLong(prepaidAmount), getGradeMap(posId, fuelpointId));
        //debug to break message
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, request.toString()), String.class, this);
        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
    }

    private String getGradeMap(String posId, String fuelpointId){
        String[] validGrades = this.callbackHandler.getValidGrades(Integer.parseInt(posId), Integer.parseInt(fuelpointId));
        if(validGrades != null) {
            int bitmask = 0;
            for (String validGrade : validGrades) {
                if(DomsGradeMap.containsKey(validGrade)) {
                    int value = Integer.parseInt(DomsGradeMap.get(validGrade));
                    switch (value){
                        case 1:
                            bitmask |= 0b0000000000000001;
                            break;
                        case 2:
                            bitmask |= 0b0000000000000010;
                            break;
                        case 3:
                            bitmask |= 0b0000000000000100;
                            break;
                        case 4:
                            bitmask |= 0b0000000000001000;
                            break;
                        case 5:
                            bitmask |= 0b0000000000010000;
                            break;
                        case 6:
                            bitmask |= 0b0000000000100000;
                            break;
                        case 7:
                            bitmask |= 0b0000000001000000;
                            break;
                        case 8:
                            bitmask |= 0b0000000010000000;
                            break;
                        case 9:
                            bitmask |= 0b0000000100000000;
                            break;
                        default:
                            break;
                    }
                }
            }
            return String.format("%04X", bitmask);
        }
        return "7FFF";
    }

    //new Random(System.currentTimeMillis()).nextInt(999)
    private void setupFuelGradeMap() {
        HashMap<String, String> PostecGradeMap = getPostecGradeMap();
        if(configuredGradePrices != null) {
            for (GradePrice gp : configuredGradePrices.getGradePrices()) {
                String fuelGrade = PostecGradeMap.get(gp.getId());
                if (fuelGrade == null)
                    fuelGrade = gp.getId();
                gp.setId(fuelGrade);
                gp.setLabel("FG_" + gp.getId());
            }
            isGradeMapped = true;
        }
    }

    private void updateFuelPrice(int fuelGrade, long fuelPrice) {
        float floatValue = (float) fuelPrice / 10.0f;
        HashMap<String, String> PostecGradeMap = getPostecGradeMap();
        String fuelGradeFormatted = String.format("%02d", fuelGrade);
        String domsFuelGrade = PostecGradeMap.get(fuelGradeFormatted);
        if (domsFuelGrade != null) {
            for (GradePrice gp : configuredGradePrices.getGradePrices()) {
                if (gp.getId().equals(domsFuelGrade)) {
                    gp.setPrice(floatValue);
                }
            }
        }
    }

    @Override
    public ArrayList<FuelPoint> getConfiguredFuelPoints() {
        if (!isGradeMapped)
            setupFuelGradeMap();
//        if (getPostecGradeMap().size() != configuredGradePrices.getGradePrices().size())
//            onError(new InterruptedException("Fuel Configuration is not configured correctly!"));
        return this.configuredFuelPoints;
    }

    @Override
    public GradePrices getConfiguredGradePrices() {
        if (!isGradeMapped)
            setupFuelGradeMap();
//        if (getPostecGradeMap().size() != configuredGradePrices.getGradePrices().size())
//            onError(new InterruptedException("Fuel Configuration is not configured correctly!"));
        if(isGradeMapped && configuredGradePrices != null)
            return configuredGradePrices;
        else
            return new GradePrices();
    }

    @Override
    public void ChangeFuelPrices(String priceSetId, ArrayList<String> priceGroupIds, ArrayList<String> fuelGradeIds, ArrayList<String> priceGroups, String priceActivationDateTime) {
        try {
            HashMap<String, String> PostecGradeMap = getPostecGradeMap();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = dateFormat.parse(priceActivationDateTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, 1);

            Date newDate = calendar.getTime();

            for (int i = 0; i < fuelGradeIds.size(); i++) {
                int fuelGrade = -1;
                for (Map.Entry<String, String> entry : PostecGradeMap.entrySet()) {
                    if (fuelGradeIds.get(i).equals(entry.getValue())) {
                        fuelGrade = Integer.parseInt(entry.getKey());
                        break;
                    }
                }
                if (fuelGrade > 0) {
                    GradePriceSetRequest request = new GradePriceSetRequest(posId,
                            fuelGrade,
                            newDate,
                            Long.parseLong(priceGroups.get(i)),
                            4
                    );
                    String str = request.toString();

                    TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), String.class, this);
                    TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
                }
            }
        } catch (ParseException e) {
            Log.i("Wrong date Format", "Exception: " + e.getMessage());
        }
    }

    @Override
    public <T> FuelTransaction getFuelTransactionDetails(T response) {
        HashMap<String, String> PostecGradeMap = getPostecGradeMap();
        com.smartfuel.service.models.forecourt.FuelTransaction fuelTransaction = new FuelTransaction();
        if (response instanceof DispenserExtendedStatusResponse) {
            DispenserExtendedStatusResponse dispenserExtendedStatusResponse = (DispenserExtendedStatusResponse) response;
            if (dispenserExtendedStatusResponse.getPumpStatus().equals(PumpStatus.ToPay1) ||
                    dispenserExtendedStatusResponse.getPumpStatus().equals(PumpStatus.ToPay2)) {
                String fuelGradeFormatted = String.format("%02d", dispenserExtendedStatusResponse.getGradeId());
                String fuelGrade = PostecGradeMap.get(fuelGradeFormatted);
                if (fuelGrade == null)
                    fuelGrade = dispenserExtendedStatusResponse.getGradeId() + "";

                fuelTransaction.setFuelPointId(dispenserExtendedStatusResponse.getPumpNumber());
                fuelTransaction.setFuelGradeId(fuelGrade);
                fuelTransaction.setSeqNumber(dispenserExtendedStatusResponse.getSequenceNumber() + "");
                fuelTransaction.setInfoMask("223");//TODO: Fix this Info Mask
                fuelTransaction.setVolume(dispenserExtendedStatusResponse.getSaleQuantity());
                fuelTransaction.setMoneyDue(dispenserExtendedStatusResponse.getSaleAmount());
                fuelTransaction.setPrice(dispenserExtendedStatusResponse.getSalePrice());
            } else {
                return null;
            }
        } else if (response instanceof DownloadTransactionResponse) {
            DownloadTransactionResponse downloadTransactionResponse = (DownloadTransactionResponse) response;
            String fuelGradeFormatted = String.format("%02d", downloadTransactionResponse.getGradeId());
            String fuelGrade = PostecGradeMap.get(fuelGradeFormatted);
            if (fuelGrade == null)
                fuelGrade = downloadTransactionResponse.getGradeId() + "";

            fuelTransaction.setFuelPointId(downloadTransactionResponse.getDispenserNumber());
            fuelTransaction.setFuelGradeId(fuelGrade);
            fuelTransaction.setSeqNumber(downloadTransactionResponse.getSequenceNumber() + "");
            fuelTransaction.setInfoMask("223");//TODO: Fix this Info Mask
            fuelTransaction.setVolume(downloadTransactionResponse.getVolume());
            fuelTransaction.setMoneyDue(downloadTransactionResponse.getMoney());
            fuelTransaction.setPrice(downloadTransactionResponse.getUnitPrice());
        }
        return fuelTransaction;
    }

    private void downloadTransaction(int dispenserNumber) {
        DownloadTransactionRequest request = new DownloadTransactionRequest(posId, dispenserNumber, TransactionType.SaleDownload);
        String str = request.toString();

        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), String.class, this);
        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
    }

    private void dispenserExtendedMessage(int dispenserNumber) {
        DispenserExtendedStatusRequest request = new DispenserExtendedStatusRequest(posId, dispenserNumber);
        String str = request.toString();

        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), String.class, this);
        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, String.class, this);
    }

    @Override
    public <T> ForecourtControllerTransaction getTransactionData(T response) {
        DispenserStatusUpdate dispenserStatusUpdate = new DispenserStatusUpdate((String) response);

        ForecourtControllerTransaction forecourtControllerTransaction = new ForecourtControllerTransaction();
        forecourtControllerTransaction.setTime(System.currentTimeMillis());
        forecourtControllerTransaction.setFpId(dispenserStatusUpdate.getDispenserNumber());
        forecourtControllerTransaction.setFpLockId(dispenserStatusUpdate.getNodeNumber());
        forecourtControllerTransaction.setFcGradeId(String.valueOf(dispenserStatusUpdate.getGradeId()));
        forecourtControllerTransaction.setFuellingDataMonE(dispenserStatusUpdate.getSaleAmount());

        if (this.configuredFuelPoints != null) {
            for (FuelPoint fp : configuredFuelPoints) {
                if (Integer.parseInt(fp.getId()) == dispenserStatusUpdate.getDispenserNumber()) {
                    if (dispenserStatusUpdate.getPumpStatus().equals(PumpStatus.Hold)) {
                        forecourtControllerTransaction.setFpMainState("02H");
                    } else if (dispenserStatusUpdate.getPumpStatus().equals(PumpStatus.ToPay1) || dispenserStatusUpdate.getPumpStatus().equals(PumpStatus.ToPay2)) {
                        forecourtControllerTransaction.setFpMainState("01H");
                        if (configuredGradePrices != null) {
                            if (fuelPointAuthorised.contains(dispenserStatusUpdate.getDispenserNumber())){
                                //fuelPointProcessed.add(dispenserStatusUpdate.getDispenserNumber());
                                fuelPointAuthorised.remove(dispenserStatusUpdate.getDispenserNumber());
                                dispenserExtendedMessage(dispenserStatusUpdate.getDispenserNumber());
                            }
                        }
                    } else if(dispenserStatusUpdate.getPumpStatus().equals(PumpStatus.Zerofill) && fuelPointAuthorised.contains(dispenserStatusUpdate.getDispenserNumber())){
                        fuelPointAuthorised.remove(dispenserStatusUpdate.getDispenserNumber());
                    }
                    else {
                        forecourtControllerTransaction.setFpMainState("01H");
                    }
                    if (dispenserStatusUpdate.getPumpStatus().equals(PumpStatus.NozzleLifted)
                        || dispenserStatusUpdate.getPumpStatus().equals(PumpStatus.Alert1)
                        || dispenserStatusUpdate.getPumpStatus().equals(PumpStatus.Alert2)) {
                        forecourtControllerTransaction.setFpSubState2("NozzleLifted");
                    }else {
                        forecourtControllerTransaction.setFpSubState2("");
                    }
                    break;
                }
            }
        }
        return forecourtControllerTransaction;
    }

    @Override
    public void setFuelGradeNames(Map fuelGradeNames) {
        configurationGradeMap = (HashMap<String, String>) fuelGradeNames;
    }

    public HashMap<String, String> getPostecGradeMap() {
        if (PostecGradeMap == null) {
            PostecGradeMap = new HashMap<>();
            DomsGradeMap = new HashMap<>();
            if(configuredGradePrices != null) {
                for (GradePrice gp : configuredGradePrices.getGradePrices()) {
                    String postecGradeId = gp.getId();
                    String postecGradeName = gp.getName();
                    for (Map.Entry<String, String> entry : configurationGradeMap.entrySet()) {
                        String domsGradeId = entry.getKey();
                        String domsGradeName = entry.getValue();
                        if (domsGradeName.startsWith(postecGradeName)) {
                            PostecGradeMap.put(postecGradeId, domsGradeId);
                            DomsGradeMap.put(domsGradeId, postecGradeId);
                            break;
                        }
                    }
                }
            }
        }
        return PostecGradeMap;
    }
}