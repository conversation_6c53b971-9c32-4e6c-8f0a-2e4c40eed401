package com.smartfuel.service.escposprinter.textparser;


import java.util.Hashtable;

public class PrinterTextParserTag {
    private Hashtable<String, String> attributes = new Hashtable<>();

    private boolean isCloseTag = false;

    private int length = 0;

    private String tagName = "";

    public PrinterTextParserTag(String paramString) {
        paramString = paramString.trim();
        if (!paramString.substring(0, 1).equals("<") || !paramString.substring(paramString.length() - 1).equals(">"))
            return;
        this.length = paramString.length();
        int i = paramString.indexOf("<");
        int j = paramString.indexOf(">");
        int k = paramString.indexOf(" ");
        if (k != -1 && k < j) {
            this.tagName = paramString.substring(i + 1, k).toLowerCase();
            for (paramString = paramString.substring(k, j).trim(); paramString.contains("='"); paramString = paramString.substring(i + 1).trim()) {
                j = paramString.indexOf("='");
                i = paramString.indexOf("'", j + 2);
                String str1 = paramString.substring(0, j);
                String str2 = paramString.substring(j + 2, i);
                if (!str1.equals(""))
                    this.attributes.put(str1, str2);
            }
        } else {
            this.tagName = paramString.substring(i + 1, j).toLowerCase();
        }
        if (this.tagName.substring(0, 1).equals("/")) {
            this.tagName = this.tagName.substring(1);
            this.isCloseTag = true;
        }
    }

    public String getAttribute(String paramString) {
        return this.attributes.get(paramString);
    }

    public Hashtable<String, String> getAttributes() {
        return this.attributes;
    }

    public int getLength() {
        return this.length;
    }

    public String getTagName() {
        return this.tagName;
    }

    public boolean hasAttribute(String paramString) {
        return this.attributes.containsKey(paramString);
    }

    public boolean isCloseTag() {
        return this.isCloseTag;
    }
}

