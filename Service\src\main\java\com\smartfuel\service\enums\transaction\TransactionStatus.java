package com.smartfuel.service.enums.transaction;

public enum TransactionStatus {
    CARD_TERMINAL_INITIALISED(0),
    CARD_AUTHORIZING(1),
    CARD_AUTHORIZED(2),
    PUMP_AUTHORIZED(3),
    PUMP_FINISHED(4),
    CARD_CAPTURED(5),
    CARD_IN_PROGRESS(6),
    CARD_REVERSED(7),
    USER_CANCELLED(8),
    CARD_AUTH_REJECTED(12),
    CARD_CAPTURE_REJECTED(25),
    CARD_REVERSAL_REJECTED(26),
    TRX_ERROR(99);

    // declaring private variable for getting values
    private int status;

    // getter method
    public int getState()
    {
        return this.status;
    }
    private TransactionStatus(int status)
    {
        this.status = status;
    }
}
