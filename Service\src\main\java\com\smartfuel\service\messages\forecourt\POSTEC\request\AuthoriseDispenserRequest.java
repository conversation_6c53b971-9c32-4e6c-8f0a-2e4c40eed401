package com.smartfuel.service.messages.forecourt.POSTEC.request;

/**
 * To request the PCC to authorise a dispenser with an allocation limit and grade restriction
 * map, for use in Postpay or Prepay modes.
 * The are two Postpay mode limit types:
 * 1: Money limit
 * 2: Volume limit
 * There are three Prepay mode limit types as follows (supported in PCC4 software V6.28
 * release 3 Feb 2003 and later):
 * 5: Is a normal Postec PCC Prepay transaction where:
 *  The pump must be configured into Prepay mode on the PCC
 *  The PCC will create a preset transaction in the Memory buffer for payment through
 * the PoS Client to download. This requires both the PCC Sale and Memory buffers
 * to be empty.
 *  At the end of the pump sale, the PCC will subtract the preset amount from the
 * amount delivered and post the result as a sale buffer transaction (Refund or
 * overfill). If the difference is zero, the PCC does not post any transaction.
 * 3: The same as type 5 with the exception that the pump does not have to be pre-
 * configured into Prepay mode on the PCC.
 * 4: The PCC sends a Prepay authorization message to the dispensers (where available) to
 * limit the sale and lock the Preset keyboard display etc... as required by W&M in some
 * countries. However the PCC does not perform the 3 functions listed above for limit type 5.
 * PCC Software > V6.38
 * When the PCC is configured with a price units digits setting of 5 or 6 digits, the PCC will
 * receive and send prices data up to 6 digits in length.
 * When the PCC is configured with a Sales Volume of 7 or 8 digits, the PCC will receive and
 * send transaction volume data with 8 digits.
 */
public class AuthoriseDispenserRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] limitAmount = new char[8];
    private char[] limitType= new char[1];
    private char[] gradeMap = new char[4];

    public AuthoriseDispenserRequest(int nodeNumber, int dispenserNumber, long limitAmount, String gradeMap){
        super(nodeNumber,59,5,"Authorise_Dispenser_Ex");
        this.dispenserNumber = String.format("%02d",dispenserNumber).toCharArray();
        this.limitAmount = String.format("%06d",limitAmount).toCharArray();
        this.limitType = "1".toCharArray();
        this.gradeMap = gradeMap.toCharArray();
    }

    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.limitType);
        returnValue.append(" ");
        returnValue.append(this.limitAmount);
        returnValue.append(" ");
        returnValue.append(this.gradeMap);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
