package com.smartfuel.service.messages.forecourt.PTS2.response;

import java.util.ArrayList;
public class PumpNozzlesConfigurationResponse extends BaseResponse{
    public class Data{
        private ArrayList<PumpNozzle> PumpNozzles;

        public ArrayList<PumpNozzle> getPumpNozzles() {
            return PumpNozzles;
        }

        public void setPumpNozzles(ArrayList<PumpNozzle> pumpNozzles) {
            PumpNozzles = pumpNozzles;
        }
    }

    public class Packet{
        private int Id;
        private String Type;
        private Data Data;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public PumpNozzlesConfigurationResponse.Data getData() {
            return Data;
        }

        public void setData(PumpNozzlesConfigurationResponse.Data data) {
            Data = data;
        }
    }
    public class PumpNozzle{
        private int PumpId;
        private ArrayList<Integer> FuelGradeIds;
        private ArrayList<Integer> TankIds;

        public int getPumpId() {
            return PumpId;
        }

        public void setPumpId(int pumpId) {
            PumpId = pumpId;
        }

        public ArrayList<Integer> getFuelGradeIds() {
            return FuelGradeIds;
        }

        public void setFuelGradeIds(ArrayList<Integer> fuelGradeIds) {
            FuelGradeIds = fuelGradeIds;
        }

        public ArrayList<Integer> getTankIds() {
            return TankIds;
        }

        public void setTankIds(ArrayList<Integer> tankIds) {
            TankIds = tankIds;
        }
    }
    public ArrayList<Packet> Packets;
}

/*
* {
    "Protocol": "jsonPTS",
    "Packets": [
        {
            "Id": 1,
            "Type": "PumpNozzlesConfiguration",
            "Data": {
                "PumpNozzles": [
                    {
                        "PumpId": 1,
                        "FuelGradeIds": [
                            1,
                            0,
                            0,
                            0,
                            0,
                            0
                        ],
                        "TankIds": [
                            1,
                            0,
                            0,
                            0,
                            0,
                            0
                        ]
                    },
                    {
                        "PumpId": 2,
                        "FuelGradeIds": [
                            1,
                            0,
                            0,
                            0,
                            0,
                            0
                        ],
                        "TankIds": [
                            1,
                            0,
                            0,
                            0,
                            0,
                            0
                        ]
                    }
                ]
            }
        }
    ]
}*/