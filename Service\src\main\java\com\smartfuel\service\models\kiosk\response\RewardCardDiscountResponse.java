package com.smartfuel.service.models.kiosk.response;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.smartfuel.service.sqlite.models.RewardCardDiscount;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;

public class RewardCardDiscountResponse extends BaseResponse implements List<RewardCardDiscount> {

    private ArrayList<RewardCardDiscount> list;

    public RewardCardDiscountResponse(){
        list = new ArrayList<>();
    }

    @Override
    public int size() {
        return list.size();
    }

    @Override
    public boolean isEmpty() {
        return list.isEmpty();
    }

    @Override
    public boolean contains(@Nullable Object o) {
        return list.contains(o);
    }

    @NonNull
    @Override
    public Iterator<RewardCardDiscount> iterator() {
        return list.iterator();
    }

    @NonNull
    @Override
    public Object[] toArray() {
        return list.toArray();
    }

    @NonNull
    @Override
    public <T> T[] toArray(@NonNull T[] a) {
        return list.toArray(a);
    }

    @Override
    public boolean add(RewardCardDiscount rewardCardDiscount) {
        return list.add(rewardCardDiscount);
    }

    @Override
    public boolean remove(@Nullable Object o) {
        return list.remove(o);
    }

    @Override
    public boolean containsAll(@NonNull Collection<?> c) {
        return list.containsAll(c);
    }

    @Override
    public boolean addAll(@NonNull Collection<? extends RewardCardDiscount> c) {
        return list.addAll(c);
    }

    @Override
    public boolean addAll(int index, @NonNull Collection<? extends RewardCardDiscount> c) {
        return list.addAll(index, c);
    }

    @Override
    public boolean removeAll(@NonNull Collection<?> c) {
        return list.removeAll(c);
    }

    @Override
    public boolean retainAll(@NonNull Collection<?> c) {
        return list.retainAll(c);
    }

    @Override
    public void clear() {
        list.clear();
    }

    @Override
    public RewardCardDiscount get(int index) {
        return list.get(index);
    }

    @Override
    public RewardCardDiscount set(int index, RewardCardDiscount element) {
        return list.set(index, element);
    }

    @Override
    public void add(int index, RewardCardDiscount element) {
        list.add(index, element);
    }

    @Override
    public RewardCardDiscount remove(int index) {
        return list.remove(index);
    }

    @Override
    public int indexOf(@Nullable Object o) {
        return list.indexOf(o);
    }

    @Override
    public int lastIndexOf(@Nullable Object o) {
        return list.lastIndexOf(o);
    }

    @NonNull
    @Override
    public ListIterator<RewardCardDiscount> listIterator() {
        return list.listIterator();
    }

    @NonNull
    @Override
    public ListIterator<RewardCardDiscount> listIterator(int index) {
        return list.listIterator(index);
    }

    @NonNull
    @Override
    public List<RewardCardDiscount> subList(int fromIndex, int toIndex) {
        return list.subList(fromIndex, toIndex);
    }
}
