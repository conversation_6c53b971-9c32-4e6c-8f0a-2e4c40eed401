package com.smartfuel.service.messages.forecourt.PSS5000.response;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.JsonObject;
import com.smartfuel.service.enums.forecourt.PSS5000.Common;
import org.json.JSONException;

public class JPLResponse {
    public final JsonNode data;

    public final Common.MessageDirection mDirection;

    public final Common.MessageSource mSource;

    public final String name;

    public final boolean solicited;

    public final String subCode;

    public JPLResponse() {
        this.mDirection = Common.MessageDirection.OUT;
        this.mSource = Common.MessageSource.PSS5000;
        this.data = null;
        this.name = "";
        this.subCode = "";
        this.solicited = false;
    }

    public JPLResponse(JsonObject paramJsonObject) throws JSONException {
        String str;
        this.mDirection = Common.MessageDirection.OUT;
        this.mSource = Common.MessageSource.PSS5000;
        this.data = null;
        this.name = paramJsonObject.get("name").getAsString();
        if (paramJsonObject.has("subCode")) {
            str = paramJsonObject.get("subCode").getAsString();
        } else {
            str = "";
        }
        this.subCode = str;
        this.solicited = paramJsonObject.has("solicited");
    }

    public String ToString() {
        return ToString();
    }
}
