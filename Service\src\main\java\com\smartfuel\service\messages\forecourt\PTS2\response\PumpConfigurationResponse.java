package com.smartfuel.service.messages.forecourt.PTS2.response;

import java.util.ArrayList;

public class PumpConfigurationResponse extends BaseResponse {
    private ArrayList<Packet> Packets;

    public ArrayList<Packet> getPackets() {
        return Packets;
    }

    public void setPackets(ArrayList<Packet> Packets) {
        this.Packets = Packets;
    }

    public static class Pump {

        private int Id;

        private int Port;

        private int Address;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            this.Id = id;
        }

        public int getPort() {
            return Port;
        }

        public void setPort(int port) {
            this.Port = port;
        }

        public int getAddress() {
            return Address;
        }

        public void setAddress(int address) {
            this.Address = address;
        }
    }

    public static class Port {
        private int Id;
        private int Protocol;
        private int BaudRate;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            this.Id = id;
        }

        public int getProtocol() {
            return Protocol;
        }

        public void setProtocol(int protocol) {
            this.Protocol = protocol;
        }

        public int getBaudRate() {
            return BaudRate;
        }

        public void setBaudRate(int baudRate) {
            this.BaudRate = baudRate;
        }
    }

    public static class Packet {
        private int Id;
        private String Type;
        private Data Data;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            this.Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            this.Type = type;
        }

        public Data getData() {
            return Data;
        }

        public void setData(Data data) {
            this.Data = data;
        }
    }

    public static class Data {
        private ArrayList<Port> Ports;
        private ArrayList<Pump> Pumps;

        public ArrayList<Port> getPorts() {
            return Ports;
        }

        public void setPorts(ArrayList<Port> ports) {
            this.Ports = ports;
        }

        public ArrayList<Pump> getPumps() {
            return Pumps;
        }

        public void setPumps(ArrayList<Pump> pumps) {
            this.Pumps = pumps;
        }
    }
}


