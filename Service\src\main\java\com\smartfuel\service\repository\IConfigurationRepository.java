package com.smartfuel.service.repository;

import com.smartfuel.service.sqlite.models.Configuration;
import java.util.List;

public interface IConfigurationRepository {
    long[] addAllConfigurations(List<Configuration> paramList);

    long addConfiguration(Configuration paramConfiguration);

    Configuration getConfigurationByName(String paramString);

    List<Configuration> getConfigurationList();

    long updateConfiguration(Configuration paramConfiguration);
}