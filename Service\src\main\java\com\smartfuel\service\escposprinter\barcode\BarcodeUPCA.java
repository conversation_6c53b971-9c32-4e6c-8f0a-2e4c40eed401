package com.smartfuel.service.escposprinter.barcode;

import com.smartfuel.service.escposprinter.EscPosPrinterSize;
import com.smartfuel.service.escposprinter.exceptions.EscPosBarcodeException;

public class BarcodeUPCA extends BarcodeNumber {
    public BarcodeUPCA(EscPosPrinterSize paramEscPosPrinterSize, String paramString, float paramFloat1, float paramFloat2, int paramInt) throws EscPosBarcodeException {
        super(paramEscPosPrinterSize, 65, paramString, paramFloat1, paramFloat2, paramInt);
    }

    public int getCodeLength() {
        return 12;
    }
}
