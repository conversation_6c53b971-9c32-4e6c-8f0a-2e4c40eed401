package com.smartfuel.service.forecourtcontroller;

import com.smartfuel.service.messages.forecourt.PTS2.request.ConfigurationRequest;
import com.smartfuel.service.messages.forecourt.PTS2.request.PumpAuthorizeRequest;
import com.smartfuel.service.messages.forecourt.PTS2.request.PumpPriceChangeRequest;
import com.smartfuel.service.messages.forecourt.PTS2.request.PumpStatusRequest;
import com.smartfuel.service.messages.forecourt.PTS2.request.TransactionInformationRequest;
import com.smartfuel.service.messages.forecourt.PTS2.response.FuelGradesConfigurationResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpAuthorizeResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpConfigurationResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpNozzlesConfigurationResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpPriceChangeResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpStatusResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.TransactionInformationResponse;

import retrofit2.Call;
import retrofit2.http.*;

public interface IPTS2ApiService {
    @POST("jsonPTS/getPumpConfiguration")
    Call<PumpConfigurationResponse>
    getPumpConfiguration(@Body ConfigurationRequest pumpConfigRequest);

    @POST("jsonPTS/getFuelGradesConfiguration")
    Call<FuelGradesConfigurationResponse>
    getFuelGradesConfiguration(@Body ConfigurationRequest fuelGradesConfigurationRequest);

    @POST("jsonPTS/getPumpNozzlesConfiguration")
    Call<PumpNozzlesConfigurationResponse>
    getPumpNozzleConfiguration(@Body ConfigurationRequest pumpNozzleConfigurationRequest);

    @POST("jsonPTS/getPumpStatus")
    Call<PumpStatusResponse>
    getPumpStatus(@Body PumpStatusRequest pumpStatusRequest);

    @POST("jsonPTS/setPumpAuthorisation")
    Call<PumpAuthorizeResponse>
    setPumpAuth(@Body PumpAuthorizeRequest pumpAuthorizeRequest);

    @POST("jsonPTS/getPumpTransactionInformation")
    Call<TransactionInformationResponse>
    getTransactionInformation(@Body TransactionInformationRequest transactionInformationRequest);

    @POST("jsonPTS/setPumpPrices")
    Call<PumpPriceChangeResponse>
    setPumpPriceChange(@Body PumpPriceChangeRequest pumpPriceChangeRequest);
}
