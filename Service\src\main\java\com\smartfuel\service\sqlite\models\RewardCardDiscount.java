package com.smartfuel.service.sqlite.models;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

@Entity(indices = {@Index(value = {"internalId"})})
public class RewardCardDiscount {

    @NonNull
    @PrimaryKey(autoGenerate = true)
    private long internalId;

    private String id;

    private String fuelGrade;

    private long discount;

    private Long cap;

    private long totalDiscountApplied;

    private long transactionId;

    public long getInternalId() {
        return internalId;
    }

    public void setInternalId(long internalId) {
        this.internalId = internalId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFuelGrade() {
        return fuelGrade;
    }

    public void setFuelGrade(String fuelGrade) {
        this.fuelGrade = fuelGrade;
    }

    public long getDiscount() {
        return discount;
    }

    public void setDiscount(long discount) {
        this.discount = discount;
    }

    public Long getCap() {
        return cap;
    }

    public void setCap(Long cap) {
        this.cap = cap;
    }

    public long getTotalDiscountApplied() {
        return totalDiscountApplied;
    }

    public void setTotalDiscountApplied(long totalDiscountApplied) {
        this.totalDiscountApplied = totalDiscountApplied;
    }

    public long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(long transactionId) {
        this.transactionId = transactionId;
    }
}
