package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;

import org.json.JSONException;
public class ChangeOperationModeRequest {
    private final FcChangeOperationModeRequestData data;

    private final String name = "change_FcOperationModeNo_req";

    private final String subCode = "00H";

    public ChangeOperationModeRequest(Integer paramInteger) {
        this.data = new FcChangeOperationModeRequestData(paramInteger);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class FcChangeOperationModeRequestData {
        private final Integer FcOperationModeNo;

        FcChangeOperationModeRequestData(Integer param1Integer) {
            this.FcOperationModeNo = param1Integer;
        }
    }
}
