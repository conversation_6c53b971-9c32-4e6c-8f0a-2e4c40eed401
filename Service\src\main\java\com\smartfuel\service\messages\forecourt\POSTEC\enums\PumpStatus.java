package com.smartfuel.service.messages.forecourt.POSTEC.enums;

public enum PumpStatus {
    Ready(1),
    Hold(2),
    Prepay(3),
    Preset(4),
    InUse(5),
    Alert1(6),
    ChangeTimeout(7),
    LowVolume(8),
    Refund(9),
    ToPay1(10),
    Zerofill(11),
    <PERSON><PERSON>(12),
    <PERSON>(13),
    Off(14),
    <PERSON><PERSON>tch(15),
    TemporaryStop(16),
    TransactionComplete(17),
    PriceError(18),
    Wait(19),
    Overfill(20),
    PumpError(21),
    PowerDownError(22),
    Attend(23),
    NozzleLifted(24),
    Initialization(25),
    DCA(26),
    diag(27),
    DCAError(28),
    SetupError(29),
    Alert2(30),
    ToPay2(31),
    AttendantTagged(32),
    EFTDownload(33),
    EFTAuthPoSIdle(34),
    EFTAuthPoSCall(35),
    PoSAuthEFTIdle(36),
    PoSAuthEFTCall(37),
    Hold<PERSON>TOnly(38),
    AlertOPTOnly(39),
    Closed(40);


    private final int type;

    private PumpStatus(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static PumpStatus getPumpStatusById(int type) {
        for (PumpStatus ps : values()) {
            if (ps.getType() == type)
                return ps;
        }
        return null;
    }
}
