package com.smartfuel.service.models.transaction;

public class ReceiptTemplate {
    //TODO: Map the TransactionType int to a String value
    public static String getWhitecardReceipt(WhiteCardReceipt paramWhiteCardReceipt) {
        return "[C]<font size='normal'>" +
                paramWhiteCardReceipt.StoreName + "</font>\n[C]<font size='normal'>" +
                paramWhiteCardReceipt.StoreAddress1 + "</font>\n[C]<font size='normal'>" +
                paramWhiteCardReceipt.StoreCity + " " +
                paramWhiteCardReceipt.StoreState + " " +
                paramWhiteCardReceipt.StorePostalCode +
                "</font>\n[C]<font size='normal'>ABN: " + paramWhiteCardReceipt.StoreABN +
                "</font>\n[C]<font size='normal'>Kiosk Name: " + paramWhiteCardReceipt.KioskName +
                "</font>\n[L]\n[C]<b><font size='normal'>Tax Invoice #" + paramWhiteCardReceipt.TransactionNumberDetail +
                "</font></b>\n[L]\n[C]<font size='normal'>" + paramWhiteCardReceipt.TransactionDateTime +
                "</font>\n[L]\n[L]<b>FUEL[C]VOLUME[R]PRICE</b>\n[C]---------------------------------------------\n[L]"
                + paramWhiteCardReceipt.FuelGradeName + "[C]" + paramWhiteCardReceipt.FuelVolume + "[R]" + paramWhiteCardReceipt.TransactionTotalAmount
                + "\n[C]PUMP NO: " + paramWhiteCardReceipt.FuelPumpNumber + " @ "
                + paramWhiteCardReceipt.FuelPricePerLitre + "\n[L]\n[C]---------------------------------------------\n[L]Total including GST:[R]"
                + paramWhiteCardReceipt.TransactionTotalAmount + "\n[L]GST:[R]"
                + paramWhiteCardReceipt.TransactionGSTAmount
                + "\n[L]\n[C]=============================================\n[L]\n[L]<font size='normal'>TRANSACTION TYPE[R]"
                + paramWhiteCardReceipt.TransactionType
                + "</font>\n[L]<font size='normal'>TRANSACTION CURRENCY[R]"
                + paramWhiteCardReceipt.TransactionCurrency + "</font>\n[L]<font size='normal'>TRANSACTION NUMBER[R]"
                + paramWhiteCardReceipt.FuelTransactionNumber
                + "</font>\n[L]<font size='normal'>RESPONSE[R]"
                + paramWhiteCardReceipt.HostResponse
                + "(" + paramWhiteCardReceipt.HostResponseCode + ")"
                + "</font>\n[L]\n[C]=============================================\n[L]Account: "
                + paramWhiteCardReceipt.AccountName + "\n[L]User: " + paramWhiteCardReceipt.AccountUser + "\n[L]Registration: "
                + paramWhiteCardReceipt.VehicleRegistration + "\n[L]Odometer: " + paramWhiteCardReceipt.VehicleOdometer + "\n[L]E-Mail: "
                + paramWhiteCardReceipt.AccountEmailAddress + "\n[L]\n[C]---------------------------------------------\n[C]<font size='normal'>"
                + paramWhiteCardReceipt.InvoiceFooter + "</font>\n[L]\n[L]\n[L]\n[L]\n";
    }

    public static String getCardReceipt(CardReceipt cardReceipt) {
        return "[C]<font size='normal'>" +
                cardReceipt.StoreName + "</font>\n[C]<font size='normal'>" +
                cardReceipt.StoreAddress1 + "</font>\n[C]<font size='normal'>" +
                cardReceipt.StoreCity + " " +
                cardReceipt.StoreState + " " +
                cardReceipt.StorePostalCode +
                "</font>\n[C]<font size='normal'>ABN: " + cardReceipt.StoreABN +
                "</font>\n[C]<font size='normal'>Kiosk Name: " + cardReceipt.KioskName +
                "</font>\n[L]\n[C]<b><font size='normal'>" + (cardReceipt.TransactionType.equals("ACCOUNT") ? "Delivery Docket #" : "Tax Invoice #") + cardReceipt.TerminalTransactionId +
                "</font></b>\n[L]\n[C]<font size='normal'>" + cardReceipt.TransactionDateTime +
                "</font>\n[L]\n[L]<b>FUEL[C]VOLUME[R]PRICE</b>\n[C]---------------------------------------------\n[L]"
                + cardReceipt.FuelGradeName + "[C]" + cardReceipt.FuelVolume + "[R]" + cardReceipt.TransactionPartialAmount
                + (cardReceipt.RewardTotalDiscount.isEmpty() ? "" : ("\nReward[C]" + cardReceipt.RewardDiscountPerLiter + "[R]" + cardReceipt.RewardTotalDiscount))
                + "\n[C]PUMP NO: " + cardReceipt.FuelPumpNumber + " @ " + cardReceipt.FuelPricePerLitre
                + "\n[L]\n[C]---------------------------------------------\n[L]Total including GST:[R]"
                + cardReceipt.TransactionFinalAmount + "\n[L]GST:[R]"
                + cardReceipt.TransactionGSTAmount
                + "\n[L]\n[C]=============================================\n[L]\n"
                + (!cardReceipt.ApplicationLabel.equals("")? "[L]<font size='normal'>Card Name[R]" + cardReceipt.ApplicationLabel + "</font>\n" :"")
                + "[L]<font size='normal'>Card Signature[R]" + cardReceipt.CardSignature + "</font>\n"
                + "[L]<font size='normal'>Sequence Number[R]" + cardReceipt.PanSeqNo + "</font>\n"
                + (!cardReceipt.ApplicationId.equals("") ? "[L]<font size='normal'>AID[R]" + cardReceipt.ApplicationId + "</font>\n" : "")
                + "[L]<font size='normal'>ATC[R]" + cardReceipt.CardATC + "</font>\n"
                + (!cardReceipt.VehicleRegistration.equals("") ? "[L]<font size='normal'>Registration[R] " + cardReceipt.VehicleRegistration + "</font>\n" : "")
                + (!cardReceipt.VehicleOdometer.equals("") ? "[L]<font size='normal'>Odometer[R] " + cardReceipt.VehicleOdometer + "</font>\n" : "")
                + "\n[L]\n[C]=============================================\n[L]\n"
                + "[L]<font size='normal'>TRANSACTION TYPE[R]" + cardReceipt.TransactionType + "</font>\n"
                + "[L]<font size='normal'>TRANSACTION CURRENCY[R]" + cardReceipt.TransactionCurrency + "</font>\n"
                + "[L]<font size='normal'>TRANSACTION NUMBER[R]" + cardReceipt.FuelTransactionNumber + "</font>\n"
                + "[L]<font size='normal'>AUTHORIZATION ID[R]" + cardReceipt.AuthorisationId + "</font>\n"
                + "[L]<font size='normal'>RESPONSE[R]"
                + cardReceipt.HostResponse
                + "(" + cardReceipt.HostResponseCode + ")"
                + "</font>"
                + "\n[L]\n[C]=============================================\n[C]<font size='normal'>"
                + cardReceipt.InvoiceFooter + "</font>\n[L]\n[L]\n[L]\n[L]\n";
    }
}

