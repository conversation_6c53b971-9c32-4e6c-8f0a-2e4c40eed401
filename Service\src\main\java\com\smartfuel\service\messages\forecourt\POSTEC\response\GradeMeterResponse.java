package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public class GradeMeterResponse extends BaseResponse {
    private int gradeNumber;
    private long shiftMoney;
    private long shiftLitres;
    private long accumulatedMoney;
    private long accumulatedVolume;

    public GradeMeterResponse(String response) throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.gradeNumber = Integer.parseInt(super.responseData[5]);
        this.shiftMoney = Long.parseLong(super.responseData[6]);
        this.shiftLitres = Long.parseLong(super.responseData[7]);
        this.accumulatedMoney = Long.parseLong(super.responseData[8]);
        this.accumulatedVolume = Long.parseLong(super.responseData[9]);


    }

    public int getGradeNumber() {
        return gradeNumber;
    }

    public long getShiftMoney() {
        return shiftMoney;
    }

    public long getShiftLitres() {
        return shiftLitres;
    }

    public long getAccumulatedMoney() {
        return accumulatedMoney;
    }

    public long getAccumulatedVolume() {
        return accumulatedVolume;
    }

}
