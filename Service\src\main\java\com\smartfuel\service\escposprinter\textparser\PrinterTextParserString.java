package com.smartfuel.service.escposprinter.textparser;


import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.escposprinter.EscPosCharsetEncoding;
import com.smartfuel.service.escposprinter.EscPosPrinter;
import com.smartfuel.service.escposprinter.EscPosPrinterCommands;
import com.smartfuel.service.escposprinter.exceptions.EscPosEncodingException;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;

public class PrinterTextParserString implements IPrinterTextParserElement {
    private EscPosPrinter printer;

    private String text;

    private byte[] textBold;

    private byte[] textColor;

    private byte[] textDoubleStrike;

    private byte[] textReverseColor;

    private byte[] textSize;

    private byte[] textUnderline;

    public PrinterTextParserString(PrinterTextParserColumn paramPrinterTextParserColumn, String paramString, byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2, byte[] paramArrayOfbyte3, byte[] paramArrayOfbyte4, byte[] paramArrayOfbyte5, byte[] paramArrayOfbyte6) {
        this.printer = paramPrinterTextParserColumn.getLine().getTextParser().getPrinter();
        this.text = paramString;
        this.textSize = paramArrayOfbyte1;
        this.textColor = paramArrayOfbyte2;
        this.textReverseColor = paramArrayOfbyte3;
        this.textBold = paramArrayOfbyte4;
        this.textUnderline = paramArrayOfbyte5;
        this.textDoubleStrike = paramArrayOfbyte6;
    }

    public int length() throws EscPosEncodingException {
        EscPosCharsetEncoding escPosCharsetEncoding = this.printer.getEncoding();
        byte b = 1;
        if (Arrays.equals(this.textSize, EscPosPrinterCommands.TEXT_SIZE_DOUBLE_WIDTH) || Arrays.equals(this.textSize, EscPosPrinterCommands.TEXT_SIZE_BIG)) {
            b = 2;
        } else if (Arrays.equals(this.textSize, EscPosPrinterCommands.TEXT_SIZE_BIG_2)) {
            b = 3;
        } else if (Arrays.equals(this.textSize, EscPosPrinterCommands.TEXT_SIZE_BIG_3)) {
            b = 4;
        } else if (Arrays.equals(this.textSize, EscPosPrinterCommands.TEXT_SIZE_BIG_4)) {
            b = 5;
        } else if (Arrays.equals(this.textSize, EscPosPrinterCommands.TEXT_SIZE_BIG_5)) {
            b = 6;
        } else if (Arrays.equals(this.textSize, EscPosPrinterCommands.TEXT_SIZE_BIG_6)) {
            b = 7;
        }
        if (escPosCharsetEncoding != null)
            try {
                int i = (this.text.getBytes(escPosCharsetEncoding.getName())).length;
                return i * b;
            } catch (UnsupportedEncodingException unsupportedEncodingException) {
                throw new EscPosEncodingException(unsupportedEncodingException.getMessage());
            }
        return this.text.length() * b;
    }

    public PrinterTextParserString print(EscPosPrinterCommands paramEscPosPrinterCommands) throws EscPosEncodingException {
        paramEscPosPrinterCommands.printText(this.text, this.textSize, this.textColor, this.textReverseColor, this.textBold, this.textUnderline, this.textDoubleStrike);
        return this;
    }
}

