package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;


/*
01 007 0123 04 2000 04 M Return_Transaction
 01 = client node number
 007 = request number
 0123 = sequence number
 04 = number of parameters
 2000 = socket success code (2) + PCC success code (000)
 04 = Dispenser number
 M = Transaction type
 */
public class ReturnTransactionResponse extends BaseResponse {
    private int dispenserNumber;
    private char transactionType;

    public ReturnTransactionResponse(String response) throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
        this.transactionType = super.responseData[6].toCharArray()[0];
    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }

    public char getTransactionType() {
        return transactionType;
    }
}
