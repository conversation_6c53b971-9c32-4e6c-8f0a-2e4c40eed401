package com.smartfuel.service.escposprinter.barcode;

import com.smartfuel.service.escposprinter.EscPosPrinterSize;
import com.smartfuel.service.escposprinter.exceptions.EscPosBarcodeException;

public class BarcodeUPCE extends Barcode {
    public BarcodeUPCE(EscPosPrinterSize paramEscPosPrinterSize, String paramString, float paramFloat1, float paramFloat2, int paramInt) throws EscPosBarcodeException {
        super(paramEscPosPrinterSize, 66, paramString, paramFloat1, paramFloat2, paramInt);
        checkCode();
    }

    private void checkCode() throws EscPosBarcodeException {
        int i = getCodeLength();
        if (this.code.length() >= i)
            try {
                this.code = this.code.substring(0, i);
                for (byte b = 0; b < i; b++)
                    Integer.parseInt(this.code.substring(b, b + 1), 10);
                return;
            } catch (NumberFormatException numberFormatException) {
                numberFormatException.printStackTrace();
                throw new EscPosBarcodeException("Invalid barcode number");
            }
        throw new EscPosBarcodeException("Code is too short for the barcode type.");
    }

    public int getCodeLength() {
        return 6;
    }

    public int getColsCount() {
        return getCodeLength() * 7 + 16;
    }
}

