package com.smartfuel.service.messages.forecourt.PTS2.response;

public class BaseResponse {
private String Protocol;
private String Message;
private boolean Error;
    //private boolean success;

    //public String getMessage() {
    //    return this.message;
    //}

    //public boolean isSuccess() {
    //    return this.success;
    //}

    //public void setMessage(String paramString) {
    //    this.message = paramString;
    //}

    //public void setSuccess(boolean paramBoolean) {
    //    this.success = paramBoolean;
    //}

    public String getProtocol() {
        return Protocol;
    }

    public void setProtocol(String protocol) {
        this.Protocol = protocol;
    }

    public String getMessage() {
        return Message;
    }

    public void setMessage(String message) {
        Message = message;
    }

    public boolean isError() {
        return Error;
    }

    public void setError(boolean error) {
        Error = error;
    }
}