package com.smartfuel.service.sqlite.models;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Index;

@Entity(indices = {@Index(value = {"fpId", "fpLockId", "FcGradeId"})},
        primaryKeys = {"fpId", "fpLockId", "FcGradeId"}  )

public class ForecourtControllerTransaction {
    @NonNull
    private String FcGradeId;

    @NonNull
    private int fpId;

    @NonNull
    private int fpLockId;

    private String fpMainState;

    private String fpSubState2;

    private String fpSubState4;

    private long fuellingDataMonE;

    private long fuellingDataVolE;

    private long time;

    public String getFcGradeId() {
        return this.FcGradeId;
    }

    public int getFpId() {
        return this.fpId;
    }

    public int getFpLockId() {
        return this.fpLockId;
    }

    public String getFpMainState() {
        return this.fpMainState;
    }

    public String getFpSubState2() {
        return this.fpSubState2;
    }

    public String getFpSubState4() {
        return this.fpSubState4;
    }

    public long getFuellingDataMonE() {
        return this.fuellingDataMonE;
    }

    public long getFuellingDataVolE() {
        return this.fuellingDataVolE;
    }

    public long getTime() {
        return this.time;
    }

    public void setFcGradeId(String paramString) {
        this.FcGradeId = paramString;
    }

    public void setFpId(int paramInt) {
        this.fpId = paramInt;
    }

    public void setFpLockId(int paramInt) {
        this.fpLockId = paramInt;
    }

    public void setFpMainState(String paramString) {
        this.fpMainState = paramString;
    }

    public void setFpSubState2(String paramString) {
        this.fpSubState2 = paramString;
    }

    public void setFpSubState4(String paramString) {
        this.fpSubState4 = paramString;
    }

    public void setFuellingDataMonE(long paramLong) {
        this.fuellingDataMonE = paramLong;
    }

    public void setFuellingDataVolE(long paramLong) {
        this.fuellingDataVolE = paramLong;
    }

    public void setTime(long paramLong) {
        this.time = paramLong;
    }
}

