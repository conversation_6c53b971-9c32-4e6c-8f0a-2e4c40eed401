package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;


/*
01 089 0123 04 2000 02 431985 Unlock_Transaction_Data
 01 = client node number
 089 = request number
 0123 = sequence number
 04 = number of parameters
 2000 = socket success code (2) + PCC success code (000)
 02 = Dispenser number
 431985 = Sequence number
 */
public class UnlockTransactionResponse extends BaseResponse{
private int dispenserNumber;
private String transactionNumber;
    public UnlockTransactionResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
        this.transactionNumber = super.responseData[6];
    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }
}
