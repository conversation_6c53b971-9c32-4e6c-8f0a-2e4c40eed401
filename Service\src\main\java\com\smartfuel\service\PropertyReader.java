package com.smartfuel.service;

import android.content.Context;

import com.smartfuel.service.logger.Log;
import com.smartfuel.service.property.DefaultProperties;
import com.smartfuel.service.property.IProperties;

import java.io.IOException;
import java.io.InputStream;

public class PropertyReader {
    private Context context;

    private IProperties properties;

    private static IPropertiesHandle handle = new IPropertiesHandle() {
        @Override
        public IProperties getHandle() {
            return new DefaultProperties();
        }

        @Override
        public IProperties loadProperties(Context context, IProperties properties, String paramString) throws IOException {
            if(!paramString.equals("")) {
                InputStream inputStream = context.getAssets().open(paramString);
                properties.load(inputStream);
            }
            return properties;
        }
    };

    public static void setPropertiesHandle(IPropertiesHandle handle){
        PropertyReader.handle = handle;
    }

    public PropertyReader(Context paramContext) {
        this(paramContext, handle.getHandle());
    }

    public PropertyReader(Context paramContext, IProperties properties) {
        this.context = paramContext;
        this.properties = properties;
    }

    public IProperties getMyProperties(String paramString) throws IOException {
        try {
            return handle.loadProperties(this.context, this.properties, paramString);
        } catch (Exception exception) {
            throw exception;
        }
    }

    public IProperties getMyProperties() {
        try {
            return getMyProperties("");
        } catch (IOException e) {
            Log.e("getMyProperties", e);
            throw new RuntimeException(e);
        }
    }

    public interface IPropertiesHandle{
        IProperties getHandle();
        IProperties loadProperties(Context context, IProperties properties, String paramString) throws IOException;
    }
}
