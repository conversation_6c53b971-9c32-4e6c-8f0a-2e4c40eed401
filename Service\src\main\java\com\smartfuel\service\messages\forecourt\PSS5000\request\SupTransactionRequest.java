package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;
import java.util.ArrayList;
import org.json.JSONException;
public class SupTransactionRequest {
    private final SupTransactionRequestData data;

    private final String name = "FpSupTrans_req";

    private final String subCode = "00H";

    public SupTransactionRequest(String terminalId, String fuelpointId, String transSeqNo, ArrayList<String> infoItemsList) {
        this.data = new SupTransactionRequestData(terminalId, fuelpointId, transSeqNo, infoItemsList);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class SupTransactionRequestData {
        private final String FpId;

        private final String PosId;

        private final ArrayList<String> TransParId;

        private final String TransSeqNo;

        SupTransactionRequestData(String terminalId, String fuelpointId, String transSeqNo, ArrayList<String> infoItemsList) {
            this.FpId = fuelpointId;
            this.PosId = terminalId;
            this.TransSeqNo = transSeqNo;
            this.TransParId = infoItemsList;
        }
    }
}
