package com.smartfuel.service.messages.forecourt.POSTEC.request;

public class HoldDispenserRequest extends BaseRequest {
    /**
     * To request the PCC to de-authorise a dispenser if pre-authorised.
     * To request the PCC to Temporary Stop a dispenser which has a sale in progress.
     * To request the PCC to terminate the sale for a dispenser in the Temp Stop state.
     *  Dispenser Number 98: Used to Close the forecourt
     *  Dispenser Number 99: Used to put the forecourt into Emergency Stop state
     */
    private char[] dispenserNumber = new char[2];
    public HoldDispenserRequest(int nodeNumber, int dispenserNumber)
    {
        super(nodeNumber,4,2,"Hold_Dispenser");
        this.dispenserNumber = String.format("%02d",dispenserNumber).toCharArray();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
