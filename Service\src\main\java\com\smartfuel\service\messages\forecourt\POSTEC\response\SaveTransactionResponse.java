package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public class SaveTransactionResponse extends BaseResponse {
    private int dispenserNumber;

    public SaveTransactionResponse(String response) throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }
}
