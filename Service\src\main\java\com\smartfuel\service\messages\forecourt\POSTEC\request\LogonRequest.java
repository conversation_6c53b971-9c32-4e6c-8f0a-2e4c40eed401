package com.smartfuel.service.messages.forecourt.POSTEC.request;


/**
 * To activate the PCC for a client with the specified node address.
 * The Smartfuel OPT uses:
 *  node 01 by default - unless configured otherwise
 *  client option 0 (LAN Mode)
 * This opens a communications path between the client and the PCC through which forecourt
 * application requests can be served.
 * The PCC system profile is returned including :
 *  Software version number
 *  Software release date (Used for pre-release software version control)
 *  Program name (Country / Region name)
 *  Hardware platform version number
 */
public class LogonRequest extends BaseRequest {
    private char[] appVersion = new char[7];
    private char[] clientVersion = new char[5];
    private char[] clientOption = new char[1];

    public LogonRequest(int nodeNumber, String appVersion, String clientVersion) {
        super(nodeNumber, 1, 4, "Login_Client_Node");

        this.appVersion = appVersion.toCharArray();
        this.clientVersion = clientVersion.toCharArray();
        this.clientOption = "0".toCharArray();
    }

    @Override
    public String toString() {
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.appVersion);
        returnValue.append(" ");
        returnValue.append(this.clientVersion);
        returnValue.append(" ");
        returnValue.append(this.clientOption);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
