package com.smartfuel.service.sqlite.models;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import com.smartfuel.service.enums.transaction.TransactionStatus;
import com.smartfuel.service.models.transaction.IM30TransactionBroadcast;

import java.util.Date;

@Entity(indices = {@Index(value = {"id"})})
public class Transaction {

    @NonNull
    @PrimaryKey(autoGenerate = true)
    private long id;

    private String AccountType;

    private String aid;

    private String authorisationId;

    private long authorisedAmount;

    private String cardSignature;

    private String cvm = "NONE";

    private boolean exported = false;

    private long finalAmount;

    private long fuelTransactionId;

    private String hostResponse;
    private boolean kioskPublished = false;

    private String panSeqNumber;

    private String processor = "IX";

    private int receiptPrintCount;

    private String responseCode;

    private int rrn;

    private int stan;

    private int status;

    private int terminalId;

    private int terminalTransactionId;

    private long time;

    private String track2;

    private int trxCount;

    private int type;

    private int currency;

    private String cardExpiryDate;

    private String applicationLabel;

    private int atc;

    private String tid;

    public String getAccountType() {
        return this.AccountType;
    }

    public String getAid() {
        return this.aid;
    }

    public String getAuthorisationId() {
        return this.authorisationId;
    }

    public long getAuthorisedAmount() {
        return this.authorisedAmount;
    }

    public String getCardSignature() {
        return this.cardSignature;
    }

    public String getCvm() {
        return this.cvm;
    }

    public long getFinalAmount() {
        return this.finalAmount;
    }

    public long getFuelTransactionId() {
        return this.fuelTransactionId;
    }

    public String getHostResponse() {
        return this.hostResponse;
    }

    public long getId() {
        return this.id;
    }

    public String getPanSeqNumber() {
        return this.panSeqNumber;
    }

    public String getProcessor() {
        return this.processor;
    }

    public int getReceiptPrintCount() {
        return this.receiptPrintCount;
    }

    public String getResponseCode() {
        return this.responseCode;
    }

    public int getRrn() {
        return this.rrn;
    }

    public int getStan() {
        return this.stan;
    }

    public int getStatus() {
        return this.status;
    }

    public int getTerminalId() {
        return this.terminalId;
    }

    public int getTerminalTransactionId() {
        return this.terminalTransactionId;
    }

    public long getTime() {
        return this.time;
    }

    public String getTrack2() {
        return this.track2;
    }

    public int getTrxCount() {
        return this.trxCount;
    }

    public int getType() {
        return this.type;
    }

    public int getCurrency() {
        return currency;
    }

    public String getCardExpiryDate() {
        return cardExpiryDate;
    }

    public String getApplicationLabel() {
        return applicationLabel;
    }

    public int getAtc() {
        return atc;
    }

    public String getTid() {
        return tid;
    }

    public boolean isExported() {
        return this.exported;
    }

    public boolean isKioskPublished() {
        return this.kioskPublished;
    }

    public void setAccountType(String paramString) {
        this.AccountType = paramString;
    }

    public void setAid(String paramString) {
        this.aid = paramString;
    }

    public void setAuthorisationId(String paramString) {
        this.authorisationId = paramString;
    }

    public void setAuthorisedAmount(long paramLong) {
        this.authorisedAmount = paramLong;
    }

    public void setCardSignature(String paramString) {
        this.cardSignature = paramString;
    }

    public void setCvm(String paramString) {
        this.cvm = paramString;
    }

    public void setExported(boolean paramBoolean) {
        this.exported = paramBoolean;
    }

    public void setFinalAmount(long paramLong) {
        this.finalAmount = paramLong;
    }

    public void setFuelTransactionId(long paramLong) {
        this.fuelTransactionId = paramLong;
    }

    public void setHostResponse(String paramString) {
        this.hostResponse = paramString;
    }

    public void setId(long paramLong) {
        this.id = paramLong;
    }

    public void setKioskPublished(boolean paramBoolean) {
        this.kioskPublished = paramBoolean;
    }

    public void setPanSeqNumber(String paramString) {
        this.panSeqNumber = paramString;
    }

    public void setProcessor(String paramString) {
        this.processor = paramString;
    }

    public void setReceiptPrintCount(int paramInt) {
        this.receiptPrintCount = paramInt;
    }

    public void setResponseCode(String paramString) {
        this.responseCode = paramString;
    }

    public void setRrn(int paramInt) {
        this.rrn = paramInt;
    }

    public void setStan(int paramInt) {
        this.stan = paramInt;
    }

    public void setStatus(int paramInt) {
        this.status = paramInt;
    }

    public void setTerminalId(int paramInt) {
        this.terminalId = paramInt;
    }

    public void setTerminalTransactionId(int paramInt) {
        this.terminalTransactionId = paramInt;
    }

    public void setTime(long paramLong) {
        this.time = paramLong;
    }

    public void setTrack2(String paramString) {
        this.track2 = paramString;
    }

    public void setTrxCount(int paramInt) {
        this.trxCount = paramInt;
    }

    public void setType(int paramInt) {
        this.type = paramInt;
    }

    public void setCurrency(int currency) {
        this.currency = currency;
    }

    public void setCardExpiryDate(String cardExpiryDate) {
        this.cardExpiryDate = cardExpiryDate;
    }

    public void setApplicationLabel(String applicationLabel) {
        this.applicationLabel = applicationLabel;
    }

    public void setAtc(int atc) {
        this.atc = atc;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public void importIm30Transaction(IM30TransactionBroadcast broadcast){
        setType(broadcast.getAuthorizationType());
        setAccountType(broadcast.getCardType());
        setCardSignature(broadcast.getCardSignature());
        setPanSeqNumber(broadcast.getCardSequenceNumber());
        setCvm(broadcast.getCardVerificationMethod() == 1 ? "PIN" : "NONE");
        setCardExpiryDate(broadcast.getCardExpiryDate());
        setAid(broadcast.getApplicationId());
        setApplicationLabel(broadcast.getApplicationLabel());
        setAtc(broadcast.getApplicationTransactionCounter());
        setAuthorisationId(broadcast.getAuthorizationId());
        setTid(broadcast.getIm30TerminalId());
        setStan(broadcast.getStan().isEmpty() ? 0 : Integer.parseInt(broadcast.getStan()));
        if(broadcast.getGatewayResponse() != null && !broadcast.getGatewayResponse().isEmpty())
            setHostResponse(broadcast.getGatewayResponse());
        if(broadcast.getGatewayResponseCode() != null && !broadcast.getGatewayResponseCode().isEmpty())
            setResponseCode(broadcast.getGatewayResponseCode());
    }
}
