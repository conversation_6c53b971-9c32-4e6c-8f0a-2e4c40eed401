package com.smartfuel.service.logic;

import com.smartfuel.service.logger.Log;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smartfuel.service.IKioskApiService;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.enums.transaction.ProcessType;
import com.smartfuel.service.enums.transaction.TransactionStatus;
import com.smartfuel.service.models.kiosk.request.RewardCardValidRequest;
import com.smartfuel.service.models.kiosk.request.TransactionAuditRequest;
import com.smartfuel.service.models.kiosk.request.TransactionRequest;
import com.smartfuel.service.models.kiosk.request.TransactionValidateRequest;
import com.smartfuel.service.models.kiosk.response.AccountCardCapabilities;
import com.smartfuel.service.models.kiosk.response.TransactionResponse;
import com.smartfuel.service.models.kiosk.response.TransactionValidateResponse;
import com.smartfuel.service.models.kiosk.response.UserResponse;
import com.smartfuel.service.models.transaction.IM30Transaction;
import com.smartfuel.service.models.transaction.WhiteCardUser;
import com.smartfuel.service.repository.IConfigurationRepository;
import com.smartfuel.service.repository.IRewardCardDiscountRepository;
import com.smartfuel.service.repository.ITransactionRepository;
import com.smartfuel.service.sqlite.models.FuelTransaction;
import com.smartfuel.service.sqlite.models.RewardCardDiscount;
import com.smartfuel.service.sqlite.models.Transaction;
import com.smartfuel.service.transaction.CardTransactionService;
import com.smartfuel.service.utils.ApiException;
import com.smartfuel.service.utils.ApiServiceHelper;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

public class TransactionLogic {

    private IKioskApiService kioskApiService;
    private IConfigurationRepository configurationRepository;
    private ITransactionRepository transactionRepository;
    private IRewardCardDiscountRepository rewardCardDiscountRepository;

    private CardTransactionService cardTransactionService;
    private IServiceEvents serviceEvents;
    private ForecourtLogic<?> forecourtLogic;
    protected int posId;

    private ObjectMapper objectMapper;

    private static TransactionLogic instance = null;

    public static TransactionLogic getInstance(IKioskApiService kioskApiService,
                                               IConfigurationRepository configurationRepository,
                                               ITransactionRepository transactionRepository,
                                               IRewardCardDiscountRepository rewardCardDiscountRepository,
                                               ForecourtLogic<?> forecourtLogic, ObjectMapper objectMapper, int posId) {
        if (instance == null) {
            instance = new TransactionLogic();
        }

        instance.kioskApiService = kioskApiService;
        instance.configurationRepository = configurationRepository;
        instance.transactionRepository = transactionRepository;
        instance.rewardCardDiscountRepository = rewardCardDiscountRepository;

        instance.forecourtLogic = forecourtLogic;
        instance.posId = posId;
        instance.objectMapper = objectMapper;
        return instance;
    }

    private TransactionLogic() {
    }

    public void setCardTransactionService(CardTransactionService cardTransactionService) {
        this.cardTransactionService = cardTransactionService;
    }

    public void registerClient(IServiceEvents activity) {
        this.serviceEvents = activity;
    }

    private long saveTransaction(Transaction trx, FuelTransaction fTrx){
        long trxId = transactionRepository.saveTransaction(trx, fTrx);
        if(trx.getId() <= 0)
            trx.setId(trxId);
        TransactionAuditRequest.send(kioskApiService, trx, fTrx, configurationRepository);
        return trxId;
    }

    public void initialiseTransaction(String pumpNo, long transactionAmount) throws InterruptedException {
        // save the initial transaction to local SQL and get an  OPTTerminalTransactionId
        // send the transaction data to the IM30 Terminal - read the first message response from IM30
        // pass OPTTerminalTransactionId in event data
        //Object usbService = this.getSystemService(Context.USB_SERVICE);
        //Context serviceContext = this.getBaseContext();

        Thread workerThread = new Thread(() -> {
            Transaction trx = new Transaction();
            FuelTransaction fTrx = new FuelTransaction();

            int currencyId = Integer.parseInt(configurationRepository.getConfigurationByName("store_currency").getValue());

            fTrx.setPumpNumber(Integer.parseInt(pumpNo));
            fTrx.setMoney(transactionAmount);
            trx.setAuthorisedAmount(transactionAmount);
            trx.setCurrency(currencyId);
            trx.setStatus(TransactionStatus.CARD_TERMINAL_INITIALISED.getState());
            trx.setTerminalId(posId);
            trx.setTime(System.currentTimeMillis());

            long trxId = saveTransaction(trx, fTrx);

            // transaction is now saved in local SQL store - we can construct message for Card Terminal
            String kioskId = configurationRepository.getConfigurationByName("kiosk_id").getValue();
            IM30Transaction im30Transaction = new IM30Transaction(kioskId, String.valueOf(trxId), transactionAmount, currencyId, ProcessType.PAYMENT);

            try {

                cardTransactionService.Initialise(im30Transaction);
            } catch (ConnectionException e) {
                Log.e("initialiseTransaction", e);
                throw new RuntimeException(e);
            }

        });
        workerThread.start();
        workerThread.join();
    }

    public void initialiseReceipt() throws InterruptedException {
        Thread workerThread = new Thread(() -> {
            // transaction is now saved in local SQL store - we can construct message for Card Terminal
            String kioskId = configurationRepository.getConfigurationByName("kiosk_id").getValue();
            IM30Transaction im30Transaction = new IM30Transaction(kioskId, null, 0, 0, ProcessType.RECEIPT);

            try {
                cardTransactionService.Initialise(im30Transaction);
            } catch (ConnectionException e) {
                Log.e("initialiseReceipt", e);
                throw new RuntimeException(e);
            }

        });
        workerThread.start();
        workerThread.join();
    }

    public void initialiseMagnetic() throws InterruptedException {
        Thread workerThread = new Thread(() -> {
            // transaction is now saved in local SQL store - we can construct message for Card Terminal
            String kioskId = configurationRepository.getConfigurationByName("kiosk_id").getValue();
            IM30Transaction im30Transaction = new IM30Transaction(kioskId, kioskId, 0, 0, ProcessType.MAGREAD);

            try {
                cardTransactionService.Initialise(im30Transaction);
            } catch (ConnectionException e) {
                Log.e("initialiseReceipt", e);
                throw new RuntimeException(e);
            }

        });
        workerThread.start();
        workerThread.join();
    }

    public void cancelCardTransaction(String pumpNo) throws InterruptedException {
        Thread workerThread = new Thread(() -> {
            int terminalId = Integer.parseInt(configurationRepository.getConfigurationByName("pos_id").getValue());
            Transaction currentTrx = transactionRepository.getCurrentTransaction(terminalId, TransactionStatus.CARD_TERMINAL_INITIALISED.getState(), Integer.parseInt(pumpNo));
            if (currentTrx != null)
                cancelTransaction(String.valueOf(currentTrx.getId()));
        });
        workerThread.start();
        workerThread.join();
    }

    public void cancelCardTransaction() throws InterruptedException {
        Thread workerThread = new Thread(() -> {
            cancelTransaction(null);
        });
        workerThread.start();
        workerThread.join();
    }

    private void cancelTransaction(String externalReference) {
        String kioskId = configurationRepository.getConfigurationByName("kiosk_id").getValue();
        int currencyId = Integer.parseInt(configurationRepository.getConfigurationByName("store_currency").getValue());
        IM30Transaction im30Transaction = new IM30Transaction(kioskId, externalReference, 0, currencyId, ProcessType.CANCEL);
        try {
            cardTransactionService.Cancel(im30Transaction);
        } catch (ConnectionException e) {
            Log.e("cancelTransaction", e);
            throw new RuntimeException(e);
        }
    }

    public void AuthoriseWhiteCardTransaction(String whitecardUserData, String pumpNumber,
                                              long transactionAmount) {
        try {

            //PreparePrepaidTransactionRequest preparePrepaidTransactionRequest = null;
            //record the transaction in local SQLite DB
            int transactionStatus = authoriseWhiteCardTransaction((WhiteCardUser) this.objectMapper.getFactory().createParser(whitecardUserData).readValueAs(WhiteCardUser.class), pumpNumber, transactionAmount);
            //update the transaction status
            switch (transactionStatus) {
                case 12: // Whitecard Auth Declined
                    this.serviceEvents.systemError("Card Auth Declined", null);
                    break;
                case 2: // Whitecard Auth Approved
                    forecourtLogic.PreparePrepaidTransaction(String.valueOf(posId), pumpNumber, "0", String.valueOf(transactionAmount));
                    break;
            }
            //preparePrepaidTransactionRequest.systemError("Out of Service", null);
        } catch (Exception exception) {
            Log.e("Whitecard Auth Failed", exception);
            this.serviceEvents.systemError("Whitecard Auth Failed", exception);
        }
    }

    private int authoriseWhiteCardTransaction(WhiteCardUser paramWhiteCardUser, String pumpNumber, long authorisationAmount) throws InterruptedException {
        AtomicInteger atomicInteger = new AtomicInteger(99);
        try {
            Thread thread = new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Transaction trx = new Transaction();
                        FuelTransaction fTrx = new FuelTransaction();

                        fTrx.setPumpNumber(Integer.parseInt(pumpNumber));
                        fTrx.setMoney(authorisationAmount);
                        fTrx.setVehicleOdometer(paramWhiteCardUser.getVehicleOdometer());
                        fTrx.setVehicleRegistration(paramWhiteCardUser.getVehicleRegistration().toUpperCase());
                        fTrx.setWhitecardUserId(paramWhiteCardUser.getUser());

                        trx.setAuthorisedAmount(authorisationAmount);
                        trx.setCvm("QRCODE");
                        trx.setAccountType("WHITECARD");
                        trx.setProcessor("WC");
                        trx.setStatus(TransactionStatus.CARD_AUTHORIZING.getState());
                        trx.setTerminalId(Integer.parseInt(configurationRepository.getConfigurationByName("pos_id").getValue()));
                        trx.setTime(System.currentTimeMillis());

                        long trxId = saveTransaction(trx, fTrx);
                        trx.setId(trxId);


                        try {
                            UserResponse user = ApiServiceHelper.executeAPI(kioskApiService.getKioskUserDetail(paramWhiteCardUser.getUser()));

                            fTrx.setWhitecardAccountId(user.account.id);
                            fTrx.setWhitecardAccountEmail(user.account.email);
                            fTrx.setWhitecardAccountName(user.account.name);
                            fTrx.setWhitecardUserName(user.userName);
                            trx.setCardSignature(user.userName);
                            //Record the fact that we have a transaction - status = 1
                            if (user.active) {
                                atomicInteger.set(TransactionStatus.CARD_AUTHORIZED.getState());
                            } else {
                                atomicInteger.set(TransactionStatus.CARD_AUTH_REJECTED.getState());
                            }

                        } catch (ApiException e) {
                            atomicInteger.set(TransactionStatus.TRX_ERROR.getState());
                        }
                        trx.setStatus(atomicInteger.get()); //update the transaction status - status = atomicInteger value (2,12,99)
                        saveTransaction(trx, fTrx);

                    } catch (Exception e) {
                        //rather than throw the exception - navigate the user to a transaction failed UI
                        Log.e("Transaction Failed", e);
                        serviceEvents.systemError("Transaction Failed", e);
                        //throw new RuntimeException(e);
                    }
                }
            });
            thread.start();
            thread.join();
            return atomicInteger.get(); // return transaction status to manage process flow/UI
        } catch (Exception exception) {
            throw exception;
        }
    }

    public void validateTransaction(long externalReference, String im30Reference, AccountCardCapabilities capabilities){
        Thread thread = new Thread(() -> {
            try {
                Transaction trx = transactionRepository.getTransactionById(externalReference);
                FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                fTrx.setVehicleRegistration(capabilities.getInputVehicleRegistration());
                fTrx.setVehicleOdometer(capabilities.getInputOdometer());
                saveTransaction(trx, fTrx);
                TransactionValidateResponse response =  TransactionValidateRequest.send(kioskApiService, trx.getTid(), im30Reference, trx.getStan(), capabilities.getInputVehicleRegistration(), capabilities.getInputOdometer());
                if(response.isValidated()) {
                    Runnable afterConnection = () -> {
                        try {
                            forecourtLogic.PreparePrepaidTransaction(String.valueOf(posId), String.valueOf(fTrx.getPumpNumber()), "0", String.valueOf(trx.getAuthorisedAmount()));
                        }catch (Exception e){
                            Log.e("validateTransaction", e);
                        }
                    };
                    afterConnection.run();
                }
                else {
                    trx.setStatus(TransactionStatus.CARD_AUTH_REJECTED.getState()); // AUTH_REJECTED
                    trx.setHostResponse("Validation Failed");
                    trx.setResponseCode(response.getHostResponse());
                    saveTransaction(trx, fTrx);
                    serviceEvents.systemError("Validate Transaction Failed", new Exception("Validation Error code: " + response.getHostResponse()));
                    String kioskId = configurationRepository.getConfigurationByName("kiosk_id").getValue();
                    cardTransactionService.Reversal(kioskId, trx);
                }
            } catch (Exception e) {
                Log.e("Validate Transaction Failed", "Validate Transaction Failed", e);
                serviceEvents.systemError("Validate Transaction Failed", e);
            }
        });
        thread.start();
    }

    public void validateRewardCard(long externalReference, String track2){
        Thread thread = new Thread(() -> {
            try {
                List<RewardCardDiscount> discounts =  RewardCardValidRequest.send(kioskApiService, track2);
                if(discounts.size() > 0) {
                    for (RewardCardDiscount discount : discounts) {
                        discount.setTransactionId(externalReference);
                    }
                    rewardCardDiscountRepository.addAll(discounts);
                    serviceEvents.rewardCardApplied(discounts);
                }
                else {
                    serviceEvents.rewardCardDeclined("Reward card not validated");
                }
            } catch (ApiException e) {
                Log.w("validateRewardCard", e);
                serviceEvents.rewardCardDeclined(e.getMessage());
            } catch (Exception e) {
                Log.e("validateRewardCard", "ValidateRewardCard Failed", e);
                serviceEvents.rewardCardDeclined(e.getMessage());
            }
        });
        thread.start();
    }

    public void checkOrphanTransactions() throws IOException, ConnectionException {
        synchronized (this) {
            List<Transaction> trxs = this.transactionRepository.getOrphanTransactions();
            if (trxs.size() > 0) {
                String kioskId = configurationRepository.getConfigurationByName("kiosk_id").getValue();
                for (Transaction trx : trxs) {
                    if (trx.getStatus() == TransactionStatus.CARD_IN_PROGRESS.getState()
                            && trx.getFinalAmount() != 0) {
                        cardTransactionService.Capture(kioskId, trx);
                    } else {
                        cardTransactionService.Reversal(kioskId, trx);
                    }
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e) {
                        throw new ConnectionException(e.getMessage());
                    }
                }
            }
        }
    }

    private int getIntegerFromString(String input) {
        char[] caracters = input.toCharArray();
        for (int i = 0; i < caracters.length; i++) {
            if (!Character.isDigit(caracters[i])) {
                int asciiValue = (int)caracters[i];
                int lastDigitAscii = asciiValue % 10;
                caracters[i] = (char)(lastDigitAscii + '0');
            }
        }
        return Integer.parseInt(new String(caracters));
    }

    public void publishKioskTransaction(Transaction transaction) throws ApiException {
        FuelTransaction fuelTransaction = this.transactionRepository.getFuelTransactionById(transaction.getFuelTransactionId());
        List<RewardCardDiscount> rewardCardDiscounts = this.rewardCardDiscountRepository.GetByTransaction(transaction.getId());
        TransactionRequest transactionRequest = new TransactionRequest();
        transactionRequest.setRewardCardTransactions(rewardCardDiscounts);
        Date date = new Date(transaction.getTime());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

        transactionRequest.setTerminalTransactionId(transaction.getTerminalTransactionId());
        transactionRequest.setTransactionDateTime(simpleDateFormat.format(date));
        transactionRequest.setType(transaction.getType());
        transactionRequest.setStatus(transaction.getStatus());
        transactionRequest.setAuthorisedAmount((float) (transaction.getAuthorisedAmount() / 100.0D));
        transactionRequest.setFinalAmount((float) (transaction.getFinalAmount() / 100.0D));
        transactionRequest.setGstAmount((float) (transaction.getFinalAmount() / 100.0D / 11.00D));
        transactionRequest.setCurrency(transaction.getCurrency());
        transactionRequest.setApplicationId(transaction.getAid());
        transactionRequest.setApplicationLabel(transaction.getApplicationLabel());
        transactionRequest.setCardSignature(transaction.getCardSignature());
        transactionRequest.setCardExpiryDate(transaction.getCardExpiryDate());
        transactionRequest.setCardATC(transaction.getAtc());
        transactionRequest.setVehicleRegistration(fuelTransaction.getVehicleRegistration());
        transactionRequest.setVehicleOdometer(fuelTransaction.getVehicleOdometer());
        transactionRequest.setAccountType(transaction.getAccountType());
        transactionRequest.setTid(transaction.getTid());
        transactionRequest.setStan(transaction.getStan());
        transactionRequest.setResponseCode(transaction.getResponseCode());
        transactionRequest.setHostResponse(transaction.getHostResponse());
        if (transaction.getAuthorisationId()!= null && !transaction.getAuthorisationId().trim().equals("")) {
            //remove any characters which are not 0-9
            transactionRequest.setAuthorizationID(getIntegerFromString(transaction.getAuthorisationId()));
        }

        if (transaction.getPanSeqNumber() != null && !transaction.getPanSeqNumber().isEmpty())
            transactionRequest.setPanSeqNo(Integer.parseInt(transaction.getPanSeqNumber()));
        transactionRequest.setCvm(transaction.getCvm());
        transactionRequest.setProcessor(transaction.getProcessor());
        transactionRequest.setTerminalId(transaction.getTerminalId());
        transactionRequest.setStoreId(this.configurationRepository.getConfigurationByName("store_id").getValue());
        transactionRequest.setKioskId(this.configurationRepository.getConfigurationByName("kiosk_id").getValue());
        transactionRequest.setAccountId(fuelTransaction.getWhitecardAccountId());
        transactionRequest.setUserId(fuelTransaction.getWhitecardUserId());

        TransactionRequest.FuelTransaction fuelTransaction2 = new TransactionRequest.FuelTransaction();
        transactionRequest.fuelTransaction = fuelTransaction2;
        if (fuelTransaction.getTransSeqNumber() != null && !fuelTransaction.getTransSeqNumber().isEmpty())
            transactionRequest.fuelTransaction.transactionSeqNumber = Integer.parseInt(fuelTransaction.getTransSeqNumber());
        transactionRequest.fuelTransaction.pumpNumber = fuelTransaction.getPumpNumber();
        transactionRequest.fuelTransaction.fuelAmount = fuelTransaction.getMoney();
        transactionRequest.fuelTransaction.fuelVolume = fuelTransaction.getVolume();
        transactionRequest.fuelTransaction.fuelPrice = fuelTransaction.getPrice();
        transactionRequest.fuelTransaction.fuelGradeId = fuelTransaction.getGradeId();

        StringBuilder fuelGradeName = new StringBuilder();
        transactionRequest.fuelTransaction.fuelGradeName = configurationRepository.getConfigurationByName(fuelGradeName.append("FG_").append(String.format("%1$" + 2 + "s", fuelTransaction.getGradeId()).replace(' ', '0')).toString()).getValue();
        try {
            TransactionResponse response = ApiServiceHelper.executeAPI(this.kioskApiService.addKioskTransaction(transactionRequest));
            if (response.isSuccess()) {
                transaction.setKioskPublished(true);
                this.transactionRepository.saveTransaction(transaction, fuelTransaction);
            }
        } catch (ApiException e) {
            StringBuilder stringBuilder1 = new StringBuilder();
            Log.e("KioskAPI Exception", stringBuilder1.append("HTTP CODE").append(e.getResponseCode()).append(e.getMessage()).toString(), e);
            throw e;
        }
    }
}
