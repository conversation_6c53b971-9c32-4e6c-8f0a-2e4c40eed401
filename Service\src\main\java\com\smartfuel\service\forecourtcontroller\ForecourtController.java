package com.smartfuel.service.forecourtcontroller;

import com.google.gson.GsonBuilder;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.FuelTransaction;
import com.smartfuel.service.models.forecourt.GradePrices;
import com.smartfuel.service.sqlite.models.ForecourtControllerTransaction;
import org.json.JSONException;

import java.io.IOException;
import java.util.ArrayList;

public abstract class ForecourtController implements IForecourtController {
    public static interface Callback<T> {
        <T>void ClearFuelTransactionBuffer(T response);

        <T> void FuellingStatusUpdate(T response) throws InterruptedException;

        <T> void  FuelTransactionComplete(T response);

        void Error(Throwable exception);

        void FuelPointAuthorized(String fuelpointId) throws InterruptedException;

        <T> void FuelPointPrePaidSetupComplete(T data) throws InterruptedException;

        void SetupComplete() throws InterruptedException;

        void ForecourtControllerReady() throws InterruptedException;

        void ForecourtControllerLoginComplete();

        void LogonRequired();
        void ShowHomeScreen();
        void ShowStandbyScreen();

        String[] getValidGrades(int posId, int fp);
    }

    public <T>void AuthoriseFuellingPoint(String posId, T response){};
    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }
    public ArrayList<FuelPoint> getConfiguredFuelPoints(){
        return new ArrayList<FuelPoint>();
    }
    public GradePrices getConfiguredGradePrices() {
        return null;
    }
    public String parseResponse(String response, String lookupfield) throws IOException {
        return null;

    }
    public<T> ForecourtControllerTransaction getTransactionData(T response){
        return null;
    }
    //public <T> void CompleteFuelTransaction(T data){}

    //public void GetFuelPriceData(){}

    public <T> FuelTransaction getFuelTransactionDetails(T data){return  null;}

    //public abstract void CancelFuelPointAuthorisation(String posId, String fuelpointId);

    public abstract void CompleteFuelTransaction(String posId, String fuelpointId, String trnSeqNumber, long volume, long fueldispensedAmount);

    public void FetchFuelTransaction(String posId, int fuelpointId, String trnSeqNumber) {}
    //public void Heartbeat(){};
    public void ChangeFuelPrices(String priceSetId, ArrayList<String> priceGroupIds, ArrayList<String> fuelGradeIds, ArrayList<String> priceGroups, String priceActivationDateTime) {}
    @Override
    public void CancelFuelPointAuthorisation(String posId, String fuelpointId) {

    }

    @Override
    public void GetFuelPriceData() {

    }

    @Override
    public void Heartbeat() {

    }

    @Override
    public void Logon(String posId, String ApplicationId, String clientVersion) throws InterruptedException {

    }

    @Override
    public void PreparePrepaidTransaction(String posId, String fuelpointId, String gradeId, String prepaidAmount) {

    }
}
