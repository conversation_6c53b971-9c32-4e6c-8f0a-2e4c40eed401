package com.smartfuel.service.messages.forecourt.PSS5000.request;

public class ClearSupTransactionRequest {
    private final FcClearSupTransactionRequestData data;

    private final String name = "clear_FpSupTrans_req";

    private final String subCode = "00H";

    public ClearSupTransactionRequest(String posId, String fuelPointId, String trnSeqNumber, String fuelVolume, String transactionAmount) {
        this.data = new FcClearSupTransactionRequestData(fuelPointId, posId, trnSeqNumber, fuelVolume, transactionAmount );
    }

    public class FcClearSupTransactionRequestData {
        private final String FpId;

        private final String Money;

        private final String PosId;

        private final String TransSeqNo;

        private final String Vol;


        FcClearSupTransactionRequestData(String fuelPointId, String terminalId, String trnSeqNumber, String fuelVolume, String transactionAmount) {
            this.FpId = fuelPointId;
            this.PosId = terminalId;
            this.TransSeqNo = trnSeqNumber;
            this.Vol = fuelVolume.substring(Math.max(0, fuelVolume.length() - 6));;
            this.Money = transactionAmount.substring(Math.max(0, transactionAmount.length() - 6));;
        }
    }
}
