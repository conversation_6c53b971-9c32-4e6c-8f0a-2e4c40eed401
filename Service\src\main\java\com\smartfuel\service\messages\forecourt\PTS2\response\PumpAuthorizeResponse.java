package com.smartfuel.service.messages.forecourt.PTS2.response;

import java.util.ArrayList;

public class PumpAuthorizeResponse extends BaseResponse{

    public ArrayList<Packet> Packets;

    public class Packet{
        private int Id;
        private String Type;
        private Data Data;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public Data getData() {
            return Data;
        }

        public void setData(PumpAuthorizeResponse.Data data) {
            Data = data;
        }
    }
    public class Data{
        private int Pump;
        private int Transaction;

        public int getPump() {
            return Pump;
        }

        public void setPump(int pump) {
            Pump = pump;
        }

        public int getTransaction() {
            return Transaction;
        }

        public void setTransaction(int transaction) {
            Transaction = transaction;
        }
    }
}
