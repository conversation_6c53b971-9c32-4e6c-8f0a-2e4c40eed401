package com.smartfuel.service.sqlite.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import com.smartfuel.service.sqlite.models.Configuration;

@Dao
public interface IConfiguration {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long[] addAllConfigurations(List<Configuration> paramList);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long addConfiguration(Configuration paramConfiguration);

    @Query("Select * from Configuration where name = :configurationName")
    Configuration getConfigurationByName(String configurationName);

    @Query("Select * from Configuration")
    List<Configuration> getConfigurationList();

    @Update
    int updateConfiguration(Configuration paramConfiguration);
}