package com.smartfuel.service.diagnostics;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class BaseDiagnostic {

    private final String TAG = this.getClass().getSimpleName();

    private List<DiagnosticModel> list;
    private AtomicBoolean working;
    private AtomicInteger attempts;
    private DiagnosticListener listener;

    public BaseDiagnostic(){
        list = new ArrayList<>();
        working = new AtomicBoolean(false);
        attempts = new AtomicInteger(0);
    }

    public void setListener(DiagnosticListener listener){
        this.listener = listener;
    }

    private boolean checkStatus() {
        boolean check = check();
        DiagnosticModel model = new DiagnosticModel(check);
        list.add(model);
        return check;
    }

    public void resolve(){
        if(working.get())
            return;

        listener.onStopDetected(TAG);

        attempts.set(0);
        new Thread(() -> {
            working.set(true);
            while (!checkStatus()){
                synchronized (this) {
                    listener.onReestablishing(TAG, attempts.incrementAndGet());
                    reestablish();
                    try {
                        Thread.sleep(getDelay());
                    } catch (InterruptedException e) {
                    }
                }
            }
            working.set(false);
            listener.onRestored(TAG);
        }).start();
    }

    protected abstract int getDelay();

    protected abstract boolean check();

    protected abstract void reestablish();

    @Override
    public String toString(){
        String ret = "";
        if(list.size() > 0){
            boolean first = true;
            for (DiagnosticModel model : list) {
                ret += "<tr>\n" +
                        (first ? "   <td rowspan=\"" + list.size() + "\"><strong>" + TAG + "</strong></td>\n" : "") +
                        "   <td>" + model.getDate() + "</td>\n" +
                        "   <td>" + model.getState() + "</td>\n" +
                        "</tr>";
                first = false;
            }
            list.clear();
        }
        return ret;
    }
}
