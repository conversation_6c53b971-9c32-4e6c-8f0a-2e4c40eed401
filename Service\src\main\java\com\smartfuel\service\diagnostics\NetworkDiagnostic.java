package com.smartfuel.service.diagnostics;

import android.accounts.NetworkErrorException;
import android.content.Context;

import com.smartfuel.service.utils.Utils;

public class NetworkDiagnostic extends BaseDiagnostic {

    private Context context;

    public NetworkDiagnostic(Context context){
        this.context = context;
    }

    @Override
    protected int getDelay() {
        return 2000;
    }

    @Override
    protected boolean check() {
        try {
            Utils.isNetworkAvailable(context);
        } catch (NetworkErrorException e) {
            return false;
        }
        return true;
    }

    @Override
    protected void reestablish() {
        //Nothing to do
    }
}
