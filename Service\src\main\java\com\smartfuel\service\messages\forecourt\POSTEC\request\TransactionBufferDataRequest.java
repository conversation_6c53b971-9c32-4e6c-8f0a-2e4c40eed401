package com.smartfuel.service.messages.forecourt.POSTEC.request;


public class TransactionBufferDataRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] bufferType = new char[1];
    private char[] byteIndex = new char[2];
    private char[] byteCount = new char[2];



    public TransactionBufferDataRequest(int nodeNumber, int dispenserNumber, int sequenceNumber, char bufferType, int
                                        byteIndex, int byteCount){
        super(nodeNumber,81,5,"Req_Trans_Buff_Data");
        this.dispenserNumber = String.format("%02d", dispenserNumber).toCharArray();
        this.bufferType = String.format("%1c", bufferType).toCharArray();
        this.byteIndex = String.format("%02d", byteIndex).toCharArray();
        this.byteCount = String.format("%02d", byteCount).toCharArray();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.bufferType);
        returnValue.append(" ");
        returnValue.append(this.byteIndex);
        returnValue.append(" ");
        returnValue.append(this.byteCount);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
