package com.smartfuel.service.posmaster;

public abstract class POSMasterAPIResponse<T> {
    private POSMasterAPIResponse() {}

    public static final class Success<T> extends POSMasterAPIResponse<T> {
        public T data;

        public Success(T data) {
            this.data = data;
        }
    }

    public static final class Error<T> extends POSMasterAPIResponse<T> {
        public Exception exception;

        public Error(Exception exception) {
            this.exception = exception;
        }
    }
}