package com.smartfuel.service.repository;

import com.smartfuel.service.sqlite.models.Configuration;
import com.smartfuel.service.sqlite.dao.IConfiguration;
import java.util.List;
import javax.inject.Inject;

public class ConfigurationDataSource implements IConfigurationRepository {
    private IConfiguration IConfigurationDao;

    @Inject
    public ConfigurationDataSource(IConfiguration configurationDao) {
        this.IConfigurationDao = configurationDao;
    }

    public long[] addAllConfigurations(List<Configuration> paramList) {
        return this.IConfigurationDao.addAllConfigurations(paramList);
    }

    public long addConfiguration(Configuration paramConfiguration) {
        return this.IConfigurationDao.addConfiguration(paramConfiguration);
    }

    public Configuration getConfigurationByName(String paramString) {
        return this.IConfigurationDao.getConfigurationByName(paramString);
    }

    public List<Configuration> getConfigurationList() {
        return this.IConfigurationDao.getConfigurationList();
    }

    public long updateConfiguration(Configuration paramConfiguration) {
        return this.IConfigurationDao.updateConfiguration(paramConfiguration);
    }
}
