package com.smartfuel.service.models.kiosk.response;

public class TransactionValidateResponse extends BaseResponse {
    private String authCode;
    private String hostResponse;
    private String merchantId;
    private long stan;
    private String rrn;

    public boolean isValidated(){
        return hostResponse.equals("00");
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getHostResponse() {
        return hostResponse;
    }

    public void setHostResponse(String hostResponse) {
        this.hostResponse = hostResponse;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public long getStan() {
        return stan;
    }

    public void setStan(long stan) {
        this.stan = stan;
    }

    public String getRrn() {
        return rrn;
    }

    public void setRrn(String rrn) {
        this.rrn = rrn;
    }
}
