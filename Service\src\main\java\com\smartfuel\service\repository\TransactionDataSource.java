package com.smartfuel.service.repository;

import com.smartfuel.service.enums.transaction.TransactionStatus;
import com.smartfuel.service.models.posmaster.OPTExport;
import com.smartfuel.service.models.posmaster.Order;
import com.smartfuel.service.models.posmaster.OrderItem;
import com.smartfuel.service.sqlite.models.FuelTransaction;
import com.smartfuel.service.sqlite.dao.IFuelTransaction;
import com.smartfuel.service.sqlite.models.Transaction;
import com.smartfuel.service.sqlite.dao.ITransaction;
import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

public class TransactionDataSource implements ITransactionRepository, IFuelTransactionRepository {
    private final IFuelTransaction IFuelTransaction;

    private final ITransaction ITransaction;

    @Inject
    public TransactionDataSource(ITransaction paramITransactionDao, IFuelTransaction paramIFuelTransactionDao) {
        this.ITransaction = paramITransactionDao;
        this.IFuelTransaction = paramIFuelTransactionDao;
    }

    public Transaction getCurrentTransaction(int paramInt1, int paramInt2, int paramInt3) {
        return this.ITransaction.getCurrentTransaction(paramInt1, paramInt2, paramInt3);
    }

    public FuelTransaction getFuelTransactionById(long paramLong) {
        return this.IFuelTransaction.getFuelTransactionById(paramLong);
    }

    public Transaction getTransactionById(long paramLong) {
        return this.ITransaction.getTransactionById(paramLong);
    }

    public List<Transaction> getOrphanTransactions() {
        long timeOut = System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(30);
        return this.ITransaction.getTransactionsByStatusAndTimeOut(timeOut,
                TransactionStatus.CARD_TERMINAL_INITIALISED.getState(),
                TransactionStatus.CARD_AUTHORIZING.getState(),
                TransactionStatus.CARD_AUTHORIZED.getState(),
                TransactionStatus.PUMP_AUTHORIZED.getState(),
                TransactionStatus.PUMP_FINISHED.getState(),
                TransactionStatus.CARD_IN_PROGRESS.getState(),
                TransactionStatus.USER_CANCELLED.getState(),
                TransactionStatus.CARD_AUTH_REJECTED.getState(),
                TransactionStatus.TRX_ERROR.getState());
    }

    public Transaction getTransactionByFuelPointStatusAndTimeout(int fuelPointId){
        long timeOut = System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(1);
        return this.ITransaction.getTransactionByFuelPointStatusAndTimeout(fuelPointId, timeOut,
                //TransactionStatus.CARD_TERMINAL_INITIALISED.getState(),
                TransactionStatus.CARD_AUTHORIZING.getState(),
                TransactionStatus.CARD_AUTHORIZED.getState(),
                TransactionStatus.PUMP_AUTHORIZED.getState()
                //TransactionStatus.PUMP_FINISHED.getState(),
                //TransactionStatus.CARD_IN_PROGRESS.getState()
        );
    }

    public OPTExport getPosMasterOrders(String posId) {
        OPTExport exportData = new OPTExport();
        List<Order> orders = this.ITransaction.getPosMasterOrders(posId);
        List<String> ordersIds = new ArrayList<String>();
        for (Order o : orders)
        {
            ordersIds.add(o.getTerminalTransactionId());
        }

        List<OrderItem> orderItems = this.ITransaction.getPosMasterOrderItems(ordersIds);
        List<com.smartfuel.service.models.posmaster.FuelTransaction> fuelTransactions = ITransaction.getPosMasterFuelTransactions(ordersIds);
        List<com.smartfuel.service.models.posmaster.Transaction> transactions = ITransaction.getPosMasterTransactions(ordersIds);

        exportData.setOrderIds(ordersIds);
        exportData.setOrders(orders);
        exportData.setOrderItems(orderItems);
        exportData.setFuelTransactions(fuelTransactions);
        exportData.setTransactions(transactions);

        return exportData;
    }

    public List<Transaction> getTransactionsForPublish() {
        return this.ITransaction.getPublishableTransactions();
    }

    public List<WhiteCardReceipt> getWhitecardUserReceipt(String paramString) {
        return this.ITransaction.getWhiteCardReceiptData(paramString);
    }

    @Override
    public List<CardReceipt> getCardUserReceipt(List<Integer> cardSignature) {
        return this.ITransaction.getCardReceiptData(cardSignature);
    }
    @Override
    public List<Integer> getCardReceiptTransactions(String cardSignature){
        long timeFrom = System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(120);
        return this.ITransaction.getCardReceiptTransactions(cardSignature, timeFrom,
                TransactionStatus.CARD_AUTHORIZED.getState(),
                TransactionStatus.CARD_AUTH_REJECTED.getState(),
                TransactionStatus.CARD_CAPTURED.getState(),
                TransactionStatus.CARD_CAPTURE_REJECTED.getState(),
                TransactionStatus.CARD_REVERSED.getState(),
                TransactionStatus.CARD_REVERSAL_REJECTED.getState(),
                TransactionStatus.PUMP_AUTHORIZED.getState(),
                TransactionStatus.PUMP_FINISHED.getState(),
                TransactionStatus.USER_CANCELLED.getState(),
                TransactionStatus.TRX_ERROR.getState());
    }

    public long saveTransaction(Transaction paramTransaction, FuelTransaction paramFuelTransaction) {
        paramTransaction.setFuelTransactionId(this.IFuelTransaction.upsertFuelTransaction(paramFuelTransaction));
        return this.ITransaction.upsertTransaction(paramTransaction);
    }

    @Override
    public void setTransactionExported(List<String> orderIds) {
        this.ITransaction.updateTransactionExportStatus(orderIds);
    }
}