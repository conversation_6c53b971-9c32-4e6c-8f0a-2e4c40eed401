package com.smartfuel.service.messages.forecourt.POSTEC.request;


public class HoseGradeTankAssociationRequest extends BaseRequest {
    private final char[] dispenserNumber;
    private final char[] hoseNumber;
    public HoseGradeTankAssociationRequest(int nodeNumber, int dispenserNumber, int hoseNumber ) {
        super(nodeNumber, 22,3, "Req_Hose_Grade_Tank_Ass");
        this.dispenserNumber = String.format("%02d",dispenserNumber).toCharArray();
        this.hoseNumber = String.format("%01d",hoseNumber).toCharArray();
    }

    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.hoseNumber);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}

