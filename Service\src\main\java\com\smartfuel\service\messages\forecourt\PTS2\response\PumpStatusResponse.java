package com.smartfuel.service.messages.forecourt.PTS2.response;

import java.util.ArrayList;

public class PumpStatusResponse extends BaseResponse{
    public ArrayList<Packet> Packets;
    public class Data{
        private int Pump;
        private int Nozzle;
        private int NozzleUp;
        private int Transaction;
        private int FuelGradeId;

        public boolean isNozzleLifted(){
            return Nozzle != 0 && NozzleUp != 0;
        }

        public int getPump() {
            return Pump;
        }

        public void setPump(int pump) {
            Pump = pump;
        }

        public int getNozzle() {
            return Nozzle;
        }

        public void setNozzle(int nozzle) {
            Nozzle = nozzle;
        }

        public int getNozzleUp() {
            return NozzleUp;
        }

        public void setNozzleUp(int nozzleUp) {
            NozzleUp = nozzleUp;
        }

        public int getTransaction() {
            return Transaction;
        }

        public void setTransaction(int transaction) {
            Transaction = transaction;
        }

        public int getFuelGradeId() {
            return FuelGradeId;
        }

        public void setFuelGradeId(int fuelGradeId) {
            FuelGradeId = fuelGradeId;
        }
    }
    public class Packet{
        private int Id;
        private String Type;
        private Data Data;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public Data getData() {
            return Data;
        }

        public void setData(Data data) {
            Data = data;
        }
    }
}
