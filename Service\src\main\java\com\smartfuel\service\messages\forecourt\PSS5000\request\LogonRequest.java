package com.smartfuel.service.messages.forecourt.PSS5000.request;

import com.google.gson.GsonBuilder;
import org.json.JSONException;

import java.util.ArrayList;

public class LogonRequest {
    private final FCLogonRequestData data;

    private final String name = "FcLogon_req";

    private final String subCode = "00H";

    public LogonRequest(String paramString1, String paramString2, String paramString3, ArrayList<Integer> paramArrayList) {
        this.data = new FCLogonRequestData(paramString1, paramString2, paramString3, paramArrayList);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class FCLogonRequestData {
        private final String CountryCode;

        private final String FcAccessCode;

        private final String PosVersionId;

        private final ArrayList<Integer> UnsolicitedApcList;

        FCLogonRequestData(String param1String1, String param1String2, String param1String3, ArrayList<Integer> param1ArrayList) {
            this.FcAccessCode = param1String1;
            this.CountryCode = param1String2;
            this.PosVersionId = param1String3;
            this.UnsolicitedApcList = param1ArrayList;
        }
    }
}
