package com.smartfuel.service.sqlite.models;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Index;

@Entity (indices = {@Index("name")}
        , primaryKeys = {"name"})
public class Configuration {
    @NonNull
    private String name;

    private String value;

    public Configuration(String name, String value) {
        setName(name);
        setValue(value);
    }

    public String getName() {
        return this.name;
    }

    public String getValue() {
        return this.value;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
