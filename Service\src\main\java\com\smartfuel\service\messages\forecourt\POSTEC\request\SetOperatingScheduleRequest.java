package com.smartfuel.service.messages.forecourt.POSTEC.request;


public class SetOperatingScheduleRequest extends BaseRequest {
    private char[] scheduleId = new char[1];

    public SetOperatingScheduleRequest(int nodeNumber, int scheduleId){
        super(nodeNumber,29,2,"Set_Operating_Schedule");
        this.scheduleId = String.format("%01d", scheduleId).toCharArray();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.scheduleId);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
