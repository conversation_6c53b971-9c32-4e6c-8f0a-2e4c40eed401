package com.smartfuel.service.messages.forecourt.PTS2.request;

import java.util.ArrayList;

public class TransactionInformationRequest {
    private ArrayList<Packet> Packets;
    private String Protocol;
    public TransactionInformationRequest(int pumpNumber, int transactionId)
    {

        Data d = new Data();
        d.setPump(pumpNumber);
        d.setTransaction(transactionId);

        Packet p = new Packet();
        p.setId(1);
        p.setType("PumpGetTransactionInformation");
        p.setData(d);

        this.Protocol = "jsonPTS";

        Packets = new ArrayList<Packet>();
        this.Packets.add(p);
    }

    public class Packet {
        public int getId() {
            return Id;
        }

        public void setId(int id) {
            this.Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            this.Type = type;
        }

        private int Id;
        private String Type;

        public Data getData() {
            return Data;
        }

        public void setData(Data data) {
            Data = data;
        }

        private Data Data;
    }
    public class Data{
        private int Pump;
        private int Transaction;

        public int getPump() {
            return Pump;
        }

        public void setPump(int pump) {
            Pump = pump;
        }

        public int getTransaction() {
            return Transaction;
        }

        public void setTransaction(int transaction) {
            Transaction = transaction;
        }
    }
}
