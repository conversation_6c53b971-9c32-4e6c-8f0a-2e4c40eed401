package com.smartfuel.service.escposprinter.textparser;

import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.escposprinter.EscPosPrinterCommands;
import com.smartfuel.service.escposprinter.exceptions.EscPosEncodingException;

public interface IPrinterTextParserElement {
    int length() throws EscPosEncodingException;

    IPrinterTextParserElement print(EscPosPrinterCommands paramEscPosPrinterCommands) throws EscPosEncodingException, ConnectionException;
}
