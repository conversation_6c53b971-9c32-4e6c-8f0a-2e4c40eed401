package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;
import org.json.JSONException;
public class PriceSetRequest {
    private final PriceSetRequestData data = new PriceSetRequestData("");

    private final String name = "FcPriceSet_req";

    private final String subCode = "00H";

    public PriceSetRequest() {}

    public PriceSetRequest(String paramString) {}

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class PriceSetRequestData {
        private final String PriceSetType;

        PriceSetRequestData(String data) {
            this.PriceSetType = data;
        }
    }
}
