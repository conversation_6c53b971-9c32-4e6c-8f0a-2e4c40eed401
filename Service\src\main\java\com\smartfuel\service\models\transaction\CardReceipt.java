package com.smartfuel.service.models.transaction;

public class CardReceipt {
    public String TerminalTransactionId;
    public String TransactionDateTime;

    public String TransactionType;
    public String TransactionStatus;
    public String TransactionPartialAmount;
    public String TransactionFinalAmount;
    public String TransactionGSTAmount;
    public String TransactionCurrency;

    public String RewardTotalDiscount;
    public String RewardDiscountPerLiter;

    public String ApplicationId;
    public String ApplicationLabel;
    public String CardSignature;
    public String CardATC;
    public String TID;
    public String STAN;
    public String HostResponseCode;
    public String HostResponse;
    public String AuthorisationId;
    public String PanSeqNo;
    public String CVM;
    public String StoreName;
    public String StoreABN;

    public String StoreAddress1;

    public String StoreAddress2;

    public String StoreCity;
    public String StorePostalCode;
    public String StoreState;

    public String KioskName;
    public String FuelGradeName;

    public String FuelPricePerLitre;

    public String FuelPumpNumber;

    public String FuelTransactionNumber;

    public String FuelValue;

    public String FuelVolume;
    public String VehicleOdometer;

    public String VehicleRegistration;
    public String InvoiceFooter;


















}
