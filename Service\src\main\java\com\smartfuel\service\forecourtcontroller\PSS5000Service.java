package com.smartfuel.service.forecourtcontroller;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.messages.forecourt.PSS5000.request.AuthorizeFuellingPointRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.request.CancelFuellingPointAuthorizeRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.request.ChangePriceSetRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.request.ClearSupTransactionRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.request.HeartbeatRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.request.LogonRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.request.PosConnectionStatusRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.request.PreparePrepaidTransactionRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.request.PriceSetRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.request.SupTransactionRequest;
import com.smartfuel.service.messages.forecourt.PSS5000.response.JPLResponse;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.FuelTransaction;
import com.smartfuel.service.models.forecourt.GradePrice;
import com.smartfuel.service.models.forecourt.GradePrices;
import com.smartfuel.service.network.Constant;
import com.smartfuel.service.network.SocketConfig;
import com.smartfuel.service.network.TCPMessenger;
import com.smartfuel.service.sqlite.models.ForecourtControllerTransaction;
import com.smartfuel.service.utils.Utils;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

public class PSS5000Service extends ForecourtController implements IForecourtController, TCPMessenger.Callback<JPLResponse> {
    private final Callback callbackHandler;

    private ArrayList<FuelPoint> configuredFuelPoints;

    private GradePrices configuredGradePrices;

    private final String forecourtControllerIP;

    private Gson json;
    private ObjectMapper myObjectMapper;
    private final SocketConfig socketConfig;
    private int myPosId;
    private String myOperationMode;
    private String myPosIpAddress;
    private AtomicLong lastHeartBeat = new AtomicLong();

    <T> PSS5000Service(int posId, String paramString, int port, String operationMode, String posIpAddress, Callback<T> paramCallback) {
        this.myPosId = posId;
        this.myOperationMode = operationMode;
        if(this.myOperationMode.isEmpty())
            this.myOperationMode = "single"; // Single is the default operation mode if none is provided.

        this.myPosIpAddress = posIpAddress;
        if(this.myPosIpAddress == null)
            this.myPosIpAddress = "";

        this.forecourtControllerIP = paramString;
        this.json = (new GsonBuilder()).create();
        this.callbackHandler = paramCallback;
        socketConfig = new SocketConfig(port, Constant.DEFAULT_TIME_OUT);
    }

    private void readConfiguration(JPLResponse paramJPLResponse) throws IOException {
        Iterator<JsonNode> iterator;
        iterator = paramJPLResponse.data.findValue("messages").elements();

        for (Iterator<JsonNode> it = iterator; it.hasNext(); ) {
            JsonNode currentNode = iterator.next();
            String responseName = currentNode.get("name").asText();
            switch (responseName) {
                default:
                    break;
                case "FpSupTransBufStatus_resp":
                    Log.i("ForecourtControllerService", paramJPLResponse.name);

                    myObjectMapper = new ObjectMapper();
                    JPLResponse jplResponse = myObjectMapper.treeToValue(currentNode, JPLResponse.class);
                    if (jplResponse.subCode.equals("03H")
                            && jplResponse.data.get("TransInSupBuffer").elements().hasNext()) {
                        //there should ONLY be 1 transaction in the controllers buffer. Hence "get(0)" in JSON array navigation
                        //validate that this transaction belongs to the device that is attempting to handle the transaction
                        if (jplResponse.data.get("TransInSupBuffer").get(0).get("TransLockId").asInt() == this.myPosId)
                            this.callbackHandler.FuelTransactionComplete(jplResponse);

                    }
                    break;
                case "FpStatus_resp":
                    if (iterator != null)
                        setupFuelPoints(paramJPLResponse.data.findValue("messages").elements());
                    GetFuelPriceData();
                    break;
            }
        }


        Log.i("MultiMsgResponse", paramJPLResponse.toString());
    }

    private void setupFuelPoints(Iterator<JsonNode> paramIterator) {
        if (this.configuredFuelPoints == null) // There are no configured fuelling points yet
        {
            this.configuredFuelPoints = new ArrayList<>();
        }

        while (paramIterator.hasNext()) {
            JsonNode jsonNode1 = paramIterator.next();
            FuelPoint fuelPoint = new FuelPoint();
            fuelPoint.setId(jsonNode1.get("data").get("FpId").asText());
            fuelPoint.setState(jsonNode1.get("data").get("FpMainState").get("value").asText());
            JsonNode jsonNode2 = jsonNode1.get("data").get("FpSupplStatusPars").get("FpAvailableGrades");
            if (jsonNode2 != null) {
                ArrayList<GradePrice> arrayList = new ArrayList();
                Iterator<JsonNode> iterator = jsonNode2.elements();
                while (iterator.hasNext()) {
                    JsonNode jsonNode = iterator.next();
                    GradePrice gradePrice = new GradePrice();
                    gradePrice.setId(jsonNode.asText());
                    gradePrice.setLabel("FG_" + jsonNode.asText());
                    arrayList.add(gradePrice);
                }
                fuelPoint.setGradePrices(arrayList);
            }

            FuelPoint currentFP = this.configuredFuelPoints.stream().filter(fuelPoint1 -> fuelPoint.getId().equals(fuelPoint1.getId())).findFirst().orElse(null);
            int i = this.configuredFuelPoints.indexOf(currentFP);
            if (currentFP == null) // fuelpoint not found in the configured array list - add
            {
                this.configuredFuelPoints.add(fuelPoint);
            } else // fuelpoint found in the configured array list - update
            {
                this.configuredFuelPoints.set(i, fuelPoint);
            }
        }
    }


    private void setupGradePrices(JsonNode paramJsonNode) {
        GradePrices configuredPrices = new GradePrices();
        configuredPrices.setGradePrices(new ArrayList<GradePrice>());

        configuredPrices.setPriceSetId(paramJsonNode.get("FcPriceSetId").asInt());

        ArrayList<String> priceGroupIds = new ArrayList<>();
        for (Iterator<JsonNode> it = paramJsonNode.findValues("FcPriceGroupId").get(0).elements(); it.hasNext(); ) {
            JsonNode n = it.next();
            priceGroupIds.add(n.asText());
        }
        configuredPrices.setPriceGroupId(priceGroupIds);
        Iterator<JsonNode> gradeIterator = ((JsonNode) paramJsonNode.findValues("FcGradeId").get(0)).elements();
        Iterator<JsonNode> priceIterator = ((JsonNode) paramJsonNode.findValues("FcPriceGroups").get(0)).get(0).elements();
        while (gradeIterator.hasNext() && priceIterator.hasNext()) {
            paramJsonNode = gradeIterator.next();
            JsonNode jsonNode = priceIterator.next();

            GradePrice gradePrice = new GradePrice();
            gradePrice.setId(paramJsonNode.asText());
            gradePrice.setLabel("FG_" + paramJsonNode.asText());
            gradePrice.setPrice(Float.valueOf(Float.parseFloat(jsonNode.asText()) / 10.0F));

            configuredPrices.getGradePrices().add(gradePrice);

            this.configuredGradePrices = configuredPrices;

            for (FuelPoint fuelPoint : this.configuredFuelPoints) {
                if (fuelPoint.getGradePrices() != null)
                    for (GradePrice gradePrice1 : fuelPoint.getGradePrices()) {
                        if (gradePrice1.getId().equals(paramJsonNode.asText()))
                            gradePrice1.setPrice(Float.valueOf(Float.parseFloat(jsonNode.asText()) / 10.0F));
                    }
            }
        }
    }

    @Override
    public <T> void AuthoriseFuellingPoint(String posId, T response) {
        JPLResponse data = (JPLResponse) response;

        String fp = data.data.get("FpId").asText();
        AuthorizeFuellingPointRequest request = new AuthorizeFuellingPointRequest(posId, fp, this.callbackHandler.getValidGrades(Integer.parseInt(posId), Integer.parseInt(fp)));
        String str = "\002" + this.json.toJson(request) + "\003";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
    }

    @Override
    public void CancelFuelPointAuthorisation(String posId, String fuelpointId) {
        CancelFuellingPointAuthorizeRequest request = new CancelFuellingPointAuthorizeRequest(posId, fuelpointId);
        String str = "\002" + this.json.toJson(request) + "\003";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
    }

    @Override
    public void CompleteFuelTransaction(String posId, String fuelpointId, String trnSeqNumber, long volume, long fueldispensedAmount) {
        ClearSupTransactionRequest request = new ClearSupTransactionRequest(posId, fuelpointId, trnSeqNumber, String.valueOf(volume), String.valueOf(fueldispensedAmount));
        String str = "\002" + this.json.toJson(request) + "\003";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
    }

    @Override
    public void FetchFuelTransaction(String posId, int fuelpointId, String trnSeqNumber) {
        ArrayList<String> infoItemsList = new ArrayList<>(4);
        infoItemsList.add("51");
        //v.add("61");
        //v.add("62");
        infoItemsList.add("64");
        infoItemsList.add("65");
        infoItemsList.add("66");
        //v.add("71");
        //v.add("73");
        //v.add("74");
        SupTransactionRequest request = new SupTransactionRequest(posId, String.valueOf(fuelpointId), trnSeqNumber, infoItemsList);
        String str = "\002" + this.json.toJson(request) + "\003";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
    }

    @Override
    public void GetFuelPriceData() {
        PriceSetRequest request = new PriceSetRequest("00H");
        String str = "\002" + this.json.toJson(request) + "\003";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
    }

    @Override
    public void Heartbeat() {
        if(lastHeartBeat.get() != 0 && (System.currentTimeMillis() - lastHeartBeat.get()) > 30000){
            Log.i("Heartbeat", "Heartbeat not responding, time: " + (System.currentTimeMillis() - lastHeartBeat.get()));
            TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, JPLResponse.class, this);
            if((System.currentTimeMillis() - lastHeartBeat.get()) > 120000){
                GetPosConnectionStatus();
            }
            return;
        }

        String str = "\002" + this.json.toJson(new HeartbeatRequest()) + "\003";
        try {
            TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
        } catch (Exception e) {
            throw e;
        }

        GetPosConnectionStatus();
    }

    @Override
    public void Logon(String posId, String applicationId, String clientVersion) {
        ArrayList<Integer> arrayList = new ArrayList(1);
        arrayList.add(0, Integer.valueOf(2));
        LogonRequest request = new LogonRequest("POS,RI,APPL_ID=" + applicationId + ",UNSO_TRBUFSTA_3,UNSO_INSTSTA_1,UNSO_FPSTA_3:MFDR=3", "0061", String.valueOf(posId), arrayList);
        String str = "\002" + this.json.toJson(request) + "\003";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, JPLResponse.class, this);
    }

    @Override
    public void PreparePrepaidTransaction(String posId, String fuelpointId, String gradeId, String prepaidAmount) {
        PreparePrepaidTransactionRequest request = new PreparePrepaidTransactionRequest(posId, fuelpointId, gradeId, prepaidAmount);
        String str = "\002" + this.json.toJson(request) + "\003";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
    }

    @Override
    public void setFuelGradeNames(Map fuelGradeNames) {

    }

    @Override
    public void ChangeFuelPrices(String priceSetId, ArrayList<String> priceGroupIds, ArrayList<String> fuelGradeIds, ArrayList<String> priceGroups, String priceActivationDateTime) {
        ChangePriceSetRequest request = new ChangePriceSetRequest(priceSetId, priceGroupIds, fuelGradeIds, priceGroups, priceActivationDateTime);
        String str = "\002" + this.json.toJson(request) + "\003";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
    }

    @Override
    public ArrayList<FuelPoint> getConfiguredFuelPoints() {
        return this.configuredFuelPoints;
    }

    @Override
    public GradePrices getConfiguredGradePrices() {
        if (configuredGradePrices != null)
            return this.configuredGradePrices;
        else
            return new GradePrices();
    }


    @Override
    public void onError(Throwable paramThrowable) {
        this.callbackHandler.Error(paramThrowable);
    }

    @Override
    public String parseResponse(String response, String lookupfield) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        JPLResponse jplResponse = objectMapper.getFactory().createParser(response).readValueAs(JPLResponse.class);
        String output = null;
        switch (lookupfield) {
            case "FpId":
            case "TransSeqNo":
                output = jplResponse.data.get(lookupfield).asText();
                break;
            case "volume":
                output = String.valueOf(jplResponse.data.get("TransPars").get("Vol_e").asLong());
                break;
            case "money":
                output = String.valueOf(jplResponse.data.get("TransPars").get("Money_e").asLong());
                break;
            case "gradeId":
                output = jplResponse.data.get("TransPars").get("FcGradeId").asText();
                break;
            case "price":
                output = String.valueOf(jplResponse.data.get("TransPars").get("Price_e").asLong());
                break;
        }
        return output;
    }

    public void GetPosConnectionStatus() {
        PosConnectionStatusRequest request = new PosConnectionStatusRequest();
        String str = "\002" + this.json.toJson(request) + "\003";
        TCPMessenger.getInstance(socketConfig).sendCommand(new TCPMessenger.Request(this.forecourtControllerIP, str), JPLResponse.class, this);
        Log.i("ForecourtControllerService", "GetPosConnectionStatus");
    }

    public void onResponse(JPLResponse paramJPLResponse) throws InterruptedException {
        lastHeartBeat.set(System.currentTimeMillis());
        Log.i("TCPMessenger: Response Name", paramJPLResponse.name);
        Log.i("TCPMessenger: Response SubCode", paramJPLResponse.subCode);
        //Log.i("TCPMessenger: Response Direction", String.valueOf(paramJPLResponse.mDirection));
        Log.i("TCPMessenger: Response Source", String.valueOf(paramJPLResponse.mSource));
        Log.i("TCPMessenger: Response Solicited", String.valueOf(paramJPLResponse.solicited));
        try {
            Log.i("TCPMessenger: Response JSON", new ObjectMapper().writeValueAsString(paramJPLResponse.data));
        } catch (JsonProcessingException e) {
            Log.i("TCPMessenger: Response JSON Failed", e.getMessage());
        }

        String str = paramJPLResponse.name;
        String responseName = paramJPLResponse.name;
        switch (responseName) {
            case "FpSupTransBufStatus_resp":
                Log.i("ForecourtControllerService", paramJPLResponse.name);
                if (paramJPLResponse.subCode.equals("03H") && paramJPLResponse.data.get("TransInSupBuffer").elements().hasNext()) {
                    if (paramJPLResponse.data.get("TransInSupBuffer").get(0).get("TransLockId").asInt() == this.myPosId) {
                        this.callbackHandler.FuelTransactionComplete(paramJPLResponse);
                    }
                }
                break;
            case "FpSupTrans_resp":
                Log.i("ForecourtControllerService", paramJPLResponse.name);
                this.callbackHandler.ClearFuelTransactionBuffer(paramJPLResponse);
                break;
            case "FcInstallStatus_resp":
            //case "FcSwInstallStatus_resp":
                Thread.sleep(1500);
                this.callbackHandler.SetupComplete();
                break;

            case "FcPriceSet_resp":
                Log.i("ForecourtControllerService", paramJPLResponse.name);
                try {
                    setupGradePrices(paramJPLResponse.data);
                    this.callbackHandler.ForecourtControllerReady();
                } catch (Exception exception) {
                    this.callbackHandler.Error(exception);
                }
                break;
            case "MultiMessage_resp":
                Log.i("ForecourtControllerService", paramJPLResponse.name);
                try {
                    readConfiguration(paramJPLResponse);
                } catch (IOException e) {
                    Log.e("DOMS Message", "Multi-Message Failed", e);
                }
                break;
            case "prepare_PrepaidTrans_resp":
                Log.i("ForecourtControllerService", paramJPLResponse.name);
                this.callbackHandler.FuelPointPrePaidSetupComplete(paramJPLResponse);
                break;
            case "RejectMessage_resp":
                Exception exception = new Exception(paramJPLResponse.data.get("RejectInfoText").asText());
                Log.e("PSS5000Service", "Controller Rejected Message", exception);
                if (exception.getMessage().equals("\"FcLogon_req\" was expected."))
                    this.callbackHandler.LogonRequired();
                else
                    this.callbackHandler.Error(exception);
                break;
            case "FpStatus_resp":
                Log.i("ForecourtControllerService", paramJPLResponse.name);
                if (paramJPLResponse.subCode.equals("03H")) {
                    this.callbackHandler.FuellingStatusUpdate(paramJPLResponse);
                }
                break;
            case "authorize_Fp_resp":
                Log.i("ForecourtControllerService", paramJPLResponse.name);
                this.callbackHandler.FuelPointAuthorized(paramJPLResponse.data.get("FpId").asText());
                break;
            case "change_FcPriceSet_resp": // need a method which will send a price status request to the controller
                GetFuelPriceData();
                break;
            case "FcLogon_resp":
                this.callbackHandler.ForecourtControllerLoginComplete();
                break;
            case "FcSwInstallStatus_resp":
            case "FcPriceSetStatus_resp":
            case "TankControlStatus_resp":
            case "jpl":
            case "FcOperationModeStatus_resp":
            case "FcStatus_resp":
            case "heartbeat":
                Log.i("ForecourtControllerService", "No Action performed");
                break;
            case "PosConnectionStatus_resp":
                checkConnectionStatusResponse(paramJPLResponse.data);
                break;
            default:
                Log.i("ForecourtControllerService", paramJPLResponse.name);
                break;
        }

        TCPMessenger.getInstance(socketConfig).readCommand(this.forecourtControllerIP, JPLResponse.class, this);
    }

    private void checkConnectionStatusResponse(JsonNode data) {
        try {
            //String localAddress = TCPMessenger.getInstance(socketConfig).getSocket(forecourtControllerIP).getLocalAddress().getHostAddress();
            String localAddress = Utils.getDeviceIpAddress();
            JsonNode list = data.get("Connections");
            boolean connected = false;
            boolean connectedPosDevice = false;
            String[] posIps = myPosIpAddress.split("\\,");
            for (JsonNode item : list) {
                int connType = item.get("ConnType").get("value").asInt();
                String address = "?";
                int port = -1;
                switch (connType) {
                    case 0: //Unknown
                        break;
                    case 1://Serial
                        address = item.get("ConnAddress").asText();
                        port = item.get("ServerPortNo").asInt();
                        break;
                    case 2://TCP/IP
                        address = getIp(Long.parseLong(item.get("ConnAddress").asText()));
                        port = item.get("ServerPortNo").asInt();
                        break;
                }
                Log.d("checkConnectionStatusResponse", "Connected Address " + address + ":" + port);
                Log.d("checkConnectionStatusResponse", "Instance Address " + localAddress);

                if(Arrays.stream(posIps).anyMatch(address::equals) && (port==8888 || port==0)){
                    connectedPosDevice = true;
                }
                if (address.equals(localAddress)) {
                    connected = true;
                    //break;
                }
            }
            Log.d("checkConnectionStatusResponse", "Operation Mode " + myOperationMode);
            Log.d("checkConnectionStatusResponse", "POS IP " + myPosIpAddress);
            Log.d("checkConnectionStatusResponse", "Connected with POS " + connectedPosDevice);

            if (!connected)
                callbackHandler.LogonRequired();
            //when the device is in multi operation mode - there are no additional events required - operates as designed

            //This device is connected, no other device is connected and it's in single device mode - then notify App to show Home Screen
            if (connected && !connectedPosDevice && myOperationMode.equals("single"))
                callbackHandler.ShowHomeScreen();
            //This device is connected, other devices are connected and it's in single device mode - then notify App to show Standby Screen
            if (connectedPosDevice && myOperationMode.equals("single"))
                callbackHandler.ShowStandbyScreen();

        } catch (Exception e) {
            Log.w("checkConnectionStatusResponse", e);
            e.printStackTrace();
        }
    }

    private String getIp(long connAddress) throws UnknownHostException {
        byte[] ipAddressBytes = new byte[]{
                (byte) ((connAddress >> 24) & 0xFF),
                (byte) ((connAddress >> 16) & 0xFF),
                (byte) ((connAddress >> 8) & 0xFF),
                (byte) (connAddress & 0xFF)
        };

        InetAddress inetAddress = InetAddress.getByAddress(ipAddressBytes);
        return inetAddress.getHostAddress();
    }

    @Override
    public <T> ForecourtControllerTransaction getTransactionData(T response) {
        JPLResponse r = (JPLResponse) response;

        ForecourtControllerTransaction forecourtControllerTransaction = new ForecourtControllerTransaction();
        forecourtControllerTransaction.setTime(System.currentTimeMillis());
        if (r.data.get("FpId") != null)
            forecourtControllerTransaction.setFpId(r.data.get("FpId").asInt());
        if (r.data.get("FpLockId") != null)
            forecourtControllerTransaction.setFpLockId(r.data.get("FpLockId").asInt());
        if (r.data.get("FcGradeId") != null)
            forecourtControllerTransaction.setFcGradeId(r.data.get("FcGradeId").asText());
        JsonNode jsonNode = r.data.get("FpMainState");
        if (jsonNode != null)
            forecourtControllerTransaction.setFpMainState(r.data.get("FpMainState").get("value").asText());
        if (r.data.get("FpSupplStatusPars") != null) {
            if (r.data.get("FpSupplStatusPars").get("FpSubStates2") != null)
                forecourtControllerTransaction.setFpSubState2(r.data.get("FpSupplStatusPars").get("FpSubStates2").get("value").asText());
            if (r.data.get("FpSupplStatusPars").get("FpSubStates4") != null)
                forecourtControllerTransaction.setFpSubState4(r.data.get("FpSupplStatusPars").get("FpSubStates4").get("value").asText());
            if (r.data.get("FpSupplStatusPars").get("FuellingDataMon_e") != null)
                forecourtControllerTransaction.setFuellingDataMonE(r.data.get("FpSupplStatusPars").get("FuellingDataMon_e").asLong());
            if (r.data.get("FpSupplStatusPars").get("FuellingDataVol_e") != null)
                forecourtControllerTransaction.setFuellingDataVolE(r.data.get("FpSupplStatusPars").get("FuellingDataVol_e").asLong());
        }
        if(forecourtControllerTransaction.getFpMainState().equals("04H"))
            forecourtControllerTransaction.setFpSubState2("NozzleLifted");
        return forecourtControllerTransaction;
    }

    public void setConfiguredFuelPoints(ArrayList<FuelPoint> paramArrayList) {
        this.configuredFuelPoints = paramArrayList;
    }

    public void setConfiguredGradePrices(ArrayList<GradePrice> paramArrayList) {
        this.configuredGradePrices.setGradePrices(paramArrayList);
    }

    @Override
    public <T> FuelTransaction getFuelTransactionDetails(T response) {

        JPLResponse r = (JPLResponse) response;

        FuelTransaction ft = null;

        if (r.subCode.equals("03H")) {
            ft = new FuelTransaction();
            ft.setFuelPointId(r.data.get("FpId").asInt());
            ft.setFuelGradeId(r.data.get("TransInSupBuffer").get(0).get("FcGradeId").asText());
            ft.setSeqNumber(r.data.get("TransInSupBuffer").get(0).get("TransSeqNo").asText());
            ft.setInfoMask(r.data.get("TransInSupBuffer").get(0).get("TransInfoMask").get("value").asText());
            //ft.setMoneyDue(r.data.get("TransInSupBuffer").get(0).get("MoneyDue_e").asLong());
            ft.setVolume(r.data.get("TransInSupBuffer").get(0).get("Vol_e").asLong());
        } else if (r.subCode.equals("00H")) {
            ft = new FuelTransaction();
            ft.setFuelPointId(r.data.get("FpId").asInt());
            ft.setSeqNumber(r.data.get("TransSeqNo").asText());
            ft.setFuelGradeId(r.data.get("TransPars").get("FcGradeId").asText());
            ft.setMoneyDue(r.data.get("TransPars").get("Money_e").asLong());
            ft.setVolume(r.data.get("TransPars").get("Vol_e").asLong());
            ft.setPrice(r.data.get("TransPars").get("Price_e").asLong());
        }
        return ft;
    }


}

