package com.smartfuel.service.diagnostics;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DiagnosticModel {

    private String date;
    private boolean state;

    public DiagnosticModel(boolean state){
        String pattern = "dd-MM-yyyy HH:mm:ss";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        Date now = new Date();
        date = simpleDateFormat.format(now);
        this.state = state;
    }

    public String getDate() {
        return date;
    }

    public String getState() {
        return state ? "on" : "off";
    }

}
