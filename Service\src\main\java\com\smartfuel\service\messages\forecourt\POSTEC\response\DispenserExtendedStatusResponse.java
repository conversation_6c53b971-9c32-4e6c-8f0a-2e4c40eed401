package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;
import com.smartfuel.service.messages.forecourt.POSTEC.enums.PumpStatus;

/*
01 038 0123 16 2000 01 03 032 0 12 123456 987654 1243 032 1 01 123456 987654 5641 Req_Disp_Ext_Status
            0. Client Number:              01
            1. Request Number:             38
            2. Sequence Number:            0123
            3. Number of Parameters:       16
            4. Success Code:               2000
            5. Pump Number:                01
            6. Pump status:                03
            7. Sale Status:                032
            8. Sale Hose Number:           0
            9. Sale Grade Number:          12
            10. Sale Amount:               123456
            11. Sale Quantity:             987654
            12. Sale Price:                1243
            13. Memory Status:             032
            14. Sale Hose Number:          1
            15. Sale Grade Number:         01
            16. Sale Amount:               123456
            17. Sale Quantity:             987654
            18. Sale Price:                5641
            19. Req_Disp_Ext_Status
         */
public class DispenserExtendedStatusResponse extends BaseResponse {
    private int pumpNumber;
    private PumpStatus pumpStatus;
    private DispenserSaleStatus saleStatus;
    private int hooseNumber;
    private int gradeId;
    private long saleAmount;
    private long saleQuantity;
    private long salePrice;
    private DispenserSaleStatus memoryStatus;
    private int memoryHoseNumber;
    private int memoryGradeId;
    private long memoryAmount;
    private long memoryQuantity;
    private long memoryPrice;

    public DispenserExtendedStatusResponse(String response) throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.pumpNumber = Integer.parseInt(super.responseData[5]);
        this.pumpStatus = PumpStatus.getPumpStatusById(Integer.parseInt(super.responseData[6]));
        this.saleStatus = new DispenserSaleStatus(super.responseData[7].getBytes());
        this.hooseNumber = Integer.parseInt(super.responseData[8]);
        this.gradeId = Integer.parseInt(super.responseData[9]);
        this.saleAmount = Long.parseLong(super.responseData[10]);
        this.saleQuantity = Long.parseLong(super.responseData[11]);
        this.salePrice = Long.parseLong(super.responseData[12]);
        this.memoryStatus = new DispenserSaleStatus(super.responseData[13].getBytes());
        this.memoryHoseNumber = Integer.parseInt(super.responseData[14]);
        this.memoryGradeId = Integer.parseInt(super.responseData[15]);
        this.memoryAmount = Long.parseLong(super.responseData[16]);
        this.memoryQuantity = Long.parseLong(super.responseData[17]);
        this.memoryPrice = Long.parseLong(super.responseData[18]);
    }

    public int getPumpNumber() {
        return pumpNumber;
    }

    public PumpStatus getPumpStatus() {
        return pumpStatus;
    }

    public DispenserSaleStatus getSaleStatus() {
        return saleStatus;
    }

    public int getHooseNumber() {
        return hooseNumber;
    }

    public int getGradeId() {
        return gradeId;
    }

    public long getSaleAmount() {
        return saleAmount;
    }

    public long getSaleQuantity() {
        return saleQuantity;
    }

    public long getSalePrice() {
        return salePrice;
    }

    public DispenserSaleStatus getMemoryStatus() {
        return memoryStatus;
    }

    public int getMemoryHoseNumber() {
        return memoryHoseNumber;
    }

    public int getMemoryGradeId() {
        return memoryGradeId;
    }

    public long getMemoryAmount() {
        return memoryAmount;
    }

    public long getMemoryQuantity() {
        return memoryQuantity;
    }

    public long getMemoryPrice() {
        return memoryPrice;
    }
}
