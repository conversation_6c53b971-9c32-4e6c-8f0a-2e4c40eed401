package com.smartfuel.service;

import android.accounts.NetworkErrorException;
import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smartfuel.service.components.DaggerIKioskComponent;
import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.diagnostics.ConnectionDiagnostic;
import com.smartfuel.service.diagnostics.DiagnosticService;
import com.smartfuel.service.diagnostics.NetworkDiagnostic;
import com.smartfuel.service.enums.transaction.TransactionServiceType;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.logger.LoggerService;
import com.smartfuel.service.logic.ForecourtLogic;
import com.smartfuel.service.logic.TransactionLogic;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.GradePrices;
import com.smartfuel.service.models.kiosk.response.AccountCardCapabilities;
import com.smartfuel.service.models.kiosk.response.ConfigurationResponse;
import com.smartfuel.service.models.kiosk.response.ValidationResponse;
import com.smartfuel.service.models.posmaster.OPTExport;
import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardUser;
import com.smartfuel.service.modules.KioskModule;
import com.smartfuel.service.modules.NetworkModule;
import com.smartfuel.service.modules.OptModule;
import com.smartfuel.service.posmaster.POSMaster;
import com.smartfuel.service.property.IProperties;
import com.smartfuel.service.repository.IConfigurationRepository;
import com.smartfuel.service.repository.IForecourtControllerTransactionRepository;
import com.smartfuel.service.repository.IRewardCardDiscountRepository;
import com.smartfuel.service.repository.ITransactionRepository;
import com.smartfuel.service.sqlite.models.Configuration;
import com.smartfuel.service.sqlite.models.Transaction;
import com.smartfuel.service.transaction.CardTransactionService;
import com.smartfuel.service.utils.ApiException;
import com.smartfuel.service.utils.ApiServiceHelper;
import com.smartfuel.service.utils.CurrencyConverter;
import com.smartfuel.service.utils.Utils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.ConnectException;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.inject.Inject;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

public class OPTService<T> extends Service {
    @Inject
    public IForecourtControllerTransactionRepository pss5000TransactionRepository;
    @Inject
    public IKioskApiService kioskApiService;
    @Inject
    public IConfigurationRepository configurationRepository;
    @Inject
    public ITransactionRepository transactionRepository;
    @Inject
    public IRewardCardDiscountRepository rewardCardDiscountRepository;

    private CardTransactionService cardTransactionService;
    private IServiceEvents serviceEvents;
    private final IBinder mBinder = new LocalBinder();
    private ObjectMapper objectMapper;

    protected boolean kioskExporting = false;
    protected List<WhiteCardReceipt> whiteCardReceipts;
    protected List<CardReceipt> cardReceipts;
    protected int posId;
    protected String cardServiceType;

    protected ForecourtLogic<T> forecourtLogic;
    protected TransactionLogic transactionLogic;

    protected Exception tempException;
    protected AtomicBoolean setup = new AtomicBoolean(false);

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return this.mBinder;
    }

    @Override
    public int onStartCommand(Intent paramIntent, int paramInt1, int paramInt2) {
        if(setup.get())
            return START_STICKY;

        DaggerIKioskComponent
                .builder()
                .kioskModule(new KioskModule(this))
                .optModule(new OptModule(this))
                .networkModule(new NetworkModule(this, BuildConfig.kiosk_api))
                .build().inject((OPTService) this);
        if (this.objectMapper == null)
            this.objectMapper = new ObjectMapper();

        tempException = null;

        LoggerService.setup(this);
        DiagnosticService.addMonitor(new NetworkDiagnostic(this), UnknownHostException.class, NetworkErrorException.class, ConnectException.class);
        ignoreSSL();

        Thread thread = new Thread(() -> {
            try {
                Utils.isNetworkAvailable(OPTService.this);

                ApiServiceHelper.setInstances(configurationRepository, kioskApiService);

                validateKiosk();
                authenticateKiosk();
                configureLocalSqlite();

                LoggerService.setup(configurationRepository, kioskApiService);

                posId = Integer.parseInt(configurationRepository.getConfigurationByName("pos_id").getValue());
                cardServiceType = configurationRepository.getConfigurationByName("payment_service_type").getValue();

                forecourtLogic = ForecourtLogic.getInstance(kioskApiService, pss5000TransactionRepository,  configurationRepository,
                        transactionRepository, rewardCardDiscountRepository, OPTService.this, posId,
                        configurationRepository.getConfigurationByName("forecourt_controller_type").getValue(),
                        Integer.parseInt(configurationRepository.getConfigurationByName("forecourt_controller_port").getValue()),
                        configurationRepository.getConfigurationByName("forecourt_controller_ip").getValue(),
                        configurationRepository.getConfigurationByName("operation_mode").getValue(),
                        configurationRepository.getConfigurationByName("pos_ip").getValue());
                transactionLogic = TransactionLogic.getInstance(kioskApiService, configurationRepository, transactionRepository, rewardCardDiscountRepository,
                        forecourtLogic, objectMapper, posId);

            } catch (ApiException e) {
                //serviceEvents.systemError("Service Offline", e);
                Log.e("Service","StartFailed",e);
                tempException = e;
            } catch (NetworkErrorException e) {
                tempException = e;
            }
        });

        try {
            thread.start();
            thread.join();
            setup.set(true);
            if(tempException != null)
                return START_STICKY;
            cardTransactionService = new CardTransactionService(TransactionServiceType.valueOf(cardServiceType), OPTService.this);
            forecourtLogic.setCardTransactionService(cardTransactionService);
            transactionLogic.setCardTransactionService(cardTransactionService);
            DiagnosticService.addMonitor(new ConnectionDiagnostic(cardTransactionService), ConnectionException.class);
        } catch (Exception exception) {
            Log.e("StartFailed", exception);
            tempException = exception;
            //this.serviceEvents.systemError("Service Configuration Failed", exception);
        }
        return START_STICKY;
    }

    public String getCurrentActivityName(){
        if(configurationRepository != null) {
            Configuration conf = configurationRepository.getConfigurationByName("currentActivityName");
            if(conf != null)
                return conf.getValue();
        }
        return "";
    }

    public void LogonForecourtController() {

        try {
            forecourtLogic.Logon(String.valueOf(posId), "OPT" + String.valueOf(posId), "1.0.0");
        } catch (Exception exception) {
            Log.e("Forecourt Controller Logon Failed", exception);
            this.serviceEvents.systemError("Forecourt Controller Logon Failed", exception);
        }
    }

    public void registerClient(IServiceEvents activity) throws Exception {
        if(tempException != null) {
            Log.e("Error to create OPTService", this.tempException);
            activity.systemError("Error to create OPTService", this.tempException);
            throw tempException;
        }
        this.serviceEvents = activity;
        forecourtLogic.registerClient(activity);
        transactionLogic.registerClient(activity);
        new Thread(() ->{
            try {
                configurationRepository.addConfiguration(new Configuration("currentActivityName", activity.getClass().getSimpleName()));
            }catch (Exception e){
            }
        }).start();
    }

    public void PrepareCustomerReceipt(String customerTransactionIdentifier, String cardType) {
        switch (cardType){
            case "WHITECARD":
                try{
                    fetchWhitecardUserReceiptData(((WhiteCardUser) this.objectMapper.getFactory().createParser(customerTransactionIdentifier).readValueAs(WhiteCardUser.class)).getUser());
                    if (this.whiteCardReceipts == null || this.whiteCardReceipts.size() == 0) {
                        this.serviceEvents.serviceReady();
                        this.serviceEvents.customerReceiptNotFount();
                    } else {
                        this.serviceEvents.customerReceiptDataReady(this.whiteCardReceipts);
                    }
                } catch (IOException iOException) {
                    Log.e("Customer Receipt Failed", iOException);
                    this.serviceEvents.systemError("Customer Receipt Failed", iOException);
                }
                break;
            case "CARD":
                PrepareCustomerReceipt(new ArrayList<Integer>() {{ Integer.parseInt(customerTransactionIdentifier); }}, cardType);
                break;
        }
    }

    public void PrepareCustomerReceipt(List<Integer> customerTransactionIdentifier, String cardType) {
        switch (cardType){
            case "WHITECARD":
                PrepareCustomerReceipt(customerTransactionIdentifier.stream().findFirst().get().toString(), cardType);
                break;
            case "CARD":
                fetchCardUserReceiptData(customerTransactionIdentifier);
                if (this.cardReceipts == null || this.cardReceipts.size() == 0) {
                    this.serviceEvents.serviceReady();
                    this.serviceEvents.customerReceiptNotFount();
                } else {
                    this.serviceEvents.customerCardReceiptDataReady(this.cardReceipts);
                }
                break;
        }
    }

    public class LocalBinder extends Binder {
        public OPTService getServiceInstance() {
            return OPTService.this;
        }
    }

    private void authenticateKiosk() throws ApiException {
        if (this.configurationRepository.getConfigurationByName("kiosk_bearer_token") != null) {
            Log.i("authenticateKiosk", "kiosk bearer token in local storage");
        } else {
            ApiServiceHelper.authenticateKiosk();
        }
    }

    private void configureLocalSqlite() throws ApiException {

        try{
            ConfigurationResponse configurationResponse = ApiServiceHelper.executeAPI(this.kioskApiService.getKioskConfig());

            if(configurationResponse != null && !configurationResponse.isEmpty()) {
                //Log.d(OPTService.class.getSimpleName(), "ConfigurationResponse size:" + configurationResponse.size());
                //configurationResponse.add(new Configuration("kiosk_api", BuildConfig.kiosk_api));
                String currency = "";
                for (Configuration conf : configurationResponse) {
                    if (conf.getName().equals("store_currency")) {
                        currency = conf.getValue();
                        break;
                    }
                }
                if (!currency.equals(""))
                    CurrencyConverter.setCurrency(currency);

                this.configurationRepository.addAllConfigurations(configurationResponse);
            }
        }catch (ApiException e){
            if(e.getCause() instanceof SocketTimeoutException && this.configurationRepository.getConfigurationByName("kiosk_hash_key") != null){

            }else{
                throw e;
            }
        }
    }

    private void fetchWhitecardUserReceiptData(String whitecardUserId) {
        Log.i("ReceiptProcessActivity", "fetching receipts for: " + whitecardUserId);
        try {
            Thread thread = new Thread(() -> whiteCardReceipts = transactionRepository.getWhitecardUserReceipt(whitecardUserId));

            thread.start();
            thread.join();
        } catch (Exception exception) {
            Log.e("Receipt Data Failed", exception);
            this.serviceEvents.systemError("Receipt Data Failed", exception);
        }
    }

    private void fetchCardUserReceiptData(List<Integer> terminalTransactionId){
        Log.i("ReceiptProcessActivity", "fetching receipts for: " + terminalTransactionId);
        try {
            Thread thread = new Thread(() -> cardReceipts = transactionRepository.getCardUserReceipt(terminalTransactionId));

            thread.start();
            thread.join();
        } catch (Exception exception) {
            Log.e("Receipt Data Failed", exception);
            this.serviceEvents.systemError("Receipt Data Failed", exception);
        }
    }

    private void validateKiosk() throws ApiException {
        //if (this.configurationRepository.getConfigurationByName("kiosk_hash_key") != null) {
        //    Log.i("validateKiosk", "Kiosk Hash Key in local storage");
        //} else {
            try {
                PropertyReader propertyReader = new PropertyReader(getBaseContext());
                IProperties properties = propertyReader.getMyProperties();
                ValidationResponse validationResponse = ApiServiceHelper.executeAPI(this.kioskApiService.validateKiosk(properties.getAndroidId(this)));

                if (validationResponse.getValidateHash() != null && !validationResponse.getValidateHash().isEmpty()) {

                    //configurationResponse.add(new Configuration("android_id", properties.getAndroidId(this)));
                    this.configurationRepository.addConfiguration(new Configuration("kiosk_hash_key", validationResponse.getValidateHash()));
                    this.configurationRepository.addConfiguration(new Configuration("kiosk_name", validationResponse.getName()));
                    this.configurationRepository.addConfiguration(new Configuration("kiosk_id", validationResponse.getId()));

                    this.configurationRepository.addConfiguration(new Configuration("store_id", validationResponse.getStore().getId()));
                    this.configurationRepository.addConfiguration(new Configuration("store_name", validationResponse.getStore().getName()));
                    this.configurationRepository.addConfiguration(new Configuration("store_address_1", validationResponse.getStore().getAddress()));
                    this.configurationRepository.addConfiguration(new Configuration("store_state", validationResponse.getStore().getState()));
                    this.configurationRepository.addConfiguration(new Configuration("store_postcode", validationResponse.getStore().getPostCode()));
                    this.configurationRepository.addConfiguration(new Configuration("store_city", validationResponse.getStore().getSuburb()));
                    this.configurationRepository.addConfiguration(new Configuration("store_abn", validationResponse.getStore().getAbn()));
                }
            }catch (ApiException e){
                if(e.getCause() instanceof SocketTimeoutException && this.configurationRepository.getConfigurationByName("kiosk_hash_key") != null){

                }else{
                    throw e;
                }
            }
        //}
    }

    public void sendTransactionReceipt(List<CardReceipt> cardReceipts, String email) throws InterruptedException {
        Thread thread = new Thread(() -> {
            try {
                for (CardReceipt receipt : this.cardReceipts) {
                    ApiServiceHelper.executeAPI(kioskApiService.sendTransactionReceipt(receipt, email));
                }
            }catch (ApiException e){
                Log.e("Transaction Receipt Failed", e);
                serviceEvents.systemError("Transaction Receipt Failed", e);
            }
        });
        thread.start();
        thread.join();
    }

    public String[] getParameterizedFuelPoint() throws InterruptedException {
        AtomicReference<String> atomicReference = new AtomicReference<>();
        Thread thread = new Thread(() -> {
            String fuelPoints = configurationRepository.getConfigurationByName("configured_fuel_point").getValue();
            atomicReference.set(fuelPoints);
        });
        thread.start();
        thread.join();
        String ret = atomicReference.get();
        if(ret != null && !ret.trim().equals(""))
            return ret.trim().split(",");
        else
            return null;
    }

    public String getConfiguration(String name, String defaultValue){
        synchronized (this) {
            AtomicReference<String> atomicReference = new AtomicReference<>();
            Thread thread = new Thread(() -> {
                Configuration conf = null;
                try {
                    conf = configurationRepository.getConfigurationByName(name);
                } catch (Exception e) {
                    Log.e("getConfiguration", e);
                }
                if (conf != null && conf.getValue() != null && !conf.getValue().isEmpty())
                    atomicReference.set(conf.getValue());
                else
                    atomicReference.set(defaultValue);
            });
            thread.start();
            try {
                thread.join();
                return atomicReference.get();
            } catch (InterruptedException e) {
                Log.e("getConfiguration", e);
            }
            return defaultValue;
        }
    }

    public void initialiseTransaction(String pumpNo, long transactionAmount) throws InterruptedException {
        transactionLogic.initialiseTransaction(pumpNo, transactionAmount);
    }

    public void validateTransaction(long externalReference, String im30Reference, AccountCardCapabilities capabilities) {
        transactionLogic.validateTransaction(externalReference, im30Reference, capabilities);
    }

    public void validateRewardCard(long externalReference, String track2) {
        transactionLogic.validateRewardCard(externalReference, track2);
    }

    public void initialiseReceipt() throws InterruptedException {
        transactionLogic.initialiseReceipt();
    }

    public void initialiseMagnetic() throws InterruptedException {
        transactionLogic.initialiseMagnetic();
    }

    public void cancelCardTransaction() throws InterruptedException {
        transactionLogic.cancelCardTransaction();
    }

    public void cancelCardTransaction(String pumpNo) throws InterruptedException {
        transactionLogic.cancelCardTransaction(pumpNo);
    }

    public void AuthoriseWhiteCardTransaction(String whitecardUserData, String pumpNumber,
                                              long transactionAmount){
        transactionLogic.AuthoriseWhiteCardTransaction(whitecardUserData, pumpNumber, transactionAmount);
    }

    public GradePrices getConfiguredGradePrices(){
        return forecourtLogic.getConfiguredGradePrices();
    }

    public void ProceedPrepaidTransaction(long transactionId) throws InterruptedException {
        forecourtLogic.ProceedPrepaidTransaction(transactionId);
    }

    public ArrayList<FuelPoint> getConfiguredFuelPoints(){

        String[] fuelPointsParam = new String[0];
        try {
            fuelPointsParam = getParameterizedFuelPoint();
        } catch (InterruptedException e) {
            Log.e("getConfiguredFuelPoints", e);
        }

        //forecourt refactor
        ArrayList<FuelPoint> fuelPoints = forecourtLogic.getConfiguredFuelPoints();

        if (fuelPointsParam != null && fuelPointsParam.length > 0) {
            List<String> fuelPointsParamList = Arrays.asList(fuelPointsParam);
            fuelPoints =
                    fuelPoints.stream()
                            .filter(e -> fuelPointsParamList.contains(e.getId()))
                            .collect(Collectors
                                    .toCollection(ArrayList::new));
        }

        return fuelPoints;
    }

    public void publishKioskTransaction(Transaction transaction){
        try {
            transactionLogic.publishKioskTransaction(transaction);
        } catch (ApiException e) {
            Log.e("Publish Transaction Failed", e);
            this.serviceEvents.systemError("Publish Transaction Failed", e);
        }
    }

    public void checkOrphanTransactions() throws IOException, ConnectionException {
        transactionLogic.checkOrphanTransactions();
    }

    public void Error(Throwable throwable) {
        forecourtLogic.Error(throwable);
    }

    public void exportKioskTransactions() {
        // create an instance of POSMaster
        try {
            POSMaster p = new POSMaster
                    (
                            configurationRepository.getConfigurationByName("posmaster_host").getValue(),
                            configurationRepository.getConfigurationByName("store_id").getValue(),
                            configurationRepository.getConfigurationByName("posmaster_username").getValue(),
                            configurationRepository.getConfigurationByName("posmaster_password").getValue(),
                            configurationRepository.getConfigurationByName("ftp_host").getValue(),
                            configurationRepository.getConfigurationByName("ftp_username").getValue(),
                            configurationRepository.getConfigurationByName("ftp_password").getValue()
                    );
            //get data from local SQL
            // PosMaster - Orders
            // PosMaster - Order Items
            // PosMaster - Transactions
            // PosMaster - FuelTransactions
            OPTExport exportData = this.transactionRepository.getPosMasterOrders(configurationRepository.getConfigurationByName("pos_id").getValue());
            // send transaction data to POSMaster - if there are transactions to send
            if (exportData.getTransactions().size() > 0) {
                boolean uploadResult = p.TransactionDataUpload(getBaseContext(), exportData);
                if (uploadResult) {
                    transactionRepository.setTransactionExported(exportData.getOrderIds());
                }
            }
        } catch (MalformedURLException e) {
            Log.e("exportKioskTransactions", e);
        }

    }

    public void publishKioskTransactions() {
        if (!this.kioskExporting) {
            this.kioskExporting = true;
            for (Transaction transaction : this.transactionRepository.getTransactionsForPublish()) {
                if(transaction!=null)
                    publishKioskTransaction(transaction);
            }
            this.kioskExporting = false;
        }
    }

    public void uploadKioskDatabase() throws IOException, ApiException {
        String zipFile = createDbBackupZip();
        uploadFileKiosk(zipFile);
    }
    private void uploadFileKiosk(String filePath) throws ApiException {
        File file = new File(filePath);
        RequestBody requestFile =
                RequestBody.create(
                        MediaType.parse("application/x-zip-compressed"),
                        file
                );

        MultipartBody.Part body =
                MultipartBody.Part.createFormData("file", file.getName(), requestFile);

        try {
            ApiServiceHelper.executeAPI(this.kioskApiService.upload(body));
        }catch (ApiException e){
            throw e;
        }finally {
            file.delete();
        }
    }

    private String createDbBackupZip() throws IOException {
        File file = getDatabasePath("opt-db");
        file = new File(file.getParent());
        File[] files = file.listFiles();
        String zipFileName = file.getAbsolutePath() + "/" + (new SimpleDateFormat("yyyy-MM-dd_HHmmss")).format(new Date()) + ".zip";
        Integer BUFFER = 1024;
        BufferedInputStream origin = null;
        FileOutputStream dest = new FileOutputStream(zipFileName);
        ZipOutputStream out = new ZipOutputStream(new BufferedOutputStream(
                dest));
        byte data[] = new byte[BUFFER];
        for (File f : files) {
            Log.v("Compress", "Adding: " + f.getPath());
            FileInputStream fi = new FileInputStream(f);

            origin = new BufferedInputStream(fi, BUFFER);

            ZipEntry entry = new ZipEntry(f.getName());
            out.putNextEntry(entry);
            int count;

            while ((count = origin.read(data, 0, BUFFER)) != -1) {
                out.write(data, 0, count);
            }
            origin.close();
        }

        out.close();

        return zipFileName;
    }

    private void ignoreSSL() {
        if(BuildConfig.DEBUG) {
            try {
                // Create a trust manager that does not validate certificate chains
                TrustManager[] trustAllCerts = new TrustManager[]{
                        new X509TrustManager() {
                            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                                return new X509Certificate[0];
                            }

                            public void checkClientTrusted(
                                    java.security.cert.X509Certificate[] certs, String authType) {
                            }

                            public void checkServerTrusted(
                                    java.security.cert.X509Certificate[] certs, String authType) {
                            }
                        }
                };

                // Install the all-trusting trust manager
                SSLContext sc = SSLContext.getInstance("SSL");
                sc.init(null, trustAllCerts, new java.security.SecureRandom());
                HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

                // Create a hostname verifier that does not validate hostname
                HostnameVerifier allHostsValid = new HostnameVerifier() {
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                };

                // Install the all-trusting host verifier
                HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            } catch (Exception e) {
                Log.e("ignoreSSL", e);
            }
        }
    }

}