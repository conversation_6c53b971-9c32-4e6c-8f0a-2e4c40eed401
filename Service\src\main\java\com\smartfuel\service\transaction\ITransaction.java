package com.smartfuel.service.transaction;

import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.sqlite.models.Transaction;
import com.smartfuel.service.models.transaction.IM30Transaction;

public interface ITransaction {
    void Initialise(IM30Transaction transactionData) throws ConnectionException;
    void Capture(String externalDeviceToken, Transaction transactionData) throws ConnectionException;
    void Cancel(IM30Transaction transactionData) throws ConnectionException;
    void Reversal(String externalDeviceToken, Transaction transactionData) throws ConnectionException;
    void Monitor(final ITransactionCallbackManager callBack) throws ConnectionException;
}
