package com.smartfuel.service.messages.forecourt.POSTEC.request;

/**
 * To deactivate the PCC connection for the client with the specified node address.
 * This closes the communications path between the client and the PCC and will stop
 * accepting request messages from the client.
 */
public class LogoutRequest extends BaseRequest {


    public LogoutRequest(int nodeNumber){
        super(nodeNumber,2,1,"Logout_Client_Node");
    }

    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
