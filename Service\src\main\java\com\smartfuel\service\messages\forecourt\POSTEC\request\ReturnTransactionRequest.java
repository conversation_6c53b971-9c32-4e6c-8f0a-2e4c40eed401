package com.smartfuel.service.messages.forecourt.POSTEC.request;

/**
 *  To request the PCC to return the transaction data to the Sale or Memory buffer for a
 * dispenser.
 *  This would normally be done if the sale is voided.
 */
public class ReturnTransactionRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] transactionType = new char[1];
    public ReturnTransactionRequest(int nodeNumber, int dispenserNumber){
        super(nodeNumber,7,3,"Return_Transaction");
        this.dispenserNumber = String.format("%02d", dispenserNumber).toCharArray();
        this.transactionType = "M".toCharArray();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.transactionType);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
