package com.smartfuel.service.messages.forecourt.POSTEC.request;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.TransactionType;

/**
 * To request the PCC to clear the transaction data and free up the Sale or Memory buffer for
 * a dispenser.
 * This would normally be sent after the finalisation process.(Download Transaction)
 */
public class ConfirmTransactionRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] transactionType = new char[1];

    public ConfirmTransactionRequest(int nodeNumber, int dispenserNumber, TransactionType transactionType){
        super(nodeNumber, 6,3,"Confirm_Transaction");
        this.dispenserNumber = String.format("%02d",dispenserNumber).toCharArray();
        this.transactionType = transactionType.getType();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.transactionType);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
