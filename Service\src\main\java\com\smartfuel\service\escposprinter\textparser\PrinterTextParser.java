package com.smartfuel.service.escposprinter.textparser;

import com.smartfuel.service.escposprinter.EscPosPrinter;
import com.smartfuel.service.escposprinter.EscPosPrinterCommands;
import com.smartfuel.service.escposprinter.exceptions.EscPosBarcodeException;
import com.smartfuel.service.escposprinter.exceptions.EscPosEncodingException;
import com.smartfuel.service.escposprinter.exceptions.EscPosParserException;

public class PrinterTextParser {
    public static final String ATTR_BARCODE_HEIGHT = "height";

    public static final String ATTR_BARCODE_TEXT_POSITION = "text";

    public static final String ATTR_BARCODE_TEXT_POSITION_ABOVE = "above";

    public static final String ATTR_BARCODE_TEXT_POSITION_BELOW = "below";

    public static final String ATTR_BARCODE_TEXT_POSITION_NONE = "none";

    public static final String ATTR_BARCODE_TYPE = "type";

    public static final String ATTR_BARCODE_TYPE_128 = "128";

    public static final String ATTR_BARCODE_TYPE_EAN13 = "ean13";

    public static final String ATTR_BARCODE_TYPE_EAN8 = "ean8";

    public static final String ATTR_BARCODE_TYPE_UPCA = "upca";

    public static final String ATTR_BARCODE_TYPE_UPCE = "upce";

    public static final String ATTR_BARCODE_WIDTH = "width";

    public static final String ATTR_FORMAT_TEXT_FONT_COLOR = "color";

    public static final String ATTR_FORMAT_TEXT_FONT_COLOR_BG_BLACK = "bg-black";

    public static final String ATTR_FORMAT_TEXT_FONT_COLOR_BG_RED = "bg-red";

    public static final String ATTR_FORMAT_TEXT_FONT_COLOR_BLACK = "black";

    public static final String ATTR_FORMAT_TEXT_FONT_COLOR_RED = "red";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE = "size";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE_BIG = "big";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE_BIG_2 = "big-2";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE_BIG_3 = "big-3";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE_BIG_4 = "big-4";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE_BIG_5 = "big-5";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE_BIG_6 = "big-6";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE_NORMAL = "normal";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE_TALL = "tall";

    public static final String ATTR_FORMAT_TEXT_FONT_SIZE_WIDE = "wide";

    public static final String ATTR_FORMAT_TEXT_UNDERLINE_TYPE = "type";

    public static final String ATTR_FORMAT_TEXT_UNDERLINE_TYPE_DOUBLE = "double";

    public static final String ATTR_FORMAT_TEXT_UNDERLINE_TYPE_NORMAL = "normal";

    public static final String ATTR_QRCODE_SIZE = "size";

    public static final String[] TAGS_ALIGN = new String[] { "L", "C", "R" };

    public static final String TAGS_ALIGN_CENTER = "C";

    public static final String TAGS_ALIGN_LEFT = "L";

    public static final String TAGS_ALIGN_RIGHT = "R";

    public static final String TAGS_BARCODE = "barcode";

    public static final String[] TAGS_FORMAT_TEXT = new String[] { "font", "b", "u" };

    public static final String TAGS_FORMAT_TEXT_BOLD = "b";

    public static final String TAGS_FORMAT_TEXT_FONT = "font";

    public static final String TAGS_FORMAT_TEXT_UNDERLINE = "u";

    public static final String TAGS_IMAGE = "img";

    public static final String TAGS_QRCODE = "qrcode";

    private static String regexAlignTags;

    private EscPosPrinter printer;

    private String text = "";

    private byte[][] textBold = new byte[][] { EscPosPrinterCommands.TEXT_WEIGHT_NORMAL };

    private byte[][] textColor = new byte[][] { EscPosPrinterCommands.TEXT_COLOR_BLACK };

    private byte[][] textDoubleStrike = new byte[][] { EscPosPrinterCommands.TEXT_DOUBLE_STRIKE_OFF };

    private byte[][] textReverseColor = new byte[][] { EscPosPrinterCommands.TEXT_COLOR_REVERSE_OFF };

    private byte[][] textSize = new byte[][] { EscPosPrinterCommands.TEXT_SIZE_NORMAL };

    private byte[][] textUnderline = new byte[][] { EscPosPrinterCommands.TEXT_UNDERLINE_OFF };

    public PrinterTextParser(EscPosPrinter paramEscPosPrinter) {
        this.printer = paramEscPosPrinter;
    }

    public static byte[][] arrayByteDropLast(byte[][] paramArrayOfbyte) {
        if (paramArrayOfbyte.length == 0)
            return paramArrayOfbyte;
        byte[][] arrayOfByte = new byte[paramArrayOfbyte.length - 1][];
        System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 0, arrayOfByte.length);
        return arrayOfByte;
    }

    public static byte[][] arrayBytePush(byte[][] paramArrayOfbyte, byte[] paramArrayOfbyte1) {
        byte[][] arrayOfByte = new byte[paramArrayOfbyte.length + 1][];
        System.arraycopy(paramArrayOfbyte, 0, arrayOfByte, 0, paramArrayOfbyte.length);
        arrayOfByte[paramArrayOfbyte.length] = paramArrayOfbyte1;
        return arrayOfByte;
    }

    public static String getRegexAlignTags() {
        if (regexAlignTags == null) {
            StringBuilder stringBuilder = new StringBuilder();
            byte b = 0;
            while (true) {
                String[] arrayOfString = TAGS_ALIGN;
                if (b < arrayOfString.length) {
                    stringBuilder.append("|\\[").append(arrayOfString[b]).append("\\]");
                    b++;
                    continue;
                }
                regexAlignTags = stringBuilder.toString().substring(1);
                break;
            }
        }
        return regexAlignTags;
    }

    public static boolean isTagTextFormat(String paramString) {
        String str = paramString;
        if (paramString.substring(0, 1).equals("/"))
            str = paramString.substring(1);
        String[] arrayOfString = TAGS_FORMAT_TEXT;
        int i = arrayOfString.length;
        for (byte b = 0; b < i; b++) {
            if (arrayOfString[b].equals(str))
                return true;
        }
        return false;
    }

    public PrinterTextParser addTextBold(byte[] paramArrayOfbyte) {
        this.textBold = arrayBytePush(this.textBold, paramArrayOfbyte);
        return this;
    }

    public PrinterTextParser addTextColor(byte[] paramArrayOfbyte) {
        this.textColor = arrayBytePush(this.textColor, paramArrayOfbyte);
        return this;
    }

    public PrinterTextParser addTextDoubleStrike(byte[] paramArrayOfbyte) {
        this.textDoubleStrike = arrayBytePush(this.textDoubleStrike, paramArrayOfbyte);
        return this;
    }

    public PrinterTextParser addTextReverseColor(byte[] paramArrayOfbyte) {
        this.textReverseColor = arrayBytePush(this.textReverseColor, paramArrayOfbyte);
        return this;
    }

    public PrinterTextParser addTextSize(byte[] paramArrayOfbyte) {
        this.textSize = arrayBytePush(this.textSize, paramArrayOfbyte);
        return this;
    }

    public PrinterTextParser addTextUnderline(byte[] paramArrayOfbyte) {
        this.textUnderline = arrayBytePush(this.textUnderline, paramArrayOfbyte);
        return this;
    }

    public PrinterTextParser dropLastTextColor() {
        byte[][] arrayOfByte = this.textColor;
        if (arrayOfByte.length > 1)
            this.textColor = arrayByteDropLast(arrayOfByte);
        return this;
    }

    public PrinterTextParser dropLastTextDoubleStrike() {
        byte[][] arrayOfByte = this.textDoubleStrike;
        if (arrayOfByte.length > 1)
            this.textDoubleStrike = arrayByteDropLast(arrayOfByte);
        return this;
    }

    public PrinterTextParser dropLastTextReverseColor() {
        byte[][] arrayOfByte = this.textReverseColor;
        if (arrayOfByte.length > 1)
            this.textReverseColor = arrayByteDropLast(arrayOfByte);
        return this;
    }

    public PrinterTextParser dropLastTextSize() {
        byte[][] arrayOfByte = this.textSize;
        if (arrayOfByte.length > 1)
            this.textSize = arrayByteDropLast(arrayOfByte);
        return this;
    }

    public PrinterTextParser dropLastTextUnderline() {
        byte[][] arrayOfByte = this.textUnderline;
        if (arrayOfByte.length > 1)
            this.textUnderline = arrayByteDropLast(arrayOfByte);
        return this;
    }

    public PrinterTextParser dropTextBold() {
        byte[][] arrayOfByte = this.textBold;
        if (arrayOfByte.length > 1)
            this.textBold = arrayByteDropLast(arrayOfByte);
        return this;
    }

    public byte[] getLastTextBold() {
        byte[][] arrayOfByte = this.textBold;
        return arrayOfByte[arrayOfByte.length - 1];
    }

    public byte[] getLastTextColor() {
        byte[][] arrayOfByte = this.textColor;
        return arrayOfByte[arrayOfByte.length - 1];
    }

    public byte[] getLastTextDoubleStrike() {
        byte[][] arrayOfByte = this.textDoubleStrike;
        return arrayOfByte[arrayOfByte.length - 1];
    }

    public byte[] getLastTextReverseColor() {
        byte[][] arrayOfByte = this.textReverseColor;
        return arrayOfByte[arrayOfByte.length - 1];
    }

    public byte[] getLastTextSize() {
        byte[][] arrayOfByte = this.textSize;
        return arrayOfByte[arrayOfByte.length - 1];
    }

    public byte[] getLastTextUnderline() {
        byte[][] arrayOfByte = this.textUnderline;
        return arrayOfByte[arrayOfByte.length - 1];
    }

    public EscPosPrinter getPrinter() {
        return this.printer;
    }

    public PrinterTextParserLine[] parse() throws EscPosParserException, EscPosBarcodeException, EscPosEncodingException {
        String[] arrayOfString = this.text.split("\n|\r\n");
        PrinterTextParserLine[] arrayOfPrinterTextParserLine = new PrinterTextParserLine[arrayOfString.length];
        byte b1 = 0;
        int i = arrayOfString.length;
        byte b2 = 0;
        while (b2 < i) {
            arrayOfPrinterTextParserLine[b1] = new PrinterTextParserLine(this, arrayOfString[b2]);
            b2++;
            b1++;
        }
        return arrayOfPrinterTextParserLine;
    }

    public PrinterTextParser setFormattedText(String paramString) {
        this.text = paramString;
        return this;
    }
}

