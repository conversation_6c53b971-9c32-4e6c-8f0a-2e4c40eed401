package com.smartfuel.service.escposprinter.barcode;


import com.smartfuel.service.escposprinter.EscPosPrinterSize;
import com.smartfuel.service.escposprinter.exceptions.EscPosBarcodeException;

public class BarcodeEAN13 extends BarcodeNumber {
    public BarcodeEAN13(EscPosPrinterSize paramEscPosPrinterSize, String paramString, float paramFloat1, float paramFloat2, int paramInt) throws EscPosBarcodeException {
        super(paramEscPosPrinterSize, 67, paramString, paramFloat1, paramFloat2, paramInt);
    }

    public int getCodeLength() {
        return 13;
    }
}
