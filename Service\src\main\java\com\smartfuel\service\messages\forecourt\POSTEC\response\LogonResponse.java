package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

/**
 * Login_Client_Node: 01 001 0123 09 2000 SB1.0.9 S6.30 03/10/03 DEVELOP! H4.3 Ser#00000141AFE4 0
 *  01 = client node number
 *  001 = request number
 *  0123 = sequence number
 *  09 = number of parameters
 *  2000 = socket success code (2) + PCC success code (000)
 *  SB1.0.9 = Forecourt server version
 *  S6.30 = PCC software version
 *  03/10/03 = PCC software date
 *  DEVELOP! = PCC software country
 *  H4.3 = PCC hardware version
 *  Ser#00000141AFE4 = PCC hardware key serial number
 *  0 = Serial or LAN mode
 */
public class LogonResponse extends BaseResponse {
    private String serverVersion;
    private String PCCSoftwareVersion;
    private String PCCSoftwareDate;
    private String PCCSoftwareCountry;
    private String PCCHardwareVersion;
    private String PCCSerialKey;
    private int networkType;
    public LogonResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.serverVersion = super.responseData[5];
        this.PCCSoftwareVersion = super.responseData[6];
        this.PCCSoftwareDate = super.responseData[7];
        this.PCCSoftwareCountry = super.responseData[8];
        this.PCCHardwareVersion = super.responseData[9];
        this.PCCSerialKey = super.responseData[10];
        this.networkType = Integer.parseInt(super.responseData[11]);
    }
    public String getServerVersion() { return serverVersion; }
    public String getPCCSoftwareVersion() { return  PCCSoftwareVersion; }
    public String getPCCSoftwareDate() { return PCCSoftwareDate; }
    public String getPCCSoftwareCountry() { return PCCSoftwareCountry; }
    public String getPCCHardwareVersion() { return PCCHardwareVersion; }
    public String getPCCSerialKey() { return PCCSerialKey; }
    public int getNetworkType() { return networkType; }
}
