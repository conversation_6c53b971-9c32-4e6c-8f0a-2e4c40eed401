package com.smartfuel.service.messages.forecourt.POSTEC.request;


public class DispenserInfoRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] hoseNumber = new char[1];

    public DispenserInfoRequest(int nodeNumber, int dispenserNumber, int hoseNumber){
        super(nodeNumber,75,3,"Req_Dispenser_Info");
        this.dispenserNumber = String.format("%02d",dispenserNumber).toCharArray();
        this.hoseNumber = (hoseNumber + "").toCharArray();
    }

    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.hoseNumber);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
