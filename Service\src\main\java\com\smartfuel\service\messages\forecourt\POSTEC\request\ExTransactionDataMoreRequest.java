package com.smartfuel.service.messages.forecourt.POSTEC.request;


public class ExTransactionDataMoreRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] sequenceNumber = new char[6];

    public ExTransactionDataMoreRequest (int nodeNumber, int dispenserNumber, int sequenceNumber){
        super(nodeNumber,87,3,"Req_Ext_Trans_Data_More");
        this.dispenserNumber = String.format("%02d", dispenserNumber).toCharArray();
        this.sequenceNumber = String.format("%06d", sequenceNumber).toCharArray();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.sequenceNumber);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
