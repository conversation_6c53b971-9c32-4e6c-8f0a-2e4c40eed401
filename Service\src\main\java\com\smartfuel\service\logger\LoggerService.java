package com.smartfuel.service.logger;

import android.content.Context;
import android.util.Log;

import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.smartfuel.service.IKioskApiService;
import com.smartfuel.service.diagnostics.DiagnosticService;
import com.smartfuel.service.repository.IConfigurationRepository;

public class LoggerService {

    private Context context;
    private final LogMailer logMailer;
    private final LogcatReader logcatReader;
    private final LogInfo logInfo;

    private static LoggerService instance;
    private static String Version;

    private static LoggerService getInstance(){
        return instance;
    }

    private LoggerService(Context context){
        this.context = context;
        logMailer = new LogMailer(Version);
        logcatReader = new LogcatReader(context);
        logInfo = new LogInfo(Version);
        DiagnosticService.setLogMailer(logMailer);
    }

    public static void setAppVersion(String version){
        Version = version;
    }

    public static void setup(Context context){
        if(instance == null)
            instance = new LoggerService(context);
    }

    public static void setup(IConfigurationRepository configurationRepository, IKioskApiService kioskApiService){
        instance.logMailer.setup(instance.context, configurationRepository);
        instance.logInfo.setup(instance.context, kioskApiService);
        instance.logInfo.start();
    }

    public static void log(LogcatReader.Level level, String tag, String message, Throwable throwable){
        switch (level){
            case WARNING:
                Log.w(tag, message, throwable);
                break;
            case DEBUG:
                Log.d(tag, message, throwable);
                break;
            case ERROR:
                Log.e(tag, message, throwable);
                FirebaseCrashlytics.getInstance().log(message);
                if(!DiagnosticService.resolve(throwable, true))
                    if(getInstance() != null && getInstance().logcatReader != null)
                        getInstance().logMailer.sendEmail(tag, throwable, getInstance().logcatReader);
                break;
            case INFO:
                Log.i(tag, message, throwable);
                break;
            case VERBOSE:
                Log.v(tag, message, throwable);
                break;
        }
        if(getInstance() != null && getInstance().logcatReader != null)
            getInstance().logcatReader.add(level, tag, message, throwable);
    }

}
