package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;
/*
            0. IP Address:                 ***************
            1. Broadcast Node Number:      99
            2. Request Number:             000
            3. Sequence Number:            3336
            4. Parameter Count:            12
            5. Socket Status:              1000
            6. Dispenser Number:           01
            7. Hose Number:                0
            8. Grade Number:               12
            9. Pump Status:                02
            10. Sale Status:                048
            11. Dollar Sales:               00000000
            12. Memory:                     115
            13. Memory Dollars:             00000485
            14. UnFinalised Trn Count:      00
            15. 105555_1_Dispenser_status_update
         */
public class DispenserStatusUpdateResponse extends BaseResponse{
    private final int dispenserNumber;
    private final int gradeId;
    private final long amount;
    public DispenserStatusUpdateResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.UDP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[6]);
        this.gradeId = Integer.parseInt(super.responseData[8]);
        this.amount = Long.parseLong(super.responseData[13]);

    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }

    public int getGradeId() {
        return gradeId;
    }

    public long getAmount() {
        return amount;
    }
}
