package com.smartfuel.service.transaction;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;

import com.smartfuel.service.connection.terminal.FakeServiceConnection;
import com.smartfuel.service.logger.Log;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.connection.terminal.PAXServiceConnection;
import com.smartfuel.service.connection.usb.UsbConnection;
import com.smartfuel.service.connection.usb.UsbPrinterConnection;
import com.smartfuel.service.enums.transaction.ProcessType;
import com.smartfuel.service.enums.transaction.TransactionServiceType;
import com.smartfuel.service.sqlite.models.Transaction;
import com.smartfuel.service.models.transaction.IM30Transaction;
import com.smartfuel.service.models.transaction.IM30TransactionBroadcast;
import com.smartfuel.service.pax.Terminal;

import java.io.IOException;
import java.util.regex.Pattern;


public class CardTransactionService implements ITransaction {

    private static final String ACTION_USB_PERMISSION = "com.smartfuel.USB_PERMISSION";

    private UsbConnection usbConnection;
    private UsbManager myUsbManager;
    private UsbDevice myUsbDevice;
    private Context myServiceContext;
    private Terminal im30Terminal;
    private TransactionServiceType serviceType;

    public CardTransactionService(TransactionServiceType serviceType, Context serviceContext) throws ConnectionException {
        this.serviceType = serviceType;
        switch (serviceType) {
            case USB:
                myServiceContext = serviceContext;
                usbConnection = UsbPrinterConnection.selectPAXIM30USB(serviceContext);
                myUsbManager = (UsbManager) serviceContext.getSystemService(Context.USB_SERVICE);
                try {
                    myUsbDevice = usbConnection.getDevice();
                    checkUSBPermission();
                    im30Terminal = new Terminal(new UsbConnection(myUsbManager, myUsbDevice));
                }
                catch (Exception e){
                    throw new ConnectionException("IM30 Connection Failed", e);
                }
                break;
            case PAXService:
                im30Terminal = new Terminal(PAXServiceConnection.getInstance(serviceContext));
                break;
            case Fake:
                im30Terminal = new Terminal(FakeServiceConnection.getInstance(serviceContext));
                break;
        }
    }

    public boolean isConnected(){
        return im30Terminal.isConnected();
    }

    public void reconnect() throws ConnectionException {
        im30Terminal.connectTerminal();
    }

    private void checkUSBPermission() throws ConnectionException {
        if(serviceType == TransactionServiceType.USB) {
            PendingIntent permissionIntent = PendingIntent.getBroadcast(
                    myServiceContext,
                    0,
                    new Intent(this.ACTION_USB_PERMISSION),
                    android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S ? PendingIntent.FLAG_MUTABLE : 0
            );
            myUsbManager.requestPermission(usbConnection.getDevice(), permissionIntent);

            if (usbConnection == null || myUsbManager == null) {
                //Raise an exception here since USB is not available
                throw new ConnectionException("Terminal not ready");
            }
        }
    }

    @Override
    public void Initialise(IM30Transaction transactionData) throws ConnectionException {

        checkUSBPermission();

        synchronized (this) {
            im30Terminal.sendTerminalCommand(transactionData);
        }
    }

    @Override
    public void Capture(String externalDeviceToken, Transaction transactionData) throws ConnectionException {

        checkUSBPermission();

        synchronized (this) {
            IM30Transaction trx = new IM30Transaction(externalDeviceToken, String.valueOf(transactionData.getId()), transactionData.getFinalAmount(), 36, ProcessType.CAPTURE);
            im30Terminal.sendTerminalCommand(trx);
        }
    }

    @Override
    public void Cancel(IM30Transaction transactionData) throws ConnectionException {

        checkUSBPermission();

        synchronized (this) {
            im30Terminal.sendTerminalCommand(transactionData);
        }
    }

    @Override
    public void Reversal(String externalDeviceToken, Transaction transactionData) throws ConnectionException {

        checkUSBPermission();

        synchronized (this) {
            IM30Transaction trx = new IM30Transaction(externalDeviceToken, String.valueOf(transactionData.getId()), transactionData.getFinalAmount(), 36, ProcessType.REVERSAL);
            im30Terminal.sendTerminalCommand(trx);
        }
    }

    @Override
    public void Monitor(final ITransactionCallbackManager callBack) throws ConnectionException {

        checkUSBPermission();

        synchronized (this) {
            im30Terminal.readTerminalMessages();
            ObjectMapper transactionMessageMapping = new ObjectMapper();
            String messageBody = im30Terminal.terminalCommands.Data;

            if (!messageBody.isEmpty()) {
                messageBody = messageBody.replace("}{", "}||{");
                String[] msgsBody = messageBody.split(Pattern.quote("||"));
                for (String msg: msgsBody) {
                    IM30TransactionBroadcast transactionBroadcast = null;
                    try {
                        transactionBroadcast = transactionMessageMapping.getFactory().createParser(msg).readValueAs(IM30TransactionBroadcast.class);
                    } catch (IOException e) {
                        throw new ConnectionException(e);
                    }
                    // handle any response messages here and raise the relevant events on the OPT service
                    // map the response JSON to the IM30TransactionBroadcast object and evaluate
                    switch (transactionBroadcast.getState()) {
                        case 0: //Rejected by Terminal Reader
                            // events.systemError("Card Terminal Failed",new RuntimeException("Card Terminal Refused Transaction"));
                            throw new ConnectionException(new Exception(transactionBroadcast.getErrorMessage()));
                            //break;
                        case 1: //Accepted by Terminal Reader
                            // No action required. The IM30State will manage the transaction lifecycle and process flow
                            break;
                    }
                    // A transaction will progress through various states as it is processed by the terminal
                    switch (transactionBroadcast.getIm30State()) {
                        // update fuel transaction status
                        case 0:  // - Awaiting Card Read
                            break;
                        case 3:  // - Sending Online
                        case 4:  // - Sending Online Complete
                        case 7:  // – Sending Capture
                        case 10: // – Sending Reversal
                            Log.i("CardTransaction", "RAISE: CardTransactionUpdate");
                            callBack.CardTransactionUpdate(transactionBroadcast);
                            break;
                        case 1: // - Card Read Success
                            Log.i("CardTransaction", "RAISE: CardTransactionReadComplete");
                            // raise event which will move to the transaction processing activity UI
                            callBack.CardTransactionReadComplete(transactionBroadcast);
                            break;
                        case 2: // - Card Read Failed
                        case 5: // - Sending Online Failed
                            Log.i("CardTransaction", "RAISE: CardTransactionTerminalFailed");
                            // raise an error event which will cancel the transaction and show error UI
                            callBack.CardTransactionTerminalFailed(transactionBroadcast);
                            break;
                        case 6: // - Pre-Authorization Complete
                            // evaluate the transaction status
                            switch (transactionBroadcast.getTransactionStatus()) {
                                case 0:     //None
                                    Log.i("CardTransaction", "RAISE: NOACTION REQUIRED");
                                    break; // NO ACTION REQUIRED
                                case 1:     //Approved
                                    // success - raise event which will complete a pump authorisation
                                    Log.i("CardTransaction", "RAISE: CardTransactionAuthorised");
                                    callBack.CardTransactionAuthorised(transactionBroadcast);
                                    break;
                                case 2:     //Declined
                                    // failed - raise event which will cancel fuel transaction
                                    Log.i("CardTransaction", "RAISE: CardTransactionDeclined");
                                    callBack.CardTransactionDeclined(transactionBroadcast);
                                    break;
                                case 99:    //Failed
                                    Log.i("CardTransaction", "RAISE: CardTransactionTerminalFailed");
                                    callBack.CardTransactionTerminalFailed(transactionBroadcast);
                                    break;
                            }
                            break;
                        case 8: // – Sending Capture Complete
                            Log.i("CardTransaction", "RAISE: CardTransactionCaptureComplete");
                            // raise event which will complete the fuel transaction
                            callBack.CardTransactionCaptureComplete(transactionBroadcast);
                            break;
                        case 9: // – Sending Capture Failed
                            Log.i("CardTransaction", "RAISE: CardTransactionCaptureFailed");
                            // raise event which will re-try the transaction capture
                            callBack.CardTransactionCaptureFailed(transactionBroadcast);
                            break;
                        case 11: // – Sending Reversal Complete
                            Log.i("CardTransaction", "RAISE: CardTransactionReversalComplete");
                            // raise event which will complete the fuel transaction reversal
                            callBack.CardTransactionReversalComplete(transactionBroadcast);
                            break;
                        case 12: // – Sending Reversal Failed
                            Log.i("CardTransaction", "RAISE: CardTransactionReversalFailed");
                            //raise event which will re-try the transaction reversal
                            callBack.CardTransactionReversalFailed(transactionBroadcast);
                            break;
                        case 98: // - Cancelled
                            Log.i("CardTransaction", "RAISE: CardTransactionCancelled");
                            callBack.CardTransactionCancelled(transactionBroadcast);
                            break;
                        case 99: // - Timeout
                            Log.i("CardTransaction", "RAISE: CardTransactionTimeout");
                            callBack.CardTransactionTimeout(transactionBroadcast);
                            break;

                    }
                    switch (transactionBroadcast.getReceiptState()) {
                        // get receipt card
                        case 0:  // - Awaiting Card Read
                            break;
                        case 1:  // - Card Read Success
                            Log.i("CardTransaction", "RAISE: CardReceiptReadComplete");
                            // raise event which will move to the transaction processing activity UI
                            callBack.CardReceiptReadComplete(transactionBroadcast);
                            break;
                        case 2:  // - Card Read Failed
                            Log.i("CardTransaction", "RAISE: CardReceiptTerminalFailed (Card Read Failed)");
                            // raise an error event which will cancel the transaction and show error UI
                            callBack.CardReceiptTerminalFailed(transactionBroadcast);
                            break;
                        case 98: // - Cancelled
                            Log.i("CardTransaction", "RAISE: CardReceiptCancelled");
                            callBack.CardReceiptCancelled(transactionBroadcast);
                            break;
                        case 99: // - Timeout
                            Log.i("CardTransaction", "RAISE: CardReceiptTimeout");
                            callBack.CardReceiptTimeout(transactionBroadcast);
                            break;
                    }
                    switch (transactionBroadcast.getMagneticState()) {
                        // get magnetic card
                        case 0:  // - Awaiting Card Read
                            break;
                        case 1:  // - Card Read Success
                            Log.i("CardTransaction", "RAISE: CardMagneticReadComplete");
                            // raise event which will move to the transaction processing activity UI
                            callBack.CardMagneticReadComplete(transactionBroadcast);
                            break;
                        case 2:  // - Card Read Failed
                            Log.i("CardTransaction", "RAISE: CardMagneticTerminalFailed");
                            // raise an error event which will cancel the transaction and show error UI
                            callBack.CardMagneticTerminalFailed(transactionBroadcast);
                            break;
                        case 98: // - Cancelled
                            Log.i("CardTransaction", "RAISE: CardMagneticCancelled");
                            callBack.CardMagneticCancelled(transactionBroadcast);
                            break;
                        case 99: // - Timeout
                            Log.i("CardTransaction", "RAISE: CardMagneticTimeout");
                            callBack.CardMagneticTimeout(transactionBroadcast);
                            break;
                    }
                }
                //Log.i("CardTransaction", im30Terminal.terminalCommands.Data);
            }
        }
    }
}
