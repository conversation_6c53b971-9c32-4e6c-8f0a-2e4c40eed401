package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;
import org.json.JSONException;
public class StatusFuellingPointRequest {
    private final StatusFuellingPointRequestData data;

    private final String name = "close_Fp_req";

    private final String subCode = "00H";

    public StatusFuellingPointRequest(String fuelpointData) {
        this.data = new StatusFuellingPointRequestData(fuelpointData);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class StatusFuellingPointRequestData {
        private final String FpId;

        StatusFuellingPointRequestData(String data) {
            this.FpId = data;
        }
    }
}
