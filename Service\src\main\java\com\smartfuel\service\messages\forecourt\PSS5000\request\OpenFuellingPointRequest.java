package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;
import org.json.JSONException;
public class OpenFuellingPointRequest {
    private final FcOpenFpRequestData data;

    private final String name = "open_Fp_req";

    private final String subCode = "00H";

    public OpenFuellingPointRequest(String paramString1, String paramString2, int paramInt) {
        this.data = new FcOpenFpRequestData(paramString1, paramString2, paramInt);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class FcOpenFpRequestData {
        private final String FpId;

        private final Integer FpOperationModeNo;

        private final String PosId;

        FcOpenFpRequestData(String fuelPointId, String posId, int operationModeNumber) {
            this.FpId = fuelPointId;
            this.PosId = posId;
            this.FpOperationModeNo = Integer.valueOf(operationModeNumber);
        }
    }
}
