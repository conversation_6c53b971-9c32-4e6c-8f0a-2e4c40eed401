package com.smartfuel.service;


import com.smartfuel.service.models.kiosk.request.AuthenticationRequest;
import com.smartfuel.service.models.kiosk.request.InfoRequest;
import com.smartfuel.service.models.kiosk.request.RewardCardValidRequest;
import com.smartfuel.service.models.kiosk.request.TransactionAuditRequest;
import com.smartfuel.service.models.kiosk.request.TransactionRequest;
import com.smartfuel.service.models.kiosk.request.TransactionValidateRequest;
import com.smartfuel.service.models.kiosk.response.AccountCardCapabilities;
import com.smartfuel.service.models.kiosk.response.AccountCardRestrictionResponse;
import com.smartfuel.service.models.kiosk.response.AuthenticationResponse;
import com.smartfuel.service.models.kiosk.response.BaseResponse;
import com.smartfuel.service.models.kiosk.response.ConfigurationResponse;
import com.smartfuel.service.models.kiosk.response.RewardCardDiscountResponse;
import com.smartfuel.service.models.kiosk.response.TransactionResponse;
import com.smartfuel.service.models.kiosk.response.TransactionValidateResponse;
import com.smartfuel.service.models.kiosk.response.UserResponse;
import com.smartfuel.service.models.kiosk.response.ValidationResponse;
import com.smartfuel.service.models.transaction.CardReceipt;

import java.util.List;

import okhttp3.MultipartBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.Path;
public interface IKioskApiService {
    @POST("api/transaction")
    Call<TransactionResponse>
    addKioskTransaction(@Body TransactionRequest paramTransactionRequest);

    @POST("/api/kiosk/authenticate")
    Call<AuthenticationResponse>
    authenticateKiosk(@Body AuthenticationRequest paramAuthenticationRequest);

    @GET("/api/user/{userId}/kiosk")
    Call<UserResponse>
    getKioskUserDetail(@Path("userId") String paramString1);

    @GET("/api/kiosk/valid/{deviceid}")
    Call<ValidationResponse>
    validateKiosk(@Path("deviceid") String paramString1);

    @GET("/api/kiosk/config")
    Call<ConfigurationResponse>
    getKioskConfig();

    @POST("/api/transaction/receipt/email/{email}")
    Call<BaseResponse>
    sendTransactionReceipt(@Body CardReceipt paramString1, @Path("email") String paramString2);

    @Multipart
    @POST("/api/kiosk/upload")
    Call<BaseResponse>
    upload(@Part MultipartBody.Part file);

    @POST("/api/transactionaudit")
    Call<BaseResponse>
    sendTransactionAudit(@Body TransactionAuditRequest paramTransactionAuditRequest);

    @POST("/api/accountcard/validate")
    Call<TransactionValidateResponse>
    validateTransaction(@Body TransactionValidateRequest paramTransactionValidateRequest);

    @POST("/api/kiosk/info")
    Call<BaseResponse>
    sendKioskInfo(@Body InfoRequest paramInfoRequest);

    @POST("/api/rewardcard/valid")
    Call<RewardCardDiscountResponse>
    validRewardCard(@Body RewardCardValidRequest paramRewardCardValidRequest);

    @GET("/api/accountcard/restriction/{authorizationID}/{stan}/{tid}")
    Call<AccountCardRestrictionResponse>
    getRestrictions(@Path("authorizationID") String paramString1, @Path("stan") int paramString2, @Path("tid") String paramString3);

    @GET("/api/accountcard/capabilities/{authorizationID}/{stan}/{tid}")
    Call<AccountCardCapabilities>
    getCapabilities(@Path("authorizationID") String paramString1, @Path("stan") int paramString2, @Path("tid") String paramString3);
}

