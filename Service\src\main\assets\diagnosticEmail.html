<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Report - Kiosk Status</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f8f8;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1, h2 {
            color: #333333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dddddd;
        }
        th {
            background-color: #f5f5f5;
        }
        strong {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Kiosk Information</h2>
        <table>
            <tr>
                <td><strong>Name</strong></td>
                <td>{{kiosk}}</td>
            </tr>
            <tr>
                <td><strong>Version</strong></td>
                <td>{{version}}</td>
            </tr>
            <tr>
                <td><strong>Store</strong></td>
                <td>{{store}}</td>
            </tr>
            <tr>
                <td><strong>Date</strong></td>
                <td>{{date}}</td>
            </tr>
        </table>

        <h2>Exception Summary</h2>
        <table>
            <tr>
                <th>Exception Type</th>
                <th>Count</th>
            </tr>
            {{exceptions}}
        </table>

        <h2>Diagnostic Status</h2>
        <table>
            <tr>
                <th>Diagnostic Name</th>
                <th>Last Updated</th>
                <th>Status</th>
            </tr>
            {{diagnostics}}
        </table>
    </div>
</body>
</html>
