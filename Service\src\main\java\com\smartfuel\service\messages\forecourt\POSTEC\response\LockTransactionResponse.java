package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;
/*
01 088 0123 06 2000 02 062581 12 00050000 Lock_Transaction_Data
 01 = client node number
 088 = request number
 0123 = sequence number
 05 = number of parameters
 2000 = socket success code (2) + PCC success code (000)
 02 = Dispenser number
 062581 = Sequence number
 12 = PoS / Console adding sale item to Forecourt PoS
 00050000 = Max oil top up limit ($50.000)
 */
public class LockTransactionResponse extends BaseResponse{
private int dispenserNumber;
private String transactionNumber;
private int clientType;
private long topUpLimit;
    public LockTransactionResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
        this.transactionNumber = super.responseData[6];
        this.clientType = Integer.parseInt(super.responseData[7]);
        this.topUpLimit = Long.parseLong(super.responseData[8]);
    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public int getClientType() { return clientType;}

    public long getTopUpLimit(){ return topUpLimit;}
}
