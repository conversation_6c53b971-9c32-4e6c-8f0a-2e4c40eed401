package com.smartfuel.service.models.posmaster;

public class Transaction {
    public String getOrderId() {
        return OrderId;
    }

    public void setOrderId(String orderId) {
        OrderId = orderId;
    }

    public String getTransactionDate() {
        return TransactionDate;
    }

    public void setTransactionDate(String createdDate) {
        TransactionDate = createdDate;
    }

    public String getPaymentStatus() {
        return PaymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        PaymentStatus = paymentStatus;
    }

    public String getAmount() {
        return Amount;
    }

    public void setAmount(String amount) {
        Amount = amount;
    }

    public String getProcessed() {
        return Processed;
    }

    public void setProcessed(String processed) {
        Processed = processed;
    }

    public String getCardSignature() {
        return CardSignature;
    }

    public void setCardSignature(String cardSignature) {
        CardSignature = cardSignature;
    }

    public String getAccountType() {
        return AccountType;
    }

    public void setAccountType(String accountType) {
        AccountType = accountType;
    }

    public String getSTAN() {
        return STAN;
    }

    public void setSTAN(String STAN) {
        this.STAN = STAN;
    }

    public String getProcessor() {
        return Processor;
    }

    public void setProcessor(String processor) {
        Processor = processor;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    @Override
    public String toString(){
        return OrderId + "," + TransactionDate + "," + PaymentStatus + "," + Amount + "," + Processed + "," + CardSignature + "," + AccountType + "," + STAN + "," + Processor + "," + Type;
    }

    private String OrderId;
    private String TransactionDate;
    private String PaymentStatus;
    private String Amount;
    private String Processed;
    private String CardSignature;
    private String AccountType;
    private String STAN;
    private String Processor;
    private String Type;
}

