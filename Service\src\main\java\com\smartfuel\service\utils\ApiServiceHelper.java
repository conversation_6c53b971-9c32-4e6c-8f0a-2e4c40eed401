package com.smartfuel.service.utils;

import android.net.TrafficStats;
import com.smartfuel.service.logger.Log;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.smartfuel.service.IKioskApiService;
import com.smartfuel.service.forecourtcontroller.IPTS2ApiService;
import com.smartfuel.service.models.kiosk.request.AuthenticationRequest;
import com.smartfuel.service.models.kiosk.response.AuthenticationResponse;
import com.smartfuel.service.models.kiosk.response.BaseResponse;
import com.smartfuel.service.repository.IConfigurationRepository;
import com.smartfuel.service.sqlite.models.Configuration;

import java.io.IOException;

import retrofit2.Call;
import retrofit2.Response;

public class ApiServiceHelper {

    private static IConfigurationRepository _configurationRepository;
    private static IKioskApiService _kioskApiService;
    private static IPTS2ApiService _pts2ApiService;

    public static void setInstances(IConfigurationRepository configurationRepository, IKioskApiService kioskApiService) {
        _configurationRepository = configurationRepository;
        _kioskApiService = kioskApiService;
    }
    public static void setInstances(IPTS2ApiService pts2ApiService)
    {
        _pts2ApiService = pts2ApiService;
    }

    public static void authenticateKiosk() throws ApiException {
        AuthenticationRequest authenticationRequest = new AuthenticationRequest();

        authenticationRequest.setHash(_configurationRepository.getConfigurationByName("kiosk_hash_key").getValue());
        AuthenticationResponse authResponse = ApiServiceHelper.executeAPI(_kioskApiService.authenticateKiosk(authenticationRequest));

        StringBuilder stringBuilder = new StringBuilder();

        if (authResponse.getMessage() != null && !authResponse.getMessage().isEmpty()) {
            String str = stringBuilder.append("Bearer ").append(authResponse.getMessage()).toString();
            Configuration configuration = new Configuration("kiosk_bearer_token", str);
            _configurationRepository.addConfiguration(configuration);
        }
    }

    public static <Res extends BaseResponse> Res executeAPI(Call<Res> resCall) throws ApiException {
        return executeAPI(resCall, true);
    }

    public static <Res extends com.smartfuel.service.messages.forecourt.PTS2.response.BaseResponse> Res executeControllerAPI(Call<Res> resCall) throws ApiException {
        return executeControllerAPI(resCall, true);
    }

    private static <Res extends com.smartfuel.service.messages.forecourt.PTS2.response.BaseResponse> Res executeControllerAPI(Call<Res> resCall, boolean reAttempt) throws ApiException {
        try {
            TrafficStats.setThreadStatsTag((int) Thread.currentThread().getId());
            Response<Res> response = resCall.execute();
            Res responseBody;
            Log.i("APIServiceHelper",response.body().toString());
            if (response.isSuccessful())
                responseBody = response.body();

            else if ((response.code() == 401 || response.code() == 407) && reAttempt) { //TODO: Check PTS2 Controller response codes on API
                return executeControllerAPI(resCall.clone(), false);
            } else {
                Gson gson = (new GsonBuilder()).create();
                responseBody = (Res) gson.fromJson(response.errorBody().string(), com.smartfuel.service.messages.forecourt.PTS2.response.BaseResponse.class);
            }

            if (responseBody != null && !responseBody.isError() && responseBody.getMessage() != null && !responseBody.getMessage().isEmpty()) {
                throw new ApiException(response, responseBody.getMessage());
            } else if (responseBody == null) {
                throw new ApiException(response, "Error performing authentication.");
            }

            return responseBody;
        } catch (IOException e) {
            throw new ApiException(e);
        } catch (IllegalStateException e) {
            throw new ApiException(e);
        }
    }
    private static <Res extends BaseResponse> Res executeAPI(Call<Res> resCall, boolean reAttempt) throws ApiException {

        try {
            TrafficStats.setThreadStatsTag((int) Thread.currentThread().getId());
            Response<Res> response = resCall.execute();

            Res responseBody;
            if(response.isSuccessful())
                responseBody = response.body();
            else if((response.code() == 401 || response.code() == 407) && reAttempt){
                authenticateKiosk();
                return executeAPI(resCall.clone(), false);
            }
            else {
                Gson gson = (new GsonBuilder()).create();
                String errorBody = response.errorBody().string();
                responseBody = (Res)gson.fromJson(errorBody, BaseResponse.class);
            }

            if(responseBody != null && !responseBody.isSuccess() && responseBody.getMessage() != null && !responseBody.getMessage().isEmpty()) {
                throw new ApiException(response, responseBody.getMessage());
            } else if(responseBody == null || !response.isSuccessful()){
                throw new ApiException(response, "Error performing authentication.");
            }

            return responseBody;
        } catch (IOException e) {
            throw new ApiException(e);
        }
        catch (IllegalStateException e){
            throw new ApiException(e);
        }
    }
}
