package com.smartfuel.service;

import com.smartfuel.service.messages.forecourt.PSS5000.request.*;


public interface IForecourtControllerService {
    void AuthorizeFuellingPoint(AuthorizeFuellingPointRequest paramAuthorizeFuellingPointRequest);

    void CancelFuellingPointAuthorize(CancelFuellingPointAuthorizeRequest paramCancelFuellingPointAuthorizeRequest);

    void CompleteFuelTransaction(ClearSupTransactionRequest paramClearSupTransactionRequest);

    void FetchFuelTransaction(SupTransactionRequest paramSupTransactionRequest);

    void GetFuelPriceData(PriceSetRequest paramPriceSetRequest);

    void Heartbeat(HeartbeatRequest paramHeartbeatRequest);

    void Logon(LogonRequest paramLogonRequest);

    void PreparePrepaidTransaction(PreparePrepaidTransactionRequest paramPreparePrepaidTransactionRequest);
}
