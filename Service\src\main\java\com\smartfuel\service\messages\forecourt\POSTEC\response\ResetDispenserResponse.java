package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;


/*
01 015 0123 04 2000 08 Y Reset_Dispenser
 01 = client node number
 015 = request number
 0123 = sequence number
 04 = number of parameters
 2000 = socket success code (2) + PCC success code (000)
 08 = Dispenser number
 Y = Successful reset
 */
public class ResetDispenserResponse extends BaseResponse{
private int dispenserNumber;
private boolean successful;
    public ResetDispenserResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
        this.successful = super.responseData[6].equals('Y')?true:false;
    }

    public int getDispenserNumber() { return dispenserNumber; }
    public boolean getSuccessful() { return successful; }
}
