package com.smartfuel.service.models.transaction;

public class WhiteCardReceipt {
    public String AccountEmailAddress;

    public String AccountName;

    public String AccountUser;

    public String FuelGradeName;

    public String FuelPricePerLitre;

    public String FuelPumpNumber;

    public String FuelTransactionNumber;

    public String FuelValue;

    public String FuelVolume;

    public String InvoiceFooter;

    public String KioskName;

    public String StoreABN;

    public String StoreAddress1;

    public String StoreAddress2;

    public String StoreCity;

    public String StoreName;

    public String StorePostalCode;

    public String StoreState;

    public String TransactionCurrency;

    public String TransactionDateTime;

    public String TransactionGSTAmount;

    public String TransactionNumberDetail;

    public String TransactionStatus;

    public String TransactionTotalAmount;

    public String TransactionType;

    public String VehicleOdometer;

    public String VehicleRegistration;
    public String HostResponse;
    public String HostResponseCode;

}
