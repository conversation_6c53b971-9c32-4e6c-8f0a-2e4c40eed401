package com.smartfuel.service.sqlite.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.smartfuel.service.sqlite.models.RewardCardDiscount;

import java.util.List;

@Dao
public interface IRewardCardDiscount {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long[] addAll(List<RewardCardDiscount> paramList);

    @Update(onConflict = OnConflictStrategy.REPLACE)
    void update(RewardCardDiscount param);

    @Delete
    void delete(RewardCardDiscount paramList);

    @Query("Select * " +
            "from `RewardCardDiscount` " +
            "where transactionId = :transactionId")
    List<RewardCardDiscount> GetByTransaction(long transactionId);
}
