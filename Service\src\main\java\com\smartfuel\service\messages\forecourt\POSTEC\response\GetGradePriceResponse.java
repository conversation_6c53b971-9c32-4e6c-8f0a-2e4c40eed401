package com.smartfuel.service.messages.forecourt.POSTEC.response;


import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

/*
* 01 054 0123 09 2000 04 0 1019 0989 0999 1009 0 Req_Grade_Prices
 01 = client node number
 054 = request number
 0123 = sequence number
 09 = number of parameters
 2000 = socket success code (2) + PCC success code (000)
 04 = Grade number
 0 = Returned Cash prices
 1019 = Schedule 1 price
 0989 = Schedule 2 price
 0999 = Schedule 3 price
 1009 = Schedule 4 price
 0 = Return current prices
    * Request Prices been:
    0 = Current prices
    1 = First schedule price change
    2 = Second schedule price change
    N = Nth schedule prices change
    In case the request schedule doesn't exist PCC should return error code 26
* */
public class GetGradePriceResponse extends BaseResponse {
    private final int gradeNumber;
    private final long schedule1Price;
    private final long schedule2Price;
    private final long schedule3Price;
    private final long schedule4Price;
    private final int priceType;
    public GetGradePriceResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.gradeNumber = Integer.parseInt(super.responseData[5]);
        this.priceType =Integer.parseInt(super.responseData[6]);
        this.schedule1Price = Long.parseLong(super.responseData[7]);
        this.schedule2Price = Long.parseLong(super.responseData[8]);
        this.schedule3Price = Long.parseLong(super.responseData[9]);
        this.schedule4Price = Long.parseLong(super.responseData[10]);
    }

    public int getGradeNumber() {
        return gradeNumber;
    }

    public long getSchedule1Price() {
        return schedule1Price;
    }

    public long getSchedule2Price() {
        return schedule2Price;
    }

    public long getSchedule3Price() {
        return schedule3Price;
    }

    public long getSchedule4Price() {
        return schedule4Price;
    }

    public int getPriceType() {
        return priceType;
    }
}
