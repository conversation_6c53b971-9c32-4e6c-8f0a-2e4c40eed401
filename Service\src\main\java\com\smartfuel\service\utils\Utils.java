package com.smartfuel.service.utils;

import android.accounts.NetworkErrorException;
import android.annotation.SuppressLint;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;

import com.smartfuel.service.logger.Log;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

public class Utils {

    public static String getTypeName(Class<?> cl)
    {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return cl.getTypeName();
        } else {
            if (!cl.isArray()) return cl.getName();
            int dimensions;
            for (dimensions = 0; cl.isArray(); cl = cl.getComponentType()) dimensions++;
            String name = cl.getName();
            StringBuilder sb = new StringBuilder(name.length() + dimensions * 2).append(name);
            for (; dimensions > 0; dimensions--) sb.append("[]");
            return sb.toString();
        }
    }

    /*Exploration code - IP Address Info*/
    public static String getDeviceIpAddress() {
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements(); ) {
                NetworkInterface intf = en.nextElement();
                for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
                    InetAddress inetAddress = enumIpAddr.nextElement();
                    if (!inetAddress.isLoopbackAddress() && inetAddress instanceof Inet4Address) {
                        return inetAddress.getHostAddress();
                    }
                }
            }
        } catch (SocketException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static void isNetworkAvailable(Context context) throws NetworkErrorException {
        int attempts = 0;
        boolean connected = false;

        try {
            ConnectivityManager connectivityManager
                    = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

            while(!connected && attempts < 5) {
                @SuppressLint("MissingPermission")
                NetworkInfo activeNetworkInfo = connectivityManager != null ? connectivityManager.getActiveNetworkInfo() : null;
                connected = activeNetworkInfo != null && activeNetworkInfo.isConnected();
                if(!connected){
                    attempts++;
                    Thread.sleep(990);
                }
            }
        }catch (Exception e){
            Log.e("isNetworkAvailable", e);
        }

        if(!connected)
            throw new NetworkErrorException("Network connection failure.");
    }

    /*end*/
}
