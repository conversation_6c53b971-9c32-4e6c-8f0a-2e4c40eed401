package com.smartfuel.service.messages.forecourt.POSTEC.request;


import com.smartfuel.service.messages.forecourt.POSTEC.enums.TransactionType;

/**
 * To request the PCC to send the transaction details for a dispenser to the POS client for
 * payment.
 * The transaction must be either confirmed (See Confirm_Transaction) or returned (See
 * Return_Transaction) by the POS.
 * The Return_Transaction can be used to allow the void POS function to be supported for
 * forecourt sales.
 * The Confirm_Transaction should only be done after the Client has placed the transaction
 * data in non-volatile storage, usually at the end of the transaction sale process.
 * The Return_Transaction function cannot be performed after the Confirm_Transaction.
 * PCC Software > V6.38
 * When the PCC is configured with a price units digits setting of 5 or 6 digits, the PCC will
 * receive and send prices data up to 6 digits in length.
 * When the PCC isconfigured with a Sales Volume of 7 or 8 digits, the PCC will receive and
 * send transaction volume data with 8 digits.
 */
public class DownloadTransactionRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] transactionType = new char[1];
    public DownloadTransactionRequest(int nodeNumber, int dispenserNumber, TransactionType transactionType){
        super(nodeNumber,5,3,"Download_Transaction");
        this.dispenserNumber =  String.format("%02d",dispenserNumber).toCharArray();
        this.transactionType = transactionType.getType();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.transactionType);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
