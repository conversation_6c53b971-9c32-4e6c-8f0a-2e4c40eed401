package com.smartfuel.service.messages.forecourt.POSTEC.request;


public class GradeTitleRequest extends BaseRequest {
    private char[] gradeNumber = new char[2];

    public GradeTitleRequest(int nodeNumber, int  GradeNumber){
        super(nodeNumber,34,2,"Req_Grade_Title");
        this.gradeNumber = String.format("%02d",GradeNumber).toCharArray();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.gradeNumber);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
