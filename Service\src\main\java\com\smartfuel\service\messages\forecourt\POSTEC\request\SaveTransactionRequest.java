package com.smartfuel.service.messages.forecourt.POSTEC.request;

public class SaveTransactionRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];

    public SaveTransactionRequest(int nodeNumber, int dispenserNumber){
        super(nodeNumber,8,2,"Save_Transaction");
        this.dispenserNumber = String.format("%02d",dispenserNumber).toCharArray();
    }

    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
