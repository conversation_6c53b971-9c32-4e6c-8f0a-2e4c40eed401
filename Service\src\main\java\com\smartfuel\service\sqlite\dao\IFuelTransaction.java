package com.smartfuel.service.sqlite.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import com.smartfuel.service.sqlite.models.FuelTransaction;
@Dao
public interface IFuelTransaction {
    @Query("Select * from FuelTransaction where id = :fuelTransactionId")
    FuelTransaction getFuelTransactionById(long fuelTransactionId);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long upsertFuelTransaction(FuelTransaction paramFuelTransaction);
}
