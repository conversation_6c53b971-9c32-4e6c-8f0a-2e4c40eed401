package com.smartfuel.service.network;

import android.net.TrafficStats;
import android.os.Handler;
import android.os.Looper;
import android.util.ArrayMap;

import androidx.annotation.NonNull;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smartfuel.service.diagnostics.DiagnosticService;
import com.smartfuel.service.diagnostics.TCPDiagnostic;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.utils.Utils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.net.Socket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * TCPMessenger will handle the tcp communication and caching between a socket server and the Android application,
 * but also will provide type safe response defined by user in the MainThread to facilitate UI updates.
 * <p>
 * <p>using {@link #getDefaultInstance()} will provide a singleton with the default port 49152</p>
 */
public class TCPMessenger {

    /**
     * Client max cache size, it is set to 5 for now in order not to have too many connected/open socket at the same time
     */
    private static final int CLIENT_CACHE_SIZE = 5;
    /**
     * Executor max thread pool to be spawn
     */
    private static final int MAX_THREAD_POOL_SIZE = 5;
    /**
     * TCPMessenger instances cache per {@link SocketConfig}
     */
    private volatile static ArrayMap<SocketConfig, TCPMessenger> instanceCache = new ArrayMap<>();
    /**
     * {@link SocketConfig} default instance with the default port and default time out
     * <p>
     * <p>{@link SocketConfig} to changes those values</p>
     */
    private static final SocketConfig defaultSocketConfig = new SocketConfig(Constant.TCP_PORT, Constant.DEFAULT_TIME_OUT);

    /**
     * LRU socket client cache
     */
    private Socket client;
    /**
     * Map to hold callback per request
     */
    private final ConcurrentHashMap<Request, WeakReference<Callback>> commandCallbackMap = new ConcurrentHashMap<>();
    /**
     * Map to hold request per future
     */
    private final ConcurrentHashMap<Request, Future> commandFutureMap = new ConcurrentHashMap<>();
    /**
     * Socket config on this instance
     */
    private final SocketConfig socketConfig;
    /**
     * ExecutorService of this instance
     */
    private final ExecutorService executorService = Executors.newFixedThreadPool(MAX_THREAD_POOL_SIZE);
    /**
     * Handler where callback will be invoke
     */
    private final Handler handler;

    /**
     * ObjectMapper
     */
    private ObjectMapper objectMapper;

    /**
     * Private constructor use {@link #getInstance} to get an instance of this
     *
     * @param socketConfig the socket config of this instance
     */
    private TCPMessenger(@NonNull SocketConfig socketConfig) {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            throw new IllegalStateException("TCPMessenger need to be initialized within the UIThread");
        }
        handler = new Handler();
        this.socketConfig = socketConfig;
    }

    private String convertStreamToString(InputStream inputStream) throws Exception {

        if (inputStream != null) {
            StringBuffer stringBuffer = new StringBuffer();
            String message = null;

            int c = 0;
            while (true) {
                try {
                    c = inputStream.read();
                } catch (Exception e) {
                    throw e;
                }
                if (c != -1) {

                    if (c == 3 || c == 10 || c == 13) {
                        message = stringBuffer.toString();
                        break;
                    }

                    stringBuffer.append((char) c);
                }
            }
            if (message != null) {
                if (message.length() > 0) {
                    message = message.replace("\002", " ").trim();
                    return message;
                }
            }
        }
        return null;
    }

    /**
     * Get the default instance with default socket config {@link #defaultSocketConfig}
     *
     * @return the TCPMessenger instance with construct with {@link #defaultSocketConfig}
     */
    public static TCPMessenger getDefaultInstance() {
        return getInstance(defaultSocketConfig);
    }

    /**
     * @param socketConfig the socketConfig for the instance
     * @return the TCPMessenger, if instance was already created a new one wont be created unless that instance
     * is release with {@link #releaseInstance(TCPMessenger)}
     */
    public static TCPMessenger getInstance(@NonNull SocketConfig socketConfig) {
        TCPMessenger TCPMessenger = instanceCache.get(socketConfig);
        if (TCPMessenger == null) {
            synchronized (TCPMessenger.class) {
                TCPMessenger = instanceCache.get(socketConfig);
                if (TCPMessenger == null) {
                    TCPMessenger = new TCPMessenger(socketConfig);
                    instanceCache.put(socketConfig, TCPMessenger);
                    DiagnosticService.addMonitor(new TCPDiagnostic(TCPMessenger), SocketException.class);
                }
            }
        }

        return TCPMessenger;
    }

    /**
     * Release any previously initialized TCPMessenger instance
     *
     * @param tcpMessenger the instance to be released
     *                     <p>please note that if instance is released it cant be used anymore</p>
     */
    public static void releaseInstance(@NonNull TCPMessenger tcpMessenger) {
        synchronized (TCPMessenger.class) {
            instanceCache.remove(tcpMessenger.getSocketConfig());
            tcpMessenger.shutdown();
        }
    }

    /**
     * Get this instance socketConfig
     *
     * @return the socketConfig of this
     */
    private SocketConfig getSocketConfig() {
        return socketConfig;
    }

    /**
     * Send a request to a specific device
     *
     * @param request       the request
     * @param responseClass the response class object expected
     * @param callback      the callback to be invoked
     * @param <T>           Type of the object expected
     * @return return a future of this request
     */
    public <T> Future sendCommand(final Request request, Class<T> responseClass, final Callback<T> callback) {
        if(!isConnected()) {
            Log.e("TCPMessenger", new SocketException("Not connected"));
            return null;
        }

        checkCommand(request);
        try {
            final Future future = enqueueCommand(request, responseClass, callback);
            //commandFutureMap.put(request, future);
            return future;
        } catch (Exception e) {
            throw e;
        }
    }

    public <T> void readCommand(String paramString, Class<T> paramClass, Callback<T> paramCallback) {
        if(!isConnected()){
            Log.e("TCPMessenger", new SocketException("Not connected"));
            return;
        }

        try {
            dequeueCommand(paramString, paramClass, paramCallback);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Enqueue the request to the executorService
     *
     * @param request       the request
     * @param responseClass the response class object expected
     * @param <T>           Type of the object expected
     * @return return a future of this request
     * @throws IllegalStateException is thrown if this executorService was shutdown {@link #shutdown()}
     */
    private <T> Future enqueueCommand(final Request request, final Class<T> responseClass, final Callback<T> callback) {
        if (executorService.isShutdown()) {
            throw new IllegalStateException("executorService is shutdown");
        }

        return executorService.submit(new Runnable() {
            @Override
            public void run() {
                final Object[][] o = new Object[1][2];
                try {
                    o[0][0] = doSendCommand(request, responseClass);
                } catch (Exception e) {
                    Log.e("TCPMessenger", "enqueueCommand", e);
                    o[0][1] = e;
                    callback.onError(e);
                }


            }
        });


    }

    private <T> Future dequeueCommand(final String ip, final Class<T> responseClass, final Callback<T> callback) {
        if (executorService.isShutdown()) {
            callback.onError(new IllegalStateException("executorService is shutdown"));
            //throw new IllegalStateException("executorService is shutdown");
        }

        return executorService.submit(new Runnable() {
            @Override
            public void run() {
                final Object[][] o = new Object[1][2];
                try {
                    o[0][0] = doReadCommand(ip, responseClass);
                } catch (Exception e) {
                    Log.e("TCPMessenger", "dequeueCommand", e);
                    o[0][1] = e;
                }

                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        final T response;
                        if ((response = (T) o[0][0]) != null) {
                            try {
                                callback.onResponse(response);
                            } catch (Exception e) {
                                callback.onError(e);
                            }

                        } else {
                            callback.onError(o[0][1] == null ? null : (Throwable) o[0][1]);
                        }
                    }
                });
            }
        });

    }

    /**
     * Execute the request
     *
     * @param request       the request
     * @param responseClass the response class object expected
     * @param <T>           Type of the object expected
     * @return Type safe response T or null if something went wrong.
     */
    private <T> T doSendCommand(final Request request, Class<T> responseClass) throws IOException {
        initMapper();
        Log.i("Send Message", request.cmd.toString());

        final Socket client = getClientFor(request.ip);
        //T response;
        //synchronized (client) {
        try {
            final JsonGenerator jsonWriter = objectMapper.getFactory().createGenerator(client.getOutputStream());
            jsonWriter.writeRaw(request.cmd.toString());
            jsonWriter.flush();

        } catch (SocketException e) {
            Log.e("TCPMessenger", "doSendCommand-e", e);
            try {
                client.close();
                throw e;
            } catch (SocketException e1) {
                Log.e("TCPMessenger", "doSendCommand-e1", e1);
                throw e1;
            }
        }
        //}

        return null;
    }

    private <T> T doReadCommand(String ip, Class<T> responseClass) throws Exception {
        initMapper();

        final Socket client = getClientFor(ip);
        T response = null;
        synchronized (client) {
            try {
                String str = convertStreamToString(client.getInputStream());
                if (str != null) {
                    if (Utils.getTypeName(responseClass).equals("java.lang.String")) {
                        Log.w("networkRead", str);
                        response = (T) str;
                    } else {
                        final JsonParser jsonReader = objectMapper.getFactory().createParser(str);
                        response = jsonReader.readValueAs(responseClass);
                    }
                }

            } catch (SocketException e) {
                try {
                    client.close();
                } catch (Exception e1) {
                    Log.e("TCPMessenger", "doReadCommand", e1);
                    throw e1;

                }
                Log.e("TCPMessenger", "doReadCommand", e);
                throw e;
            }
             catch (SocketTimeoutException e) {
               try {
                    client.close();
                } catch (Exception e1) {
                    Log.e("TCPMessenger", "doReadCommand", e1);
                    throw e1;

                }
                Log.e("TCPMessenger", "doReadCommand", e);
                throw e;
            }
        }

        return response;
    }

    private Socket getClientFor(String ip) throws IOException {
        TrafficStats.setThreadStatsTag((int) Thread.currentThread().getId());

        synchronized (this) {
            if (client == null || client.isClosed()) {
                if (client != null) {
                    try {
                        client.close();//it could be already closed but for the sake of it.
                    } catch (Exception e) {
                    }
                }

                client = new Socket(ip, socketConfig.port);
                client.setSoTimeout(socketConfig.timeOut);
            }
        }

        return client;
    }

    /**
     * Check if a request is valid
     *
     * @param request the request to be examined
     * @throws NullPointerException if the request command or ip is null
     */
    private void checkCommand(Request request) {
        if (request == null) {
            throw new NullPointerException("request cannot be null");
        }

        if (request.cmd == null || request.ip == null) {
            throw new NullPointerException("request or ip cannot be null");
        }
    }

    /**
     * Init the mapper
     */
    private void initMapper() {
        if (objectMapper == null) {
            objectMapper = new ObjectMapper();
        }
    }

    /**
     * Shutdown this instance, clear all cache and close all cached client socket
     * <p>
     * <p>note that this will be useless after this method is called</p>
     */
    public void shutdown() {
        executorService.shutdownNow();
        commandCallbackMap.clear();
        commandFutureMap.clear();
    }

    public Socket getSocket(String ip) throws IOException {
        return getClientFor(ip);
    }

    public boolean isConnected(){
        try {
            if (client != null && (!client.isConnected() || client.isClosed()))
                return false;
        } catch (Exception e) {
            Log.w("isConnected", e);
            return false;
        }
        return true;
    }

    public void reconnect() throws IOException {
        if (client != null) {
            String ip = client.getInetAddress().getHostAddress();
            if(!client.isClosed())
                client.close();
            getClientFor(ip);
        }
    }

    /**
     * Callback to be register to {@link TCPMessenger} in order to invoke request callback
     *
     * @param <T> Type T of the expect request response
     */
    public interface Callback<T> {

        /**
         * Invoke when request was successful
         *
         * @param response
         */
        //void onResponse(Request request,T t);
        void onResponse(T response) throws InterruptedException;


        /**
         * Invoke when an error occur
         *
         * @param throwable the throwable thrown when the error occur
         */
        //void onError(Request request, Throwable throwable);
        void onError(Throwable throwable);


    }

    /**
     * Request object
     */
    public static class Request {

        private static AtomicInteger atomicInteger = new AtomicInteger(0);
        /**
         * The id of the request, auto incremented each time a new instance of this is created
         */
        private final int id;
        /**
         * The ip of the request
         */
        public String ip;
        /**
         * The command of the request
         */
        public Object cmd;

        public Request(String ip, Object cmd) {
            this.ip = ip;
            this.cmd = cmd;
            id = atomicInteger.incrementAndGet();
        }

        @Override
        public String toString() {
            return "Request{" +
                    "id=" + id +
                    ", ip='" + ip + '\'' +
                    ", cmd=" + cmd +
                    '}';
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            Request request = (Request) o;

            if (id != request.id) return false;
            if (ip != null ? !ip.equals(request.ip) : request.ip != null) return false;
            return cmd != null ? cmd.equals(request.cmd) : request.cmd == null;

        }

        @Override
        public int hashCode() {
            int result = id;
            result = 31 * result + (ip != null ? ip.hashCode() : 0);
            result = 31 * result + (cmd != null ? cmd.hashCode() : 0);
            return result;
        }
    }
}