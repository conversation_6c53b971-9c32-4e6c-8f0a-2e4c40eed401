package com.smartfuel.service.messages.forecourt.POSTEC.response;

public class DispenserSaleStatus {
    private int[] saleStatus;

    public DispenserSaleStatus(byte[] bytes) {
        int byteIndex = 0;
        saleStatus = new int[8];
        saleStatus[0] = (bytes[byteIndex] >> 1) & 1;
        saleStatus[1] = bytes[byteIndex] & 1;
        saleStatus[2] = (bytes[byteIndex] >> 2) & 1;
        saleStatus[3] = (bytes[byteIndex] >> 3) & 1;
        saleStatus[4] = (bytes[byteIndex] >> 4) & 1;
        saleStatus[5] = (bytes[byteIndex] >> 5) & 1;
        saleStatus[6] = (bytes[byteIndex] >> 6) & 1;
        saleStatus[7] = (bytes[byteIndex] >> 7) & 1;
    }
    public boolean noSale(){
        return saleStatus[0] == 0 && saleStatus[1] == 0;
    }
    public boolean progressSale(){
        return saleStatus[0] == 1 && saleStatus[1] == 0;
    }
    public boolean completeSale(){
        return (saleStatus[0] == 0 && saleStatus[1] == 1) || (saleStatus[0] == 1 && saleStatus[1] == 1);
    }
}
