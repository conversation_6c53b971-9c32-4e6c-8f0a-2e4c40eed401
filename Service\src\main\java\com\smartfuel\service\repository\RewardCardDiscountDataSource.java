package com.smartfuel.service.repository;

import com.smartfuel.service.sqlite.dao.IRewardCardDiscount;
import com.smartfuel.service.sqlite.models.RewardCardDiscount;

import java.util.List;

import javax.inject.Inject;

public class RewardCardDiscountDataSource implements IRewardCardDiscountRepository {
    private IRewardCardDiscount rewardCardDiscount;

    @Inject
    public RewardCardDiscountDataSource(IRewardCardDiscount rewardCardDiscount) {
        this.rewardCardDiscount = rewardCardDiscount;
    }

    public long[] addAll(List<RewardCardDiscount> paramList){
        return this.rewardCardDiscount.addAll(paramList);
    }

    public void update(RewardCardDiscount param){
        this.rewardCardDiscount.update(param);
    }

    public void delete(RewardCardDiscount paramList){
        this.rewardCardDiscount.delete(paramList);
    }

    public List<RewardCardDiscount> GetByTransaction(long transactionId){
        return this.rewardCardDiscount.GetByTransaction(transactionId);
    }
}
