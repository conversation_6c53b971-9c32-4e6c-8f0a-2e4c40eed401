package com.smartfuel.service.models.posmaster;

import java.util.List;

public class OPTExport {
    private List<String> orderIds;
    private List<Order> orders;
    private List<OrderItem> orderItems;
    private List<FuelTransaction> fuelTransactions;
    private List<Transaction> transactions;

    public List<String> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<String> orderIds) {
        this.orderIds = orderIds;
    }

    public List<Order> getOrders() {
        return orders;
    }

    public void setOrders(List<Order> orders) {
        this.orders = orders;
    }

    public List<OrderItem> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<OrderItem> orderItems) {
        this.orderItems = orderItems;
    }

    public List<FuelTransaction> getFuelTransactions() {
        return fuelTransactions;
    }

    public void setFuelTransactions(List<FuelTransaction> fuelTransactions) {
        this.fuelTransactions = fuelTransactions;
    }

    public List<Transaction> getTransactions() {
        return transactions;
    }

    public void setTransactions(List<Transaction> transactions) {
        this.transactions = transactions;
    }
}

