package com.smartfuel.service;

import com.smartfuel.service.models.kiosk.response.AccountCardCapabilities;
import com.smartfuel.service.models.kiosk.response.UserResponse;
import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;
import com.smartfuel.service.sqlite.models.RewardCardDiscount;
import com.smartfuel.service.sqlite.models.Transaction;

import java.util.List;
public interface IServiceEvents {
    default void customerReceiptDataReady(List<WhiteCardReceipt> paramList){}
    default void customerCardReceiptDataReady(List<CardReceipt> cardReceipts){}
    default void customerReceiptNotFount(){}

    default void pumpAuthorised(long trxNumber, int fuelPointId, String[] authorizedGrades){}

    default void serviceReady(){}

    void systemError(String paramString, Throwable paramThrowable);

    default void whiteCardUserRejected(UserResponse paramUserResponseResponse){}

    default void getWhiteCardUserInformation(long externalReference, String im30Reference, AccountCardCapabilities capabilities){}

    default void getRewardCardUserInformation(long externalReference){}

    default void rewardCardApplied(List<RewardCardDiscount> discounts){}

    default void rewardCardDeclined(String message){}

    default void cardMagneticComplete(String track2){}

    default void cardMagneticDeclined(String message){}

    default void cardTransactionDeclined(String bankResponse, String cardMask){}

    default void cardTransactionInProgress(String pumpNo, long processAmount){}

    default void paymentComplete(Transaction transaction){};

    default void cardReceiptComplete(List<Integer> terminalTransactionId){}

    default void cardReceiptNotFound(){}

    default void cardReceiptDeclined(){}

    default void cardReceiptCancelled(){}

    default void cardReceiptTimeOut(){}

    default void fuellingStatusUpdate(){}

    default void gradePriceUpdate(){}

    default void controllerConnectionAttempt(int attempt){};
    default void showHomeScreen(){};
    default void showStandbyScreen(){};
}
