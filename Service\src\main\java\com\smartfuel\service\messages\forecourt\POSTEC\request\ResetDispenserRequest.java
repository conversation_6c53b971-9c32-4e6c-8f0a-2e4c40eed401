package com.smartfuel.service.messages.forecourt.POSTEC.request;

/*
* To reset the specified dispenser.
It can be used to delete an invalid transaction caused by a faulty dispenser or any
exception / error condition in the FCC.
Access to this function should be restricted to a management menu and all actions
recorded in an Audit trail.
* */
public class ResetDispenserRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] authCode = new char[4];
    public ResetDispenserRequest(int nodeNumber, int dispenserNumber)
    {
        super(nodeNumber,15,3,"Reset_Dispenser");
        this.dispenserNumber = String.format("%02d",dispenserNumber).toCharArray();
        this.authCode = "9512".toCharArray();

    }

    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.authCode);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
