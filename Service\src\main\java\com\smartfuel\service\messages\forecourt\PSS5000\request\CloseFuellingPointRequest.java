package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;
import org.json.JSONException;
public class CloseFuellingPointRequest {
    private final FcCloseFpRequestData data;

    private final String name = "close_Fp_req";

    private final String subCode = "00H";

    public CloseFuellingPointRequest(String paramString) {
        this.data = new FcCloseFpRequestData(paramString);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class FcCloseFpRequestData {
        private final String FpId;

        FcCloseFpRequestData(String param1String) {
            this.FpId = param1String;
        }
    }
}
