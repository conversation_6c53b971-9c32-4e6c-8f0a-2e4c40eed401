package com.smartfuel.service;

import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;
import com.smartfuel.service.models.transaction.ReceiptTemplate;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ReceiptsManager {
    public static List<String> getReceiptData(List<WhiteCardReceipt> whitecardReceiptList) {
        ArrayList<String> arrayList = new ArrayList();
        try {
            Iterator<WhiteCardReceipt> iterator = whitecardReceiptList.iterator();
            while (iterator.hasNext())
                arrayList.add(ReceiptTemplate.getWhitecardReceipt(iterator.next()));
            return arrayList;
        } catch (Exception exception) {
            throw exception;
        }
    }
    public static List<String> getCardReceiptData(List<CardReceipt> cardReceipts){
        ArrayList<String> arrayList = new ArrayList();
        try {
            Iterator<CardReceipt> iterator = cardReceipts.iterator();
            while (iterator.hasNext())
                arrayList.add(ReceiptTemplate.getCardReceipt(iterator.next()));
            return arrayList;
        } catch (Exception exception) {
            throw exception;
        }
    }
}
