package com.smartfuel.service.messages.forecourt.POSTEC.request;


/**
 * This enables the POS client to request exclusive rights to a transaction for the purposes of
 * finalization. Locking the sale prevents another PCC client from being able to finalize the
 * transaction, while this POS is processing the transaction.
 * Client Types - using 01 in OPT
 * 00 = Reserved
 * 01 = PoS / Console client
 * 02 = EFT transaction collection client
 * 03 = PoS / Console auto-payoff client
 * 04 = PoS / Console client (Prepay)
 * 05 = PoS / Console client (Dispenser Test)
 * 06 = PoS / Console client (ePurse Handheld)
 * 07 = Transaction recovery
 * 08 = PoS / Console client (ePurse Vehicle)
 * 09 = OPT Dispenser Test
 * 10 = PoS / Console client (Forecourt finalised
 * override). Enables PoS to lock an FPoS transaction
 * which is not yet finalized and signalled as ready for
 * collection. This shifts the FCC transaction state on to
 * allow the PoS to confirm (clear) the transaction and is
 * only used to record and clear transactions should the
 * payment system fail. It should be implemented as a
 * special function protected by password access and
 * recorded in an Audit trail
 * 11 = Forecourt Payment device client (eg EDC
 * terminal)
 * 12 = PoS / Console adding sale item to Forecourt PoS
 * transaction for finalization with the payment server
 * 13 = PoS / Console client (Sampling)
 * 14 = OPT Sampling
 */
public class LockTransactionRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] sequenceNumber = new char[6];
    private char[] clientType = new char[2];
    public LockTransactionRequest(int nodeNumber, int dispenserNumber, int sequenceNumber){
        super(nodeNumber,88,4,"Lock_Transaction_Data");
        this.dispenserNumber = String.format("%02d",dispenserNumber).toCharArray();
        this.sequenceNumber = String.format("%06d",sequenceNumber).toCharArray();
        this.clientType = "01".toCharArray();
    }

    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.sequenceNumber);
        returnValue.append(" ");
        returnValue.append(this.clientType);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
