package com.smartfuel.service.models.kiosk.request;

import com.smartfuel.service.IKioskApiService;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.models.kiosk.response.TransactionValidateResponse;
import com.smartfuel.service.utils.ApiServiceHelper;

public class TransactionValidateRequest {

    private String terminalID;
    private long transactionReference;
    private long stan;
    private String vehicleRegistrationNumber;
    private String vehicleOdometer;


    private TransactionValidateRequest(String terminalID,
                                        long transactionReference,
                                        long stan,
                                        String vehicleRegistrationNumber,
                                        String vehicleOdometer){
        this.terminalID = terminalID;
        this.transactionReference = transactionReference;
        this.stan = stan;
        this.vehicleRegistrationNumber = vehicleRegistrationNumber;
        this.vehicleOdometer = vehicleOdometer;
    }

    public static TransactionValidateResponse send(IKioskApiService kioskApiService,
                                                   String terminalID,
                                                   String transactionReference,
                                                   long stan,
                                                   String vehicleRegistrationNumber,
                                                   String vehicleOdometer){
        TransactionValidateResponse response = null;
        try {
            TransactionValidateRequest request = new TransactionValidateRequest(terminalID,
                                                                                Long.parseLong(transactionReference),
                                                                                stan,
                                                                                vehicleRegistrationNumber,
                                                                                vehicleOdometer);
            response = ApiServiceHelper.executeAPI(kioskApiService.validateTransaction(request));
        } catch (Exception ex) {
            Log.e("TransactionValidateRequest", ex);
        }
        return response;
    }

    public String getTerminalID() {
        return terminalID;
    }

    public void setTerminalID(String terminalID) {
        this.terminalID = terminalID;
    }

    public long getTransactionReference() {
        return transactionReference;
    }

    public void setTransactionReference(long transactionReference) {
        this.transactionReference = transactionReference;
    }

    public long getStan() {
        return stan;
    }

    public void setStan(long stan) {
        this.stan = stan;
    }

    public String getVehicleRegistrationNumber() {
        return vehicleRegistrationNumber;
    }

    public void setVehicleRegistrationNumber(String vehicleRegistrationNumber) {
        this.vehicleRegistrationNumber = vehicleRegistrationNumber;
    }

    public String getVehicleOdometer() {
        return vehicleOdometer;
    }

    public void setVehicleOdometer(String vehicleOdometer) {
        this.vehicleOdometer = vehicleOdometer;
    }
}
