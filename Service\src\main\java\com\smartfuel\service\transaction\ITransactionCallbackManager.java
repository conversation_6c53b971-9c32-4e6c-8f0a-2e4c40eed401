package com.smartfuel.service.transaction;

import com.smartfuel.service.models.transaction.IM30TransactionBroadcast;

public interface ITransactionCallbackManager {
    void CardTransactionUpdate(IM30TransactionBroadcast broadcast);

    void CardTransactionReadComplete(IM30TransactionBroadcast broadcast);

    void CardTransactionTerminalFailed(IM30TransactionBroadcast broadcast);

    void CardTransactionAuthorised(IM30TransactionBroadcast broadcast);

    void CardTransactionDeclined(IM30TransactionBroadcast broadcast);

    void CardTransactionCaptureComplete(IM30TransactionBroadcast broadcast);
    void CardTransactionReversalComplete(IM30TransactionBroadcast broadcast);
    void CardTransactionTimeout(IM30TransactionBroadcast broadcast);
    void CardTransactionCancelled(IM30TransactionBroadcast broadcast);
    void CardTransactionCaptureFailed(IM30TransactionBroadcast broadcast);
    void CardTransactionReversalFailed(IM30TransactionBroadcast broadcast);

    void CardReceiptReadComplete(IM30TransactionBroadcast broadcast);
    void CardReceiptTerminalFailed(IM30TransactionBroadcast broadcast);
    void CardReceiptCancelled(IM30TransactionBroadcast broadcast);
    void CardReceiptTimeout(IM30TransactionBroadcast broadcast);

    void CardMagneticReadComplete(IM30TransactionBroadcast broadcast);
    void CardMagneticTerminalFailed(IM30TransactionBroadcast broadcast);
    void CardMagneticCancelled(IM30TransactionBroadcast broadcast);
    void CardMagneticTimeout(IM30TransactionBroadcast broadcast);
}