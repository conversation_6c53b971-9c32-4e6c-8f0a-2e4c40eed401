package com.smartfuel.service.diagnostics;

import com.smartfuel.service.logger.Log;
import com.smartfuel.service.logger.LogMailer;
import com.smartfuel.service.utils.ApiException;
import com.smartfuel.service.utils.Utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class DiagnosticService {

    private HashMap<Class<?>, BaseDiagnostic> diagnostics;
    private DiagnosticListener listener;
    private ScheduledExecutorService scheduledSendReportExecutorService;
    private List<Throwable> exceptionList;
    private LogMailer logMailer = null;

    private static DiagnosticService instance = null;

    public static DiagnosticService getInstance(){
        if(instance == null)
            instance = new DiagnosticService();

        return instance;
    }

    private DiagnosticService(){
        diagnostics = new HashMap<>();
        scheduledSendReportExecutorService = Executors.newSingleThreadScheduledExecutor();
        exceptionList = new ArrayList<>();
        listener = new DiagnosticListener() {
            @Override public void onStopDetected(String tag) { }
            @Override public void onReestablishing(String tag, int attempt) { }
            @Override public void onRestored(String tag) { }
        };
        startSchedule();
    }

    public static void setListener(DiagnosticListener listener){
        getInstance().listener = listener;
        for (BaseDiagnostic diagnostic : getInstance().diagnostics.values()) {
            diagnostic.setListener(listener);
        }
    }

    public static void setLogMailer(LogMailer logMailer){
        getInstance().logMailer = logMailer;
    }

    public static void addMonitor(BaseDiagnostic diagnostic, Class<? extends Exception> ... exceptionType){
        for (Class<?> ex : exceptionType) {
            if(!getInstance().diagnostics.containsKey(ex)) {
                diagnostic.setListener(getInstance().listener);
                getInstance().diagnostics.put(ex, diagnostic);
            }
        }
    }

    public static <T extends Throwable> boolean resolve(T exception, boolean resolve){
        Throwable ex = exception;
        if(ex instanceof ApiException && ex.getCause() != null)
            ex = exception.getCause();

        if(getInstance().diagnostics.containsKey(ex.getClass())){
            BaseDiagnostic diagnostic = getInstance().diagnostics.get(ex.getClass());
            if(!diagnostic.check()) {
                if(resolve){
                    getInstance().exceptionList.add(ex);
                    diagnostic.resolve();
                }
                return true;
            }
        }

        try {
            //avoid send email exception for this exception
            switch (ex.getMessage()) {
                case "Broken pipe":
                case "Connection reset":
                case "Receive timed out":
                case "Socket closed":
                case "Socket is closed":
                case "Software caused connection abort":
                case "Not connected":
                    getInstance().exceptionList.add(ex);
                    return true;
                default:
                    break;
            }
        }catch (Exception e){
        }
        return false;
    }

    public static <T extends Throwable> boolean checkResolve(T exception){
        return resolve(exception, false);
    }

    private void startSchedule() {
        scheduledSendReportExecutorService.scheduleAtFixedRate(() -> {

            synchronized (DiagnosticService.this) {
                try {
                    Utils.isNetworkAvailable(logMailer.getContext());
                    String exceptions = getExceptions();
                    String diagnostics = getDiagnostics();
                    if(logMailer != null && (!exceptions.isEmpty() || !diagnostics.isEmpty())){
                        logMailer.sendDiagnosticEmail(exceptions, diagnostics);
                    }
                } catch (Exception e) {
                    Log.w("LogInfo", "Send Kiosk Info Failed");
                }
            }
        }, 30, 60, TimeUnit.MINUTES);
    }

    private String getExceptions(){

        String ret = "";

        Map<Class<? extends Throwable>, Integer> exceptionCount = new HashMap<>();

        for (Throwable ex : exceptionList) {
            Class<? extends Throwable> exType = ex.getClass();
            exceptionCount.put(exType, exceptionCount.getOrDefault(exType, 0) + 1);
        }

        for (Map.Entry<Class<? extends Throwable>, Integer> entry : exceptionCount.entrySet()) {
            ret += "<tr>\n" +
                    "   <td>" + entry.getKey().getName() + "</td>\n" +
                    "   <td>" + entry.getValue() + "</td>\n" +
                    "</tr>";
        }

        exceptionList.clear();

        return ret;
    }

    private String getDiagnostics(){
        String ret = "";

        for (BaseDiagnostic diagnostic : diagnostics.values()) {
            if(!diagnostic.check()) {
                diagnostic.resolve();
            }
            ret += diagnostic.toString();
        }

        return ret;
    }
}
