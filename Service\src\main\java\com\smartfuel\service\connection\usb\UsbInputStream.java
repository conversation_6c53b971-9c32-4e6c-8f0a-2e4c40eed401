package com.smartfuel.service.connection.usb;

import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbEndpoint;
import android.hardware.usb.UsbInterface;
import android.hardware.usb.UsbManager;
import android.hardware.usb.UsbRequest;
import com.smartfuel.service.logger.Log;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;

public class UsbInputStream extends InputStream {
    private UsbDeviceConnection usbConnection;
    private UsbInterface usbInterface;
    private UsbEndpoint usbEndpoint;
    protected String responseData;
    private int dataoutSize = 0;
    private UsbRequest usbRequestRead;

    public UsbInputStream(UsbManager usbManager, UsbDevice usbDevice) throws IOException {

        this.usbInterface = UsbDeviceHelper.findDeviceInterface(usbDevice);
        if (this.usbInterface == null) {
            throw new IOException("Unable to find USB interface.");
        }

        this.usbEndpoint = UsbDeviceHelper.findEndpointRead(this.usbInterface);
        if (this.usbEndpoint == null) {
            throw new IOException("Unable to find USB endpoint.");
        }

        this.usbConnection = usbManager.openDevice(usbDevice);
        if (this.usbConnection == null) {
            throw new IOException("Unable to open USB connection.");
        }

        this.usbRequestRead = new UsbRequest();
        if (!this.usbRequestRead.initialize(usbConnection, usbEndpoint)) {
            throw new IOException("Failed to initialize USB request.");
        }
    }

    public String getResponse() {
        String r = "";
        r = responseData != null ? responseData.trim() : "";
        return r;
    }

    @Override
    public int read() throws IOException {


        if (this.usbInterface == null || this.usbEndpoint == null || this.usbConnection == null) {
            throw new IOException("Unable to connect to USB device.");
        }

        if (!this.usbConnection.claimInterface(this.usbInterface, true)) {
            read();
            //throw new IOException("Error during claim USB interface.");
        }
        Thread readThread = new Thread(new Runnable() {
            @Override
            public void run() {
                //UsbRequest usbRequestRead = new UsbRequest();
                //if (usbRequestRead.initialize(usbConnection, usbEndpoint)) {
                    byte[] dataRead = new byte[1024];
                    dataoutSize = usbConnection.bulkTransfer(usbEndpoint, dataRead, dataRead.length, 30);
                    //Log.i("READ DATA SIZE", String.valueOf(dataoutSize));
                    //Log.i("READ DATA", new String(dataRead).trim());
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Log.e("USB Input Stream", e);
                        e.printStackTrace();
                    }
                    responseData = new String(dataRead);
                    //usbRequestRead.close();

                //}
            }
        });
        readThread.start();
        try {
            readThread.join();
        } catch (InterruptedException e) {
            Log.e("USB Input Stream", e);
            e.printStackTrace();
        }

        return dataoutSize;
    }

    @Override
    public void close() throws IOException {
        super.close();
        if (usbRequestRead != null) {
            usbRequestRead.close();
        }
        if (usbConnection != null && usbInterface != null) {
            usbConnection.releaseInterface(usbInterface);
        }
    }
}