package com.smartfuel.service.models.transaction;
import com.smartfuel.service.enums.transaction.ProcessType;

public class IM30Transaction {
    private String externalDeviceToken = "";
    private String externalReference;
    private long transactionAmount;
    private int transactionCurrency;
    private int processType;

    public IM30Transaction(String externalDeviceToken, String externalReference, long transactionAmount, int transactionCurrency, ProcessType processType)
    {
        this.externalDeviceToken = externalDeviceToken;
        this.externalReference = externalReference;
        this.transactionAmount = transactionAmount;
        this.transactionCurrency = transactionCurrency;
        this.processType = processType.getProcessType();
    }

}
