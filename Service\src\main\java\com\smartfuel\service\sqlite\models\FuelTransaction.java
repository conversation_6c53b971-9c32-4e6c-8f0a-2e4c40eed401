package com.smartfuel.service.sqlite.models;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

@Entity(indices = {@Index(value = {"id", "transSeqNumber"})})
public class FuelTransaction {
    @NonNull
    @PrimaryKey(autoGenerate = true)
    private long id;
    private String gradeId;
    private long money;
    private long moneyDue;
    private long price;
    private int pumpNumber;
    private String transInfoMask;
    private String transSeqNumber;
    private String vehicleOdometer;
    private String vehicleRegistration;
    private long volume;
    private String whitecardAccountEmail;
    private String whitecardAccountId;
    private String whitecardAccountName;
    private String whitecardUserId;
    private String whitecardUserName;

    public String getGradeId() {
        return this.gradeId;
    }

    public long getId() {
        return this.id;
    }

    public long getMoney() {
        return this.money;
    }

    public long getMoneyDue() {
        return this.moneyDue;
    }

    public long getPrice() {
        return this.price;
    }

    public int getPumpNumber() {
        return this.pumpNumber;
    }

    public String getTransInfoMask() {
        return this.transInfoMask;
    }

    public String getTransSeqNumber() {
        return this.transSeqNumber;
    }

    public String getVehicleOdometer() {
        return this.vehicleOdometer;
    }

    public String getVehicleRegistration() {
        return this.vehicleRegistration;
    }

    public long getVolume() {
        return this.volume;
    }

    public String getWhitecardAccountEmail() {
        return this.whitecardAccountEmail;
    }

    public String getWhitecardAccountId() {
        return this.whitecardAccountId;
    }

    public String getWhitecardAccountName() {
        return this.whitecardAccountName;
    }

    public String getWhitecardUserId() {
        return this.whitecardUserId;
    }

    public String getWhitecardUserName() {
        return this.whitecardUserName;
    }

    public void setGradeId(String paramString) {
        this.gradeId = paramString;
    }

    public void setId(long paramLong) {
        this.id = paramLong;
    }

    public void setMoney(long paramLong) {
        this.money = paramLong;
    }

    public void setMoneyDue(long paramLong) {
        this.moneyDue = paramLong;
    }

    public void setPrice(long paramLong) {
        this.price = paramLong;
    }

    public void setPumpNumber(int paramInt) {
        this.pumpNumber = paramInt;
    }

    public void setTransInfoMask(String paramString) {
        this.transInfoMask = paramString;
    }

    public void setTransSeqNumber(String paramString) {
        this.transSeqNumber = paramString;
    }

    public void setVehicleOdometer(String paramString) {
        this.vehicleOdometer = paramString;
    }

    public void setVehicleRegistration(String paramString) {
        this.vehicleRegistration = paramString;
    }

    public void setVolume(long paramLong) {
        this.volume = paramLong;
    }

    public void setWhitecardAccountEmail(String paramString) {
        this.whitecardAccountEmail = paramString;
    }

    public void setWhitecardAccountId(String paramString) {
        this.whitecardAccountId = paramString;
    }

    public void setWhitecardAccountName(String paramString) {
        this.whitecardAccountName = paramString;
    }

    public void setWhitecardUserId(String paramString) {
        this.whitecardUserId = paramString;
    }

    public void setWhitecardUserName(String paramString) {
        this.whitecardUserName = paramString;
    }
}

