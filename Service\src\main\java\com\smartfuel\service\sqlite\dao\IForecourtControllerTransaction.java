package com.smartfuel.service.sqlite.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import com.smartfuel.service.sqlite.models.ForecourtControllerTransaction;

@Dao
public interface IForecourtControllerTransaction {
    @Query("Delete from ForecourtControllerTransaction where FpLockId = :fpLockId and FpId = :fpId and FcGradeId = :FcGradeId")
    void deleteForeceourtControllerTransaction(int fpLockId, int fpId, String FcGradeId);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void upsertForeceourtControllerTransaction(ForecourtControllerTransaction paramForecourtControllerTransaction);
}
