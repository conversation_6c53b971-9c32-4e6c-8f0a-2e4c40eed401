package com.smartfuel.service.forecourtcontroller;

public class ForecourtControllerFactory {
    public static <T> ForecourtController getForecourtController(int posId, String type, int port, String ipAddress,String operationMode, String posIpAddress, ForecourtController.Callback<T> paramCallback){
        if("fake".equalsIgnoreCase(type)) return new FakeService(posId, ipAddress, port, operationMode, posIpAddress, paramCallback);
        else if("pss5000".equalsIgnoreCase(type)) return new PSS5000Service(posId, ipAddress, port, operationMode, posIpAddress, paramCallback);
        else if("postec".equalsIgnoreCase(type)) return new POSTECService(ipAddress, port, operationMode, posIpAddress, paramCallback);
        else if("pts2".equalsIgnoreCase(type)) return new PTS2Service(ipAddress, port, operationMode, posIpAddress, paramCallback);
        return null;
    }
}
