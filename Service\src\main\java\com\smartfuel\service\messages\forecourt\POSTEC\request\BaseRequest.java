package com.smartfuel.service.messages.forecourt.POSTEC.request;

/**
 * This is the base PCC request class which defines the parameters required on all PCC requests.
 * This class also provides the ability to generate the message checksum.
 */
public class BaseRequest {
    private char[] nodeNumber = new char[2];
    private char[] requestNumber = new char[3];
    private char[] parameterCount = new char[2];
    private char[] functionName = new char[32];

    public BaseRequest(int nodeNumber, int requestNumber, int parameterCount, String functionName) {
        this.nodeNumber = String.format("%02d", nodeNumber).toCharArray();
        this.requestNumber = String.format("%03d", requestNumber).toCharArray();
        this.parameterCount = String.format("%02d", parameterCount).toCharArray();
        this.functionName = functionName.toCharArray();
    }

    public char[] getNodeNumber() {
        return nodeNumber;
    }

    public char[] getRequestNumber() {
        return requestNumber;
    }

    public char[] getParameterCount() {
        return parameterCount;
    }

    public char[] getFunctionName() {
        return functionName;
    }

    public String generateMessageCheckSum(String message) {
        //byte[] buf = message.getBytes(Charset.forName(StandardCharsets.US_ASCII.name()));
        byte[] buf = message.getBytes();
        int sum = 0;
        for (int i = 0; i < buf.length; i++) {
            sum = sum + buf[i];
        }
        sum = Math.floorMod(sum, 256); //sum%256;//
        sum = sum % 64; //3Fh ;sum<<63
        sum = sum + 48; //30h
        return Character.toString((char) sum);
    }
}
