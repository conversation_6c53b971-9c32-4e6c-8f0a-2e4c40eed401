package com.smartfuel.service.network;

import android.net.TrafficStats;
import android.os.Handler;
import android.os.Looper;
import android.util.ArrayMap;

import androidx.annotation.NonNull;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smartfuel.service.diagnostics.DiagnosticService;
import com.smartfuel.service.diagnostics.UDPDiagnostic;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.utils.Utils;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class UDPMessenger {
    /**
     * Client max cache size, it is set to 5 for now in order not to have too many connected/open socket at the same time
     */
    private static final int CLIENT_CACHE_SIZE = 5;
    /**
     * Executor max thread pool to be spawn
     */
    private static final int MAX_THREAD_POOL_SIZE = 5;
    /**
     * UDPMessenger instances cache per {@link SocketConfig}
     */
    private volatile static ArrayMap<SocketConfig, UDPMessenger> instanceCache = new ArrayMap<>();
    /**
     * {@link SocketConfig} default instance with the default port and default time out
     * <p>
     * <p>{@link SocketConfig} to changes those values</p>
     */
    private static final SocketConfig defaultSocketConfig = new SocketConfig(Constant.TCP_PORT, Constant.DEFAULT_TIME_OUT);
    /**
     * LRU socket client cache
     */
    private DatagramSocket client;
    private final SocketConfig socketConfig;
    private final ExecutorService executorService = Executors.newFixedThreadPool(MAX_THREAD_POOL_SIZE);
    /**
     * Handler where callback will be invoke
     */
    private final Handler handler;

    /**
     * ObjectMapper
     */
    private ObjectMapper objectMapper;

    /**
     * Private constructor use {@link #getInstance} to get an instance of this
     *
     * @param socketConfig the socket config of this instance
     */

    private UDPMessenger(@NonNull SocketConfig socketConfig) {
        if (Looper.myLooper() != Looper.getMainLooper()) {
            throw new IllegalStateException("UDPMessenger need to be initialized within the UIThread");
        }
        handler = new Handler();
        this.socketConfig = socketConfig;
    }

    public static UDPMessenger getDefaultInstance() {
        return getInstance(defaultSocketConfig);
    }

    /**
     * @param socketConfig the socketConfig for the instance
     * @return the TCPMessenger, if instance was already created a new one wont be created unless that instance
     * is release with {@link #releaseInstance(UDPMessenger)}
     */
    public static UDPMessenger getInstance(@NonNull SocketConfig socketConfig) {
        UDPMessenger UDPMessenger = instanceCache.get(socketConfig);
        if (UDPMessenger == null) {
            synchronized (UDPMessenger.class) {
                UDPMessenger = instanceCache.get(socketConfig);
                if (UDPMessenger == null) {
                    UDPMessenger = new UDPMessenger(socketConfig);
                    instanceCache.put(socketConfig, UDPMessenger);
                    DiagnosticService.addMonitor(new UDPDiagnostic(UDPMessenger), SocketTimeoutException.class);
                }
            }
        }

        return UDPMessenger;
    }

    /**
     * Release any previously initialized TCPMessenger instance
     *
     * @param udpMessenger the instance to be released
     *                     <p>please note that if instance is released it cant be used anymore</p>
     */
    public static void releaseInstance(@NonNull UDPMessenger udpMessenger) {
        synchronized (UDPMessenger.class) {
            instanceCache.remove(udpMessenger.getSocketConfig());
            udpMessenger.shutdown();
        }
    }

    /**
     * Get this instance socketConfig
     *
     * @return the socketConfig of this
     */
    private SocketConfig getSocketConfig() {
        return socketConfig;
    }

    private DatagramSocket getClientFor(String port) throws IOException {
        TrafficStats.setThreadStatsTag((int) Thread.currentThread().getId());

        synchronized (this) {
            if (client == null || client.isClosed()) {

                if (client != null) {
                    try {
                        client.close();//it could be already closed but for the sake of it.
                    } catch (Exception e) {
                    }

                }

                client = new DatagramSocket(Integer.parseInt(port));
                client.setSoTimeout(socketConfig.timeOut);
            }
        }
        return client;
    }

    public void shutdown() {
        executorService.shutdownNow();
    }

    public <T> void readCommand(String
                                        port, Class<T> responseClass, Callback<T> callback) {
        try {
            dequeueCommand(port, responseClass, callback);
        } catch (Exception e) {
            throw e;
        }
    }

    private <T> Future dequeueCommand(final String port, final Class<T> responseClass, final Callback<T> callback) {
        if (executorService.isShutdown()) {
            callback.onError(new IllegalStateException("executorService is shutdown"));
        }

        return executorService.submit(new Runnable() {
            @Override
            public void run() {
                while(true) {
                    //Log.d(this.getClass().getSimpleName(), "UDP dequeueCommand");
                    final Object[][] o = new Object[1][2];
                    try {
                        o[0][0] = doReadCommand(port, responseClass);
                    } catch (Exception e) {
                        Log.e("UDPMessenger", "dequeueCommand", e);
                        o[0][1] = e;
                    }

                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            final T response;
                            if ((response = (T) o[0][0]) != null) {
                                try {
                                   // Log.d(this.getClass().getSimpleName(), "UDP dequeueCommand message: " + response.toString());
                                    callback.onUDPResponse(response);
                                } catch (InterruptedException e) {
                                    Log.e("dequeueCommand", e);
                                    callback.onError(e);
                                }

                            } else {
                                //Log.d(this.getClass().getSimpleName(), "UDP dequeueCommand Error");
                                callback.onError(o[0][1] == null ? null : (Throwable) o[0][1]);
                            }
                        }
                    });
                }
            }
        });

    }

    private void initMapper() {
        if (objectMapper == null) {
            objectMapper = new ObjectMapper();
        }
    }

    private <T> T doReadCommand(String port, Class<T> responseClass) throws Exception {
        initMapper();

        final DatagramSocket client = getClientFor(port);
        T response = null;
        synchronized (client) {
            try {
                byte[] recvBuf = new byte[1500];
                DatagramPacket packet = new DatagramPacket(recvBuf, recvBuf.length);
                client.receive(packet);
                String str = new String(packet.getData()).trim();

                if (str != null) {
                    if (Utils.getTypeName(responseClass).equals("java.lang.String")) {
                        response = (T) str;
                    } else {
                        final JsonParser jsonReader = objectMapper.getFactory().createParser(str);
                        response = jsonReader.readValueAs(responseClass);
                    }
                }

            } catch (SocketException e) {
                try {
                    client.close();
                } catch (Exception e1) {
                    Log.e("UDPMessenger", "doReadCommand", e1);
                    throw e1;

                }
                Log.e("UDPMessenger", "doReadCommand", e);
                throw e;
            }
        }

        return response;
    }

    /**
     * Callback to be register to {@link TCPMessenger} in order to invoke request callback
     *
     * @param <T> Type T of the expect request response
     */
    public interface Callback<T> {

        /**
         * Invoke when request was successful
         *
         * @param response
         */
        //void onResponse(Request request,T t);
        void onResponse(T response) throws InterruptedException;

        void onUDPResponse(T response) throws InterruptedException;

        /**
         * Invoke when an error occur
         *
         * @param throwable the throwable thrown when the error occur
         */
        //void onError(Request request, Throwable throwable);
        void onError(Throwable throwable);


    }
}



