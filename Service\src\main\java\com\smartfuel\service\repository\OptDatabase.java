package com.smartfuel.service.repository;

import androidx.room.Database;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.smartfuel.service.sqlite.dao.IRewardCardDiscount;
import com.smartfuel.service.sqlite.models.Configuration;
import com.smartfuel.service.sqlite.models.ForecourtControllerTransaction;
import com.smartfuel.service.sqlite.models.FuelTransaction;
import com.smartfuel.service.sqlite.dao.IConfiguration;
import com.smartfuel.service.sqlite.dao.IFuelTransaction;
import com.smartfuel.service.sqlite.dao.IForecourtControllerTransaction;
import com.smartfuel.service.sqlite.dao.ITransaction;
import com.smartfuel.service.sqlite.models.RewardCardDiscount;
import com.smartfuel.service.sqlite.models.Transaction;

@Database(entities = {Configuration.class, Transaction.class, FuelTransaction.class, ForecourtControllerTransaction.class, RewardCardDiscount.class}
        , version = 2)

public abstract class OptDatabase extends RoomDatabase {
    static final int VERSION = 2;

    public abstract IConfiguration getConfigurationDao();

    public abstract IFuelTransaction getFuelTransactionDao();

    public abstract IForecourtControllerTransaction getForeceourtControllerTransactionDao();

    public abstract ITransaction getTransactionDao();

    public abstract IRewardCardDiscount getRewardCardDiscountDao();

    public static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            database.execSQL("CREATE TABLE IF NOT EXISTS `RewardCardDiscount` (`internalId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `id` TEXT, `fuelGrade` TEXT, `discount` INTEGER NOT NULL, `cap` INTEGER, `totalDiscountApplied` INTEGER NOT NULL, `transactionId` INTEGER NOT NULL)");
            database.execSQL("CREATE INDEX IF NOT EXISTS `index_RewardCardDiscount_internalId` ON `RewardCardDiscount` (`internalId`)");
        }
    };
}
