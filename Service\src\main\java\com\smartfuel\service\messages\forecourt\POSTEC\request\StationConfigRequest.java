package com.smartfuel.service.messages.forecourt.POSTEC.request;

public class StationConfigRequest extends BaseRequest {
    public StationConfigRequest(int nodeNumber) {
        super(nodeNumber, 26, 1, "Req_Station_Config");
    }

    @Override
    public String toString() {
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
