package com.smartfuel.service.escposprinter;

import android.graphics.Bitmap;

public abstract class EscPosPrinterSize {
    public static final float INCH_TO_MM = 25.4F;

    protected int printerCharSizeWidthPx;

    protected int printerDpi;

    protected int printerNbrCharactersPerLine;

    protected float printerWidthMM;

    protected int printerWidthPx;

    protected EscPosPrinterSize(int paramInt1, float paramFloat, int paramInt2) {
        this.printerDpi = paramInt1;
        this.printerWidthMM = paramFloat;
        this.printerNbrCharactersPerLine = paramInt2;
        paramInt1 = mmToPx(paramFloat);
        this.printerWidthPx = paramInt1 % 8 + paramInt1;
        this.printerCharSizeWidthPx = paramInt1 / this.printerNbrCharactersPerLine;
    }

    public byte[] bitmapToBytes(Bitmap paramBitmap) {
        int i = 0;
        int j = paramBitmap.getWidth();
        int k = paramBitmap.getHeight();
        int m = this.printerWidthPx;
        int n = j;
        int i1 = k;
        if (j > m) {
            i1 = Math.round(k * m / j);
            n = m;
            i = 1;
        }
        k = i;
        m = n;
        i = i1;
        if (i1 > 256) {
            m = Math.round(n * 'Ā' / i1);
            i = 256;
            k = 1;
        }
        Bitmap bitmap = paramBitmap;
        if (k != 0)
            bitmap = Bitmap.createScaledBitmap(paramBitmap, m, i, true);
        return EscPosPrinterCommands.bitmapToBytes(bitmap);
    }

    public int getPrinterCharSizeWidthPx() {
        return this.printerCharSizeWidthPx;
    }

    public int getPrinterDpi() {
        return this.printerDpi;
    }

    public int getPrinterNbrCharactersPerLine() {
        return this.printerNbrCharactersPerLine;
    }

    public float getPrinterWidthMM() {
        return this.printerWidthMM;
    }

    public int getPrinterWidthPx() {
        return this.printerWidthPx;
    }

    public int mmToPx(float paramFloat) {
        return Math.round(this.printerDpi * paramFloat / 25.4F);
    }
}

