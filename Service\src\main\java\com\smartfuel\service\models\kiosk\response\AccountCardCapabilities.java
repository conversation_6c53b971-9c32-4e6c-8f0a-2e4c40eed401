package com.smartfuel.service.models.kiosk.response;

import java.util.List;

public class AccountCardCapabilities extends BaseResponse {

    private boolean promptVehicle;

    private boolean promptOdometer;

    private boolean validateVehicle;

    private boolean validateOdometer;

    private long vehicleOdometer;

    private List<String> vehicleRegistrations;

    private String inputVehicleRegistration;

    private String inputOdometer;

    private String errorMessage;

    public boolean hasPrompt(){
        return promptOdometer || promptVehicle;
    }

    public boolean validateVehicle(){
        errorMessage = "";
        if(promptVehicle && (inputVehicleRegistration == null || inputVehicleRegistration.isEmpty())) {
            errorMessage = "Vehicle registration must be entered.";
            return false;
        }

        if(validateVehicle && !vehicleRegistrations.contains(inputVehicleRegistration)) {
            errorMessage = "Vehicle registration entered was not found.";
            return false;
        }

        return true;
    }

    public boolean validateOdometer(){
        errorMessage = "";
        if(validateOdometer) {
            if(promptOdometer && (inputOdometer == null || inputOdometer.isEmpty())) {
                errorMessage = "Odometer must be entered.";
                return false;
            }

            long odometer = 0;
            try {
                odometer = Long.parseLong(inputOdometer);
            }catch (NumberFormatException e){
            }
            if(odometer <= vehicleOdometer) {
                errorMessage = "The odometer entered is less than your last reading.";
                return false;
            }
        }
        return true;
    }

    public String getErrorMessage(){
        return errorMessage;
    }

    public boolean isValid(){
        return validateVehicle() && validateOdometer();
    }

    public boolean isPromptVehicle() {
        return promptVehicle;
    }

    public void setPromptVehicle(boolean promptVehicle) {
        this.promptVehicle = promptVehicle;
    }

    public boolean isPromptOdometer() {
        return promptOdometer;
    }

    public void setPromptOdometer(boolean promptOdometer) {
        this.promptOdometer = promptOdometer;
    }

    public boolean isValidateVehicle() {
        return validateVehicle;
    }

    public void setValidateVehicle(boolean validateVehicle) {
        this.validateVehicle = validateVehicle;
    }

    public boolean isValidateOdometer() {
        return validateOdometer;
    }

    public void setValidateOdometer(boolean validateOdometer) {
        this.validateOdometer = validateOdometer;
    }

    public long getVehicleOdometer() {
        return vehicleOdometer;
    }

    public void setVehicleOdometer(long vehicleOdometer) {
        this.vehicleOdometer = vehicleOdometer;
    }

    public List<String> getVehicleRegistrations() {
        return vehicleRegistrations;
    }

    public void setVehicleRegistrations(List<String> vehicleRegistrations) {
        this.vehicleRegistrations = vehicleRegistrations;
    }

    public String getInputVehicleRegistration() {
        return inputVehicleRegistration;
    }

    public void setInputVehicleRegistration(String inputVehicleRegistration) {
        this.inputVehicleRegistration = inputVehicleRegistration;
    }

    public String getInputOdometer() {
        return inputOdometer;
    }

    public void setInputOdometer(String inputOdometer) {
        this.inputOdometer = inputOdometer;
    }
}
