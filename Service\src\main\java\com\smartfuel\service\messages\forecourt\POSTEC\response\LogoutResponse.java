package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

/*
01 002 0123 02 2000 Logout_Client_Node
 01 = client node number
 002 = request number
 0123 = sequence number
 02 = number of parameters
 2000 = socket success code (2) + PCC success code (000)
 */
public class LogoutResponse extends BaseResponse{

    public LogoutResponse(String response){
        super(response, NetworkMessageType.TCP);
        super.read();
    }
}
