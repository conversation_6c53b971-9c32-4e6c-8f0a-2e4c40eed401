package com.smartfuel.service.messages.forecourt.PSS5000.request;

import org.json.JSONException;
import com.google.gson.GsonBuilder;

public class AuthorizeFuellingPointRequest {
    private final FcAuthorizeFuellingPointRequestData data;

    private final String name = "authorize_Fp_req";

    private final String subCode = "02H";

    public AuthorizeFuellingPointRequest(String posId, String fuelpointId, String[] validGrades) {
        this.data = new FcAuthorizeFuellingPointRequestData(posId, fuelpointId, validGrades);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class AuthorizePars {
        private final String AutoLockId;

        private final String SmId;

        private final String[] ValidGrades;

        AuthorizePars(String posId, String smId, String[] validGrades) {
            this.AutoLockId = posId;
            this.SmId = smId;
            this.ValidGrades = validGrades;
        }
    }

    public class FcAuthorizeFuellingPointRequestData {
        private final AuthorizeFuellingPointRequest.AuthorizePars AuthorizePars;

        private final String FpId;

        private final String PosId;

        FcAuthorizeFuellingPointRequestData(String posId, String fuelPointId, String[] validGrades ) {
            this.FpId = fuelPointId;
            this.PosId = posId;
            this.AuthorizePars = new AuthorizeFuellingPointRequest.AuthorizePars(posId, "21", validGrades);
        }
    }
}
