package com.smartfuel.service.diagnostics;

import com.smartfuel.service.logger.Log;
import com.smartfuel.service.network.TCPMessenger;

import java.io.IOException;

public class TCPDiagnostic extends BaseDiagnostic {

    private TCPMessenger tcpMessenger;

    public TCPDiagnostic(TCPMessenger tcpMessenger){
        this.tcpMessenger = tcpMessenger;
    }

    @Override
    protected int getDelay() {
        return 5000;
    }

    @Override
    protected boolean check() {
        return tcpMessenger.isConnected();
    }

    @Override
    protected void reestablish() {
        try {
            tcpMessenger.reconnect();
        } catch (IOException e) {
            Log.w("SocketDiagnostic", e);
        }
    }
}
