package com.smartfuel.service.posmaster;


import android.content.Context;
import android.net.TrafficStats;

import com.smartfuel.service.logger.Log;

import androidx.annotation.NonNull;

import com.smartfuel.service.models.posmaster.FuelPriceUpdate;
import com.smartfuel.service.models.posmaster.OPTExport;

import org.apache.commons.io.FileUtils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.NoRouteToHostException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;
import java.util.Vector;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import javax.net.ssl.HttpsURLConnection;

public class POSMaster implements IPOSMaster {
    private final String host;
    private final String storeId;
    private final String encodedAuth;

    private final String ftphost;
    private final String ftpencodedAuth;

    public POSMaster(String Host, String StoreId, String Username, String Password, String FtpHost, String FtpUsername, String FtpPassword) throws MalformedURLException {
        this.host = Host;
        this.storeId = StoreId;
        String auth = Username + ":" + Password;
        this.encodedAuth = android.util.Base64.encodeToString((auth).getBytes(), android.util.Base64.DEFAULT);

        this.ftphost = FtpHost;
        if(FtpUsername != null && !FtpUsername.equals("")){
            auth = FtpUsername + ":" + FtpPassword;
            this.ftpencodedAuth = android.util.Base64.encodeToString((auth).getBytes(), android.util.Base64.DEFAULT);
        }
        else
            this.ftpencodedAuth = "";
    }
    @Override
    public void HeartBeat(Context serviceContext,final IPOSMasterCallBack<FuelPriceUpdate> callBack) {
        try{
            POSMasterAPIResponse<FuelPriceUpdate> result = fuelPriceCheckAPICall(serviceContext);
            callBack.onComplete(result);

        }
        catch (IOException ioE)
        {
            //TODO: determine how best to report this exception - extend kiosk API to have error reporting endpoint?
        }


    }

    @Override
    public void CatalogApplied(Context serviceContext, String CatalogId, String CatalogType) {
        String posMasterHost = this.host;
        String posMasterStoreId = this.storeId;
        ExecutorService myExecutor = Executors.newSingleThreadExecutor();
        Runnable myRunnable = new Runnable() {
            @Override
            public void run() {
                HttpsURLConnection POSMasterConnection = null;
                String Id = "\"" + CatalogId + "\"";
                try {
                    URL syncURL = new URL(String.format("%s/api/Synchronizer/file_downloaded/%s/%s", posMasterHost, posMasterStoreId, CatalogType));

                    TrafficStats.setThreadStatsTag((int) Thread.currentThread().getId());
                    POSMasterConnection = (HttpsURLConnection) syncURL.openConnection();

                    POSMasterConnection.setRequestMethod("POST");
                    POSMasterConnection.setRequestProperty("Content-Type", "application/json");
                    POSMasterConnection.setDoOutput(true);
                    POSMasterConnection.setRequestProperty( "charset", "utf-8");
                    POSMasterConnection.setRequestProperty( "Content-Length", Integer.toString( Id.length() ));
                    POSMasterConnection.setUseCaches( false );
                    POSMasterConnection.setRequestProperty("Authorization", "Basic " + encodedAuth);

                    try (DataOutputStream out = new DataOutputStream(POSMasterConnection.getOutputStream())){
                        out.write(Id.getBytes("utf-8"));}

                    POSMasterConnection.connect();
                    if (POSMasterConnection.getResponseCode() != HttpsURLConnection.HTTP_OK) {
                        Exception e = new Exception(String.format("Failed to apply catalog: %s", POSMasterConnection.getResponseCode()));
                    }
                    else{
                    }
                } catch (IOException e) {

                } finally {
                    POSMasterConnection.disconnect();
                }
            }
        };
        myExecutor.execute(myRunnable);
    }

    @Override
    public boolean TransactionDataUpload(Context serviceContext, OPTExport export)
    {
        boolean uploadResult = false;
        // use the transaction list to generate the relevant files for upload to POSMaster
        // need to generate the following files
        Vector<String> files = new Vector<String>(5);
        File direct = new File(serviceContext.getDataDir(), "/files/workspace");

        if(!direct.exists()) {
            if(direct.mkdir()); //directory is created;
        }
        // make the Envelope text file
        String filename = String.format("%s%s", direct, "/Envelope.txt");

        try {
            FileOutputStream fos = new FileOutputStream(filename);
            String line = String.format("%s\r\n", this.storeId);
            fos.write(line.getBytes(StandardCharsets.UTF_8));
            fos.flush();
            fos.close();
            files.add(filename);

            // Orders
            filename = String.format("%s/Orders.csv", direct);
            VectorToCsv(filename, export.getOrders());
            files.add(filename);
            // Order Items
            filename = String.format("%s/OrderItems.csv", direct);
            VectorToCsv(filename, export.getOrderItems());
            files.add(filename);
            // Transactions
            filename = String.format("%s/Transactions.csv", direct);
            VectorToCsv(filename, export.getTransactions());
            files.add(filename);
            // Fuel Transactions
            filename = String.format("%s/FuelTransactions.csv", direct);
            VectorToCsv(filename, export.getFuelTransactions());
            files.add(filename);

            String zipFile = CompressFile(files, direct.toString());
            if (zipFile.length() != 0) {
                uploadResult = UploadFile(zipFile);
                File zip = new File(zipFile);
                zip.delete();
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }


        return uploadResult;

    }

    private boolean UploadFile(String filename) {
        //FTPClient posmasterFtpClient = null;
        HttpURLConnection POSMasterConnection = null;
        boolean result = false;
        String charset = "UTF-8";
        File binaryFile = new File(filename);
        String boundary = Long.toHexString(System.currentTimeMillis());
        String CRLF = "\r\n";

        try {
            URL syncURL = new URL(String.format("%s", this.ftphost));

            TrafficStats.setThreadStatsTag((int) Thread.currentThread().getId());
            POSMasterConnection = (HttpURLConnection) syncURL.openConnection();
            POSMasterConnection.setRequestMethod("POST");
            POSMasterConnection.setConnectTimeout(20000);

            POSMasterConnection.setDoOutput(true);
            POSMasterConnection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
            if(!ftpencodedAuth.equals(""))
                POSMasterConnection.setRequestProperty("Authorization", "Basic " + ftpencodedAuth);


            DataOutputStream request = new DataOutputStream(POSMasterConnection.getOutputStream());
            request.writeBytes("--" + boundary + "\r\n");
            request.writeBytes("Content-Disposition: form-data; name=\"file\"; filename=\"" + filename + "\"\r\n\r\n");
            request.write(FileUtils.readFileToByteArray(binaryFile));
            request.writeBytes("\r\n");
            request.writeBytes("--" + boundary + "--\r\n");
            request.flush();

            int responseCode = POSMasterConnection.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) {
                InputStream inputStream =POSMasterConnection.getInputStream();

                String text = new BufferedReader(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                        .lines()
                        .collect(Collectors.joining("\n"));
                result = true;
            }
            else
            {
                Log.e("Upload Failure",String.valueOf(responseCode), new Exception(String.valueOf(responseCode)));
            }

        } catch (NoRouteToHostException e) {
            Log.w("Upload Failure",e.getMessage());
        } catch (Exception e) {
            if(e.getMessage().contains("Failed to connect") || e.getMessage().contains("Connection refused"))
                Log.w("Upload Failure",e.getMessage());
            else
                Log.e("Upload Failure",e);

        } finally {
            if(POSMasterConnection != null)
                POSMasterConnection.disconnect();
        }
        return result;

    }
    private String CompressFile(Vector<String> filename, String directory) {
        String zipFileName = directory + "/" + (new SimpleDateFormat("yyyy-MM-dd_HHmmss")).format(new Date()) + ".zip";
        boolean success = true;
        try {
            Integer BUFFER = 1024;
            BufferedInputStream origin = null;
            FileOutputStream dest = new FileOutputStream(zipFileName);
            ZipOutputStream out = new ZipOutputStream(new BufferedOutputStream(
                    dest));
            byte data[] = new byte[BUFFER];

            for (String _file : filename) {
                Log.v("Compress", "Adding: " + _file);
                FileInputStream fi = new FileInputStream(_file);

                origin = new BufferedInputStream(fi, BUFFER);

                ZipEntry entry = new ZipEntry(_file.substring(_file.lastIndexOf("/") + 1));
                out.putNextEntry(entry);
                int count;

                while ((count = origin.read(data, 0, BUFFER)) != -1) {
                    out.write(data, 0, count);
                }
                origin.close();
            }

            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return success ? zipFileName : "";
    }
    private void

    VectorToCsv(String filename, @NonNull List<?> data) {
        FileOutputStream file = null;
        try {
            file = new FileOutputStream(filename);
            StringJoiner stringJoiner = new StringJoiner("\r\n");
            data.forEach(
                    a -> stringJoiner.add(a.toString()));
            file.write((stringJoiner.toString()).getBytes(StandardCharsets.UTF_8));
            file.flush();
            file.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    private POSMasterAPIResponse<FuelPriceUpdate> fuelPriceCheckAPICall(Context serviceContext) throws IOException {
        ArrayList<String> files = new ArrayList<>();
        String zipFile = "temp_catalog.zip";
        HttpsURLConnection POSMasterConnection = null;
        FuelPriceUpdate fpcr = new FuelPriceUpdate();
        try {
            URL syncURL = new URL(String.format("%s/api/Synchronizer/heartbeat/%s", this.host, this.storeId));
            TrafficStats.setThreadStatsTag((int) Thread.currentThread().getId());
            POSMasterConnection = (HttpsURLConnection) syncURL.openConnection();
            POSMasterConnection.setRequestMethod("GET");
            POSMasterConnection.setRequestProperty("Authorization", "Basic " + encodedAuth);
            POSMasterConnection.setChunkedStreamingMode(0);
            POSMasterConnection.setDoOutput(true);


            int responseCode = POSMasterConnection.getResponseCode();
            if (responseCode > 200) {
                throw new IOException("Invalid response from server: " + responseCode);
            }
            InputStream inputStream = POSMasterConnection.getInputStream();
            OutputStream outputStream = new BufferedOutputStream(serviceContext.openFileOutput(zipFile,Context.MODE_PRIVATE));
            int nRead;
            byte[] data = new byte[1];

            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                outputStream.write(data, 0, nRead);
            }
            inputStream.close();
            outputStream.flush();
            outputStream.close();
            //extract files from zip
            fpcr.status = POSMasterConnection.getResponseCode();
            fpcr.success = true;
            fpcr.filePaths = Decompress(serviceContext, zipFile);
            return new POSMasterAPIResponse.Success<>(fpcr);

        } catch (MalformedURLException e) {Log.e("POSMaster","Heartbeat",e);
        } catch (ConnectException e) {Log.e("POSMaster","Heartbeat",e);}
         catch (IOException e) {Log.e("POSMaster","Heartbeat",e);}
        finally {
            if (POSMasterConnection != null) {
                POSMasterConnection.disconnect();
            }
        }
        return null;
    }

    @NonNull
    private List<String> Decompress(Context context, String zipFile) throws IOException {
        List<String> files = new ArrayList<>();
        try {
            FileInputStream fin = context.openFileInput(zipFile);
            ZipInputStream zin = new ZipInputStream(fin);
            ZipEntry ze = null;
            while ((ze = zin.getNextEntry()) != null) {

                //create dir if required while unzipping
                if (ze.isDirectory()) {
                    //dirChecker(ze.getName());
                } else {
                    try (FileOutputStream fos = context.openFileOutput(ze.getName(), Context.MODE_PRIVATE)) {
                        for (int c = zin.read(); c != -1; c = zin.read()) {
                            fos.write(c);
                        }
                        files.add(ze.getName());
                        zin.closeEntry();
                    }
                }
            }
            zin.close();
            context.deleteFile(zipFile);
        } catch (IOException e) {
            throw e;
        }
        return files;
    }

}

