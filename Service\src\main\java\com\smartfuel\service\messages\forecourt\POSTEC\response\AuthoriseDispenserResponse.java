package com.smartfuel.service.messages.forecourt.POSTEC.response;


import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

/*TODO: Check for the below scenario.
 * If ResponseCode = rcSavedTransaction, the fields following
 * Dispenser are returned for a previous transaction which has
 * been shifted into the PCC transaction memory stack as a result
 * of the authorization.
 */
public class AuthoriseDispenserResponse extends BaseResponse {
    private int dispenser;
    private int hoseNumber;
    private int gradeNumber;
    private long money;
    private long volume;
    private long unitPrice;

    public AuthoriseDispenserResponse(String response)  throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenser = Integer.parseInt(super.responseData[5]);
    }

    public int getDispenser() {
        return dispenser;
    }
    public int getHoseNumber() { return hoseNumber; }
    public int getGradeNumber() { return gradeNumber; }
    public long getMoney() { return money; }
    public long getVolume() { return volume; }
    public long getUnitPrice() { return unitPrice; }
}
