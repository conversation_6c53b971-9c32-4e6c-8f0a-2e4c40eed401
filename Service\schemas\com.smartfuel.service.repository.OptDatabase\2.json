{"formatVersion": 1, "database": {"version": 2, "identityHash": "f830104fd8065f5675da52ced0c63914", "entities": [{"tableName": "Configuration", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `value` TEXT, PRIMARY KEY(`name`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["name"]}, "indices": [{"name": "index_Configuration_name", "unique": false, "columnNames": ["name"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_Configuration_name` ON `${TABLE_NAME}` (`name`)"}], "foreignKeys": []}, {"tableName": "Transaction", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `AccountType` TEXT, `aid` TEXT, `authorisationId` TEXT, `authorisedAmount` INTEGER NOT NULL, `cardSignature` TEXT, `cvm` TEXT, `exported` INTEGER NOT NULL, `finalAmount` INTEGER NOT NULL, `fuelTransactionId` INTEGER NOT NULL, `hostResponse` TEXT, `kioskPublished` INTEGER NOT NULL, `panSeqNumber` TEXT, `processor` TEXT, `receiptPrintCount` INTEGER NOT NULL, `responseCode` TEXT, `rrn` INTEGER NOT NULL, `stan` INTEGER NOT NULL, `status` INTEGER NOT NULL, `terminalId` INTEGER NOT NULL, `terminalTransactionId` INTEGER NOT NULL, `time` INTEGER NOT NULL, `track2` TEXT, `trxCount` INTEGER NOT NULL, `type` INTEGER NOT NULL, `currency` INTEGER NOT NULL, `cardExpiryDate` TEXT, `applicationLabel` TEXT, `atc` INTEGER NOT NULL, `tid` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "AccountType", "columnName": "AccountType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "aid", "columnName": "aid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "authorisationId", "columnName": "authorisationId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "authorisedAmount", "columnName": "authorisedAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cardSignature", "columnName": "cardSignature", "affinity": "TEXT", "notNull": false}, {"fieldPath": "cvm", "columnName": "cvm", "affinity": "TEXT", "notNull": false}, {"fieldPath": "exported", "columnName": "exported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "finalAmount", "columnName": "finalAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fuelTransactionId", "columnName": "fuelTransactionId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hostResponse", "columnName": "hostResponse", "affinity": "TEXT", "notNull": false}, {"fieldPath": "kioskPublished", "columnName": "kioskPublished", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "panSeqNumber", "columnName": "panSeqNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "processor", "columnName": "processor", "affinity": "TEXT", "notNull": false}, {"fieldPath": "receiptPrintCount", "columnName": "receiptPrintCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "responseCode", "columnName": "responseCode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "rrn", "columnName": "rrn", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stan", "columnName": "stan", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "terminalId", "columnName": "terminalId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "terminalTransactionId", "columnName": "terminalTransactionId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "track2", "columnName": "track2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "trxCount", "columnName": "trxCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currency", "columnName": "currency", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cardExpiryDate", "columnName": "cardExpiryDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "applicationLabel", "columnName": "applicationLabel", "affinity": "TEXT", "notNull": false}, {"fieldPath": "atc", "columnName": "atc", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tid", "columnName": "tid", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_Transaction_id", "unique": false, "columnNames": ["id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_Transaction_id` ON `${TABLE_NAME}` (`id`)"}], "foreignKeys": []}, {"tableName": "FuelTransaction", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `gradeId` TEXT, `money` INTEGER NOT NULL, `moneyDue` INTEGER NOT NULL, `price` INTEGER NOT NULL, `pumpNumber` INTEGER NOT NULL, `transInfoMask` TEXT, `transSeqNumber` TEXT, `vehicleOdometer` TEXT, `vehicleRegistration` TEXT, `volume` INTEGER NOT NULL, `whitecardAccountEmail` TEXT, `whitecardAccountId` TEXT, `whitecardAccountName` TEXT, `whitecardUserId` TEXT, `whitecardUserName` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gradeId", "columnName": "gradeId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "money", "columnName": "money", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "moneyDue", "columnName": "moneyDue", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "price", "columnName": "price", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pumpNumber", "columnName": "pumpNumber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "transInfoMask", "columnName": "transInfoMask", "affinity": "TEXT", "notNull": false}, {"fieldPath": "transSeqNumber", "columnName": "transSeqNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "vehicleOdometer", "columnName": "vehicleOdometer", "affinity": "TEXT", "notNull": false}, {"fieldPath": "vehicleRegistration", "columnName": "vehicleRegistration", "affinity": "TEXT", "notNull": false}, {"fieldPath": "volume", "columnName": "volume", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "whitecardAccountEmail", "columnName": "whitecardAccountEmail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "whitecardAccountId", "columnName": "whitecardAccountId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "whitecardAccountName", "columnName": "whitecardAccountName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "whitecardUserId", "columnName": "whitecardUserId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "whitecardUserName", "columnName": "whitecardUserName", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_FuelTransaction_id_transSeqNumber", "unique": false, "columnNames": ["id", "transSeqNumber"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_FuelTransaction_id_transSeqNumber` ON `${TABLE_NAME}` (`id`, `transSeqNumber`)"}], "foreignKeys": []}, {"tableName": "ForecourtControllerTransaction", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`Fc<PERSON>radeId` TEXT NOT NULL, `fpId` INTEGER NOT NULL, `fpLockId` INTEGER NOT NULL, `fpMainState` TEXT, `fpSubState2` TEXT, `fpSubState4` TEXT, `fuellingDataMonE` INTEGER NOT NULL, `fuellingDataVolE` INTEGER NOT NULL, `time` INTEGER NOT NULL, PRIMARY KEY(`fpId`, `fpLockId`, `FcGradeId`))", "fields": [{"fieldPath": "FcGradeId", "columnName": "FcGradeId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fpId", "columnName": "fpId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fpLockId", "columnName": "fpLockId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fpMainState", "columnName": "fpMainState", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fpSubState2", "columnName": "fpSubState2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fpSubState4", "columnName": "fpSubState4", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fuellingDataMonE", "columnName": "fuellingDataMonE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fuellingDataVolE", "columnName": "fuellingDataVolE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["fpId", "fpLockId", "FcGradeId"]}, "indices": [{"name": "index_ForecourtControllerTransaction_fpId_fpLockId_FcGradeId", "unique": false, "columnNames": ["fpId", "fpLockId", "FcGradeId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_ForecourtControllerTransaction_fpId_fpLockId_FcGradeId` ON `${TABLE_NAME}` (`fpId`, `fpLockId`, `FcGradeId`)"}], "foreignKeys": []}, {"tableName": "<PERSON><PERSON><PERSON>ardD<PERSON>unt", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`internalId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `id` TEXT, `fuelGrade` TEXT, `discount` INTEGER NOT NULL, `cap` INTEGER, `totalDiscountApplied` INTEGER NOT NULL, `transactionId` INTEGER NOT NULL)", "fields": [{"fieldPath": "internalId", "columnName": "internalId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fuelGrade", "columnName": "fuelGrade", "affinity": "TEXT", "notNull": false}, {"fieldPath": "discount", "columnName": "discount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cap", "columnName": "cap", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalDiscountApplied", "columnName": "totalDiscountApplied", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "transactionId", "columnName": "transactionId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["internalId"]}, "indices": [{"name": "index_RewardCardDiscount_internalId", "unique": false, "columnNames": ["internalId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_RewardCardDiscount_internalId` ON `${TABLE_NAME}` (`internalId`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'f830104fd8065f5675da52ced0c63914')"]}}