package com.smartfuel.service.messages.forecourt.POSTEC.enums;

public enum TransactionType {
    SaleDownload('S'),
    MemoryDownload('M'),
    EFTSaleDownload('C'),
    EFTMemoryDownload('D');

    private final char[] type;
    private TransactionType(char type) {
        this.type = new char[]{type};
    }

    public char[] getType() {
        return type;
    }

    public static TransactionType getPumpStatusById(char[] type) {
        for (TransactionType ps : values()) {
            if (ps.getType() == type)
                return ps;
        }
        return null;
    }
}
