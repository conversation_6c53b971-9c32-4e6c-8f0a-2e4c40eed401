plugins {
    id 'com.android.library'
    id 'com.google.gms.google-services'
    id("com.google.firebase.crashlytics")
}

android {
    namespace 'com.smartfuel.service'
    compileSdk 33

    defaultConfig {
        minSdk 25
        targetSdk 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField 'String', 'kiosk_api','"https://apiopt.posmaster.app"' // api.opt.posmaster.com.au
            buildConfigField 'Integer', 'printerVendorId','19267' //8401
            buildConfigField 'Integer', 'printerProductId','14384' //28680
        }
        debug {
            debuggable true
            buildConfigField 'String', 'kiosk_api','"https://apioptstaging.posmaster.com.au"' // api.staging.opt.posmaster.com.au
            buildConfigField 'Integer', 'printerVendorId','19267'
            buildConfigField 'Integer', 'printerProductId','14384'
        }

    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'

    implementation 'commons-io:commons-io:2.11.0' // DON'T CHANGE  - New version will break build
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'

    implementation platform('com.google.firebase:firebase-bom:32.2.2')
    implementation("com.google.firebase:firebase-crashlytics")
    implementation("com.google.firebase:firebase-analytics")

    //JSON Libs
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.12.7.1'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.12.7'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.12.7'

    //Dependency Injection
    implementation 'com.google.dagger:dagger:2.45'
    implementation 'com.google.dagger:dagger-android:2.45'
    annotationProcessor 'com.google.dagger:dagger-android-processor:2.45'
    annotationProcessor 'com.google.dagger:dagger-compiler:2.45'

    //Printer Support Libs
    implementation 'com.google.zxing:core:3.5.1'

    //SQLite Room Database
    implementation "androidx.room:room-runtime:2.5.1"
    annotationProcessor "androidx.room:room-compiler:2.5.1"

    //REST API
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    //logback
    implementation 'com.sun.mail:android-mail:1.6.7'
    implementation 'com.sun.mail:android-activation:1.6.7'
}