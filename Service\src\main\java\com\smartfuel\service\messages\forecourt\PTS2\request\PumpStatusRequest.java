package com.smartfuel.service.messages.forecourt.PTS2.request;

import java.util.ArrayList;

public class PumpStatusRequest {
    private ArrayList<Packet> Packets;
    private String Protocol;
    public PumpStatusRequest(int pumpNumber)
    {

        Data d = new Data();
        d.setPump(pumpNumber);

        Packet p = new Packet();
        p.setId(1);
        p.setType("PumpGetStatus");
        p.setData(d);

        this.Protocol = "jsonPTS";

        Packets = new ArrayList<Packet>();
        this.Packets.add(p);
    }

    public class Packet {
        public int getId() {
            return Id;
        }

        public void setId(int id) {
            this.Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            this.Type = type;
        }

        private int Id;
        private String Type;

        public PumpStatusRequest.Data getData() {
            return Data;
        }

        public void setData(PumpStatusRequest.Data data) {
            Data = data;
        }

        private Data Data;
    }
    public class Data{
        public int getPump() {
            return Pump;
        }

        public void setPump(int pump) {
            Pump = pump;
        }

        private int Pump;
    }
}
