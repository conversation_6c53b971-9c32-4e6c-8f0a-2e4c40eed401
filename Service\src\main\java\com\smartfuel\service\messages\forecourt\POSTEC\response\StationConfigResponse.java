package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

/*
Node Number:            07
Request/Command Number: 026
Sequence Number:        3147
Parameter Count:        10
Response Status:        2000
Number of Dispensers:   01
Number of Grades:       04
Number of Tanks:        05
Number DCAs:            0
Top Node:               7
Display Running Sales:  1
CRIP Control Flag:      0
Site End of Day Time:   0000
Function Name:          Req_Station_Config
 */
public class StationConfigResponse extends BaseResponse{
    private int dispenserCount;
    private int gradeCount;
    public StationConfigResponse(String response){
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserCount = Integer.parseInt(super.responseData[5]);
        this.gradeCount = Integer.parseInt(super.responseData[6]);
    }

    public int getDispenserCount() {
        return dispenserCount;
    }

    public int getGradeCount() {
        return gradeCount;
    }
}
