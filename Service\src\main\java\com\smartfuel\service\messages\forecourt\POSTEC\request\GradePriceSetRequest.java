package com.smartfuel.service.messages.forecourt.POSTEC.request;

import java.util.Calendar;
import java.util.Date;

/**
 * To set the unit price for a specified grade to change at the scheduled time.
 * PCC Software > V6.38
 * When the PCC is configured with a price units digits setting of 5 or 6 digits, the PCC will
 * receive and send prices data up to 6 digits in length.
 */
public class GradePriceSetRequest extends BaseRequest {
    /*String gradeNumber, Date effectiveChangeDateTime, String gradePrice*/
    private char[] gradeNumber = new char[2];
    private char[] gradePrice = new char[6];
    private char[] cashFlag = new char[1];
    private char[] schedule = new char[1];
    private char[] controlFlag = new char[1];
    private char[] year = new char[4];
    private char[] month = new char[2];
    private char[] day = new char[2];
    private char[] hour = new char[2];
    private char[] minute = new char[2];

    public GradePriceSetRequest(int nodeNumber, int gradeNumber, Date effectiveChangeDateTime, long gradePrice, int gradeLength) {
        super(nodeNumber, 55, 11, "Set_Grade_Prices");

        this.gradeNumber = String.format("%02d", gradeNumber).toCharArray();
        this.gradePrice = String.format("%0" + gradeLength + "d", gradePrice).toCharArray();
        this.schedule = "?".toCharArray(); // price change applies to all operating schedules
        this.controlFlag = "1".toCharArray(); // Perform the price change without needing console confirmation
        this.cashFlag = "0".toCharArray();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(effectiveChangeDateTime);
        this.year = String.format("%04d", calendar.get(Calendar.YEAR)).toCharArray();
        this.month = String.format("%02d", calendar.get(Calendar.MONTH)).toCharArray();
        this.day = String.format("%02d", calendar.get(Calendar.DAY_OF_MONTH)).toCharArray();
        this.hour = String.format("%02d", calendar.get(Calendar.HOUR_OF_DAY)).toCharArray();
        this.minute = String.format("%02d", calendar.get(Calendar.MINUTE)).toCharArray();
    }

    @Override
    public String toString() {
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.gradeNumber);
        returnValue.append(" ");
        returnValue.append(this.cashFlag);
        returnValue.append(" ");
        returnValue.append(this.year);
        returnValue.append(" ");
        returnValue.append(this.month);
        returnValue.append(" ");
        returnValue.append(this.day);
        returnValue.append(" ");
        returnValue.append(this.hour);
        returnValue.append(" ");
        returnValue.append(this.minute);
        returnValue.append(" ");
        returnValue.append(this.gradePrice);
        returnValue.append(" ");
        returnValue.append(this.schedule);
        returnValue.append(" ");
        returnValue.append(this.controlFlag);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
