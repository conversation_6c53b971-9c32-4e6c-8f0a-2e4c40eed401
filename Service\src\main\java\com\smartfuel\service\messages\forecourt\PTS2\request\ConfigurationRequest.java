package com.smartfuel.service.messages.forecourt.PTS2.request;

import java.util.ArrayList;

public class ConfigurationRequest {
    public ArrayList<ConfigurationRequest.Packet> getPackets() {
        return Packets;
    }

    public void setPackets(ArrayList<ConfigurationRequest.Packet> packets) {
        this.Packets = packets;
    }
    private String Protocol;
    private ArrayList<ConfigurationRequest.Packet> Packets;

    public class Packet {
        public int getId() {
            return Id;
        }

        public void setId(int id) {
            this.Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            this.Type = type;
        }

        private int Id;
        private String Type;
    }

    public ConfigurationRequest(String type) {
        Packets = new ArrayList<ConfigurationRequest.Packet>();
        ConfigurationRequest.Packet packets = new ConfigurationRequest.Packet();
        packets.setId(1);
        packets.setType(type);
        this.Protocol = "jsonPTS";
        this.Packets.add(packets);
    }
}
