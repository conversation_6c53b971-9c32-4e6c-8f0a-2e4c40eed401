package com.smartfuel.service.models.posmaster;

public class OrderItem {
    public String getTerminalTransactionId() {
        return TerminalTransactionId;
    }

    public void setTerminalTransactionId(String terminalTransactionId) {
        TerminalTransactionId = terminalTransactionId;
    }

    public String getBarcode() {
        return Barcode;
    }

    public void setBarcode(String barcode) {
        Barcode = barcode;
    }

    public String getProductName() {
        return ProductName;
    }

    public void setProductName(String productName) {
        ProductName = productName;
    }

    public String getCost() {
        return Cost;
    }

    public void setCost(String cost) {
        Cost = cost;
    }

    public String getPrice() {
        return Price;
    }

    public void setPrice(String price) {
        Price = price;
    }

    public String getQuantity() {
        return Quantity;
    }

    public void setQuantity(String quantity) {
        Quantity = quantity;
    }

    public String getTaxable() {
        return Taxable;
    }

    public void setTaxable(String taxable) {
        Taxable = taxable;
    }

    public String getFGID() {
        return FGID;
    }

    public void setFGID(String FGID) {
        this.FGID = FGID;
    }

    public String getFTID() {
        return FTID;
    }

    public void setFTID(String FTID) {
        this.FTID = FTID;
    }

    @Override
    public String toString(){
        return TerminalTransactionId + "," + Barcode + "," + ProductName + "," + Cost + "," + Price + "," + Quantity + "," + Taxable + "," + FGID + "," + FTID;
    }

    private String TerminalTransactionId;
    private String Barcode;
    private String ProductName;
    private String Cost;
    private String Price;
    private String Quantity;
    private String Taxable;
    private String FGID;
    private String FTID;
}

