package com.smartfuel.service.connection.terminal;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.Messenger;
import android.os.PowerManager;
import android.os.RemoteException;
import android.provider.Settings;

import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.connection.IDeviceConnection;
import com.smartfuel.service.logger.Log;

public class PAXServiceConnection implements IDeviceConnection, ServiceConnection {

    private static final String TAG = PAXServiceConnection.class.getSimpleName();

    private static final String PACKAGE_NAME = "com.visionpay.opt.pax";
    private static final String PACKAGE_CLASS = "com.visionpay.opt.pax.service.MessageService";

    private static final String KEY_PARAM = "key_message";

    private Messenger _messenger = null; //used to make an RPC invocation
    private volatile boolean _isBound = false;
    private Messenger _replyTo = null;

    private Context context;
    private String dataReceive;
    private String dataSend;

    private static PAXServiceConnection instance = null;

    public static PAXServiceConnection getInstance(Context context){
        if(instance == null)
            instance = new PAXServiceConnection();

        instance.dataReceive = "";
        instance.dataSend = "";
        instance.context = context;
        instance.disableBatteryOptimization();

        return instance;
    }

    private PAXServiceConnection(){

    }

    private void disableBatteryOptimization() {
        PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);

        if (!pm.isIgnoringBatteryOptimizations(PACKAGE_NAME)) {
            Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            intent.setData(Uri.parse("package:" + PACKAGE_NAME));
            context.startActivity(intent);
        }
    }

    @Override
    public IDeviceConnection connect() throws ConnectionException {
        if (this.isConnected()) {
            return this;
        }

        this._replyTo = new Messenger(new IncomingHandler(message -> dataReceive += message));

        try {
            //Bind to the remote service
            Intent intent = new Intent();

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                intent.setComponent(new ComponentName(PACKAGE_NAME, PACKAGE_CLASS));
                context.startForegroundService(intent);
            }
            else{
                intent.setClassName(PACKAGE_NAME, PACKAGE_CLASS);
                context.startService(intent);
            }
            boolean bound = context.bindService(intent, this, Context.BIND_AUTO_CREATE);
            if (!bound) {
                throw new ConnectionException("Failed to bind to service");
            }
        }
        catch (Exception e){
            throw new ConnectionException(e.getMessage());
        }

        return this;
    }

    @Override
    public IDeviceConnection disconnect(){
        dataReceive = "";
        dataSend = "";
        if(this.isConnected())
        {
            context.unbindService(this);
            _isBound = false;
        }
        return this;
    }

    @Override
    public boolean isConnected(){
        return _isBound;
    }

    @Override
    public void onServiceConnected(ComponentName component, IBinder binder)
    {
        _messenger = new Messenger(binder);
        _isBound = true;
    }

    @Override
    public void onServiceDisconnected(ComponentName component)
    {
        _messenger = null;
        _isBound = false;
    }

    @Override
    public void write(byte[] bytes){
        dataSend += new String(bytes);
    }

    @Override
    public String read() throws ConnectionException{
        if (!this.isConnected()) {
            throw new ConnectionException("Unable to read data from device.");
        }
        String read = dataReceive;
        dataReceive = "";
        return read;
    }

    public void send() throws ConnectionException{
        this.send(0);
    }

    public void send(int addWaitingTime) throws ConnectionException{
        if (!this.isConnected()) {
            throw new ConnectionException("Unable to send data to device.");
        }

        Bundle bundle = new Bundle();
        bundle.putString(KEY_PARAM, dataSend);
        dataSend = "";

        //Setup the message for invocation
        Message message = Message.obtain(null, 1, 0, 0, bundle);
        try
        {
            //Set the ReplyTo Messenger for processing the invocation response
            message.replyTo = _replyTo;

            //Make the invocation
            _messenger.send(message);
            int waitingTime = addWaitingTime + dataSend.length() / 16;
            if (waitingTime > 0) {
                Thread.sleep(waitingTime);
            }
        }
        catch(RemoteException rme)
        {
            //Show an Error Message
            Log.e(TAG, rme);
            throw new ConnectionException("Invocation Failed!!");
        }
        catch (Exception e){
            Log.e(TAG, e);
            throw new ConnectionException(e.getMessage());
        }
    }

    private class IncomingHandler extends Handler
    {
        private final ClientConnectionListener _listener;

        public IncomingHandler(ClientConnectionListener listener){
            super(Looper.getMainLooper());
            _listener = listener;
        }

        @Override
        public void handleMessage(Message msg)
        {
            Bundle bundle = (Bundle) msg.obj;
            String message = bundle.getString(KEY_PARAM);
            _listener.onMessageReceive(message);
        }
    }

    private interface ClientConnectionListener {
        void onMessageReceive(String message);
    }
}

