package com.smartfuel.service.messages.forecourt.PTS2.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;

public class FuelGradesConfigurationResponse extends BaseResponse{
    public class Data{
        private ArrayList<FuelGrade> FuelGrades;

        public ArrayList<FuelGrade> getFuelGrades() {
            return FuelGrades;
        }

        public void setFuelGrades(ArrayList<FuelGrade> fuelGrades) {
            FuelGrades = fuelGrades;
        }
    }

    public class FuelGrade{
        private int Id;
        private String Name;
        private float Price;
        private float ExpansionCoefficient;
        private int BlendTank1Id;
        private int BlendTank2Id;
        private int BlendTank1Percentage;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            Id = id;
        }

        public String getName() {
            return Name;
        }

        public void setName(String name) {
            Name = name;
        }

        public float getPrice() {
            return Float.valueOf(String.format("%.1f", Price*100));
        }

        public void setPrice(float price) {
            Price = price;
        }

        public float getExpansionCoefficient() {
            return ExpansionCoefficient;
        }

        public void setExpansionCoefficient(float expansionCoefficient) {
            ExpansionCoefficient = expansionCoefficient;
        }

        public int getBlendTank1Id() {
            return BlendTank1Id;
        }

        public void setBlendTank1Id(int blendTank1Id) {
            BlendTank1Id = blendTank1Id;
        }

        public int getBlendTank2Id() {
            return BlendTank2Id;
        }

        public void setBlendTank2Id(int blendTank2Id) {
            BlendTank2Id = blendTank2Id;
        }

        public int getBlendTank1Percentage() {
            return BlendTank1Percentage;
        }

        public void setBlendTank1Percentage(int blendTank1Percentage) {
            BlendTank1Percentage = blendTank1Percentage;
        }
    }

    public class Packet{
        private int Id;
        private String Type;
        private Data Data;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public FuelGradesConfigurationResponse.Data getData() {
            return Data;
        }

        public void setData(FuelGradesConfigurationResponse.Data data) {
            Data = data;
        }
    }

        public ArrayList<Packet> Packets;

}
/*
{
    "Protocol": "jsonPTS",
    "Packets": [
        {
            "Id": 1,
            "Type": "FuelGradesConfiguration",
            "Data": {
                "FuelGrades": [
                    {
                        "Id": 1,
                        "Name": "E10",
                        "Price": 111.00,
                        "ExpansionCoefficient": 0.00110,
                        "BlendTank1Id": 0,
                        "BlendTank2Id": 0,
                        "BlendTank1Percentage": 0
                    }
                ]
            }
        }
    ]
}
* */