package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;


public class PCCVersionResponse extends BaseResponse {
    private String serverVersion;
    private String PCCSoftwareVersion;
    private String PCCSoftwareDate;
    private String PCCSoftwareCountry;
    private String PCCHardwareVersion;
    private String PCCSerialKey;
    private int networkType;

    public PCCVersionResponse(String response) throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.serverVersion = super.responseData[5];
        this.PCCSoftwareVersion = super.responseData[6];
        this.PCCSoftwareDate = super.responseData[7];
        this.PCCSoftwareCountry = super.responseData[8];
        this.PCCHardwareVersion = super.responseData[9];
        this.PCCSerialKey = super.responseData[10];
        this.networkType = Integer.parseInt(super.responseData[11]);
    }

    public String getServerVersion() {
        return serverVersion;
    }

    public String getPCCSoftwareVersion() {
        return PCCSoftwareVersion;
    }

    public String getPCCSoftwareDate() {
        return PCCSoftwareDate;
    }

    public String getPCCSoftwareCountry() {
        return PCCSoftwareCountry;
    }

    public String getPCCHardwareVersion() {
        return PCCHardwareVersion;
    }

    public String getPCCSerialKey() {
        return PCCSerialKey;
    }

    public int getNetworkType() {
        return networkType;
    }
}
