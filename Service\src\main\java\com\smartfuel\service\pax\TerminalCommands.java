package com.smartfuel.service.pax;

import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.connection.IDeviceConnection;
import com.smartfuel.service.logger.Log;

public class TerminalCommands {
    private IDeviceConnection terminalConnection;
    public String Data;
    public TerminalCommands(IDeviceConnection terminalConnection){
        this.terminalConnection = terminalConnection;
        Data = "";
    }

    public TerminalCommands connect() throws ConnectionException {
        this.terminalConnection.connect();
        return this;
    }
    public void disconnect() {
        this.terminalConnection.disconnect();
    }
    public TerminalCommands reset() {
        if (!this.terminalConnection.isConnected()) {
            return this;
        }
        return this;
    }
    public TerminalCommands sendCommand(String transactionData) throws ConnectionException {
        Log.i("Terminal Connection State",String.valueOf(terminalConnection.isConnected()));
        Log.i("TerminalCommand >>",transactionData);

        this.terminalConnection.write(transactionData.getBytes());

        this.terminalConnection.send();


        return this;
    }
    public TerminalCommands readCommand() throws ConnectionException {
        Data = this.terminalConnection.read();
        if(Data != null && !Data.equals(""))
            Log.i("TerminalCommand <<", Data);
        return this;
    }

    public boolean isConnected(){
        return terminalConnection.isConnected();
    }
}

