package com.smartfuel.service.diagnostics;

import com.smartfuel.service.network.UDPMessenger;

public class UDPDiagnostic extends BaseDiagnostic {

    private UDPMessenger udpMessenger;

    public UDPDiagnostic(UDPMessenger udpMessenger){
        this.udpMessenger = udpMessenger;
    }

    @Override
    protected int getDelay() {
        return 2000;
    }

    @Override
    protected boolean check() {
        return true; //don't need to check connection on UDP
    }

    @Override
    protected void reestablish() {
        //Nothing to do
    }
}
