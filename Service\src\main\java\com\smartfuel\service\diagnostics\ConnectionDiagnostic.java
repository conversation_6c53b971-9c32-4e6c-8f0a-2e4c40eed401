package com.smartfuel.service.diagnostics;

import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.transaction.CardTransactionService;

public class ConnectionDiagnostic extends BaseDiagnostic {

    private CardTransactionService service;

    public ConnectionDiagnostic(CardTransactionService service){
        this.service = service;
    }

    @Override
    protected int getDelay() {
        return 2000;
    }

    @Override
    protected boolean check() {
        return service.isConnected();
    }

    @Override
    protected void reestablish() {
        try {
            service.reconnect();
        } catch (ConnectionException e) {
            Log.w("ConnectionDiagnostic", e);
        }
    }
}
