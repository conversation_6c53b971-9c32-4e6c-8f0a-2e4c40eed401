package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.logger.Log;
import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public abstract class BaseResponse {
    protected String[] responseData;
    private int nodeNumber;
    private int requestNumber;
    private int sequenceNumber;
    private  int parameterCount;
    private String status;
    private int networkMessageType;

    public BaseResponse(String response, NetworkMessageType messageType)
    {
        this.responseData = response.split(" ");
        networkMessageType = messageType.getType();
    }

    public int getNodeNumber(){return this.nodeNumber; }
    public int getRequestNumber(){return this.requestNumber; }
    public int getSequenceNumber(){return this.sequenceNumber; }
    public int getParameterCount(){return this.parameterCount; }
    public String getStatus(){return this.status;}
    protected void read() throws RuntimeException
    {
        switch (networkMessageType)
        {
            case 0: //TCP
                this.nodeNumber = Integer.parseInt(this.responseData[0]);
                this.requestNumber = Integer.parseInt(this.responseData[1]);
                this.sequenceNumber = Integer.parseInt(this.responseData[2]);
                this.parameterCount = Integer.parseInt(this.responseData[3]);
                this.status = this.responseData[4];

                if (!this.status.equals("2000") && !(this.responseData[6].startsWith("Authorise_Dispenser_Ex") && this.status.equals("2026"))){
                    Log.e("cancelTransaction", "PCC Message Failed", new Throwable(this.status));
                    throw new RuntimeException("PCC Message Failed", new Throwable(this.status));
                }

                break;
            case 1: //UDP
                this.nodeNumber = Integer.parseInt(this.responseData[1]);
                this.requestNumber = Integer.parseInt(this.responseData[2]);
                this.sequenceNumber = Integer.parseInt(this.responseData[3]);
                this.parameterCount = Integer.parseInt(this.responseData[4]);
                this.status = this.responseData[5];

                if (!this.status.equals("1000")){
                    Log.e("PCC Message Failed", new Throwable(this.status));
                    throw new RuntimeException("PCC Message Failed", new Throwable(this.status));
                }
                break;
        }



    }
}
