package com.smartfuel.service.logger;

import android.content.Context;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class LogcatReader {

    private Context context;
    private static final String LOG_FILE_NAME = "log.txt";

    public LogcatReader(Context context) {
        this.context = context;
        try {
            File logFile = getLogFile();
            if(logFile.exists())
                logFile.delete();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public String readAppLogs() {
        /*StringBuilder log = new StringBuilder();
        try {
            Process process = Runtime.getRuntime().exec("logcat -d");

            BufferedReader bufferedReader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()));

            String line;
            while ((line = bufferedReader.readLine()) != null) {
                if (line.contains(context.getPackageName())) {
                    log.append(line).append("\n");
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return log.toString();*/
        try {
            File logFile = getLogFile();
            return readFromFile(logFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    public void add(Level level, String tag,
                      String message, Throwable throwable){
        Thread thread = new Thread(() -> {
            CustomLogcat c = new CustomLogcat(new Date(), tag, message, level, throwable);
            try {
                String log = c + "\n";
                writeLogToFile(log);
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
        thread.start();
    }

    private void writeLogToFile(String log) throws IOException {
        synchronized (this) {
            File logFile = getLogFile();

            FileOutputStream fileOutputStream = new FileOutputStream(logFile, true);
            OutputStreamWriter outputStreamWriter = new OutputStreamWriter(fileOutputStream);

            outputStreamWriter.append(log);
            outputStreamWriter.close();
        }
    }

    private String readFromFile(File file) throws IOException {
        synchronized (this) {
            StringBuilder stringBuilder = new StringBuilder();

            FileReader fileReader = new FileReader(file);
            BufferedReader bufferedReader = new BufferedReader(fileReader);

            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line).append("\n");
            }

            bufferedReader.close();
            return stringBuilder.toString();
        }
    }

    private File getLogFile() throws IOException {
        File databasePath = context.getDatabasePath("opt-db");
        String logFilePath = databasePath.getParent() + File.separator + LOG_FILE_NAME;
        return deleteOldLogs(logFilePath);
    }

    private File deleteOldLogs(String logFilePath) {
        File logFile = new File(logFilePath);
        String currentDate = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(new Date());

        if (logFile.exists()) {
            String logFileDate = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(logFile.lastModified());
            if (!currentDate.equals(logFileDate)) {
                logFile.delete();
            }
        }
        return new File(logFilePath);
    }

    public enum Level {
        INFO,
        ERROR,
        DEBUG,
        WARNING,
        VERBOSE
    }

    private static class CustomLogcat{
        private Date date;
        private String tag;
        private String message;
        private Level level;
        private Throwable throwable;

        private static final String pattern = "dd-MM-yyyy HH:mm:ss";
        private final SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);

        public CustomLogcat(Date date,
                            String tag,
                            String message,
                            Level level,
                            Throwable throwable){
            this.date = date;
            this.tag = tag;
            this.message = message;
            this.level = level;
            this.throwable = throwable;
        }

        @Override
        public String toString(){
            String ret = simpleDateFormat.format(date) + "\t" + tag + "\t";
            ret += "<span style=\"color: " + getColor(level) + ";\">";
            ret += getLevel(level) + ((message != null && !message.isEmpty()) ? "\t" + message : "") + (throwable != null ? "\t" + getStackTrace(throwable) : "");
            ret += "</span>";
            return ret;
        }

        private String getStackTrace(Throwable e){
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return sw.toString();
        }

        private String getLevel(Level level){
            switch (level){
                case INFO:
                    return "I";
                case DEBUG:
                    return "D";
                case ERROR:
                    return "E";
                case WARNING:
                    return "W";
                case VERBOSE:
                    return "V";
                default:
                    return "";
            }
        }

        private String getColor(Level level){
            switch (level){
                case INFO:
                    return "lightblue";
                case DEBUG:
                    return "green";
                case ERROR:
                    return "red";
                case WARNING:
                    return "yellow";
                case VERBOSE:
                    return "black";
                default:
                    return "";
            }
        }
    }
}
