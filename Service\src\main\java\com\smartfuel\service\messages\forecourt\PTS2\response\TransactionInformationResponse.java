package com.smartfuel.service.messages.forecourt.PTS2.response;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

public class TransactionInformationResponse extends BaseResponse{
    public ArrayList<Packet> Packets;
    public class Packet{
        private int Id;
        private String Type;
        private Data Data;

        public int getId() {
            return Id;
        }

        public void setId(int id) {
            Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            Type = type;
        }

        public Data getData() {
            return Data;
        }

        public void setData(Data data) {
            Data = data;
        }
    }
    public class Data{
        private int Pump;
        private int Transaction;
        private String State;
        private String DateTimeStart;
        private String DateTime;
        private int Nozzle;
        private float Volume;
        private float Amount;
        private float Price;
        private int FuelGradeId;
        private String FuelGradeName;

        public int getPump() {
            return Pump;
        }

        public void setPump(int pump) {
            Pump = pump;
        }

        public int getNozzle() {
            return Nozzle;
        }

        public void setNozzle(int nozzle) {
            Nozzle = nozzle;
        }

        public int getTransaction() {
            return Transaction;
        }

        public void setTransaction(int transaction) {
            Transaction = transaction;
        }

        public int getFuelGradeId() {
            return FuelGradeId;
        }

        public void setFuelGradeId(int fuelGradeId) {
            FuelGradeId = fuelGradeId;
        }

        public String getState() {
            return State;
        }

        public void setState(String state) {
            State = state;
        }

        public String getDateTimeStart() {
            return DateTimeStart;
        }

        public void setDateTimeStart(String dateTimeStart) throws ParseException {
            DateTimeStart = dateTimeStart;
        }

        public String getDateTime() {
            return DateTime;
        }

        public void setDateTime(String dateTime) throws ParseException {
            DateTime = dateTime;
        }

        public float getVolume() {
            return Volume*1000F;
        }

        public void setVolume(float volume) {
            Volume = volume;
        }

        public float getAmount() {
            return Amount*100F;
        }

        public void setAmount(float amount) {
            Amount = amount;
        }

        public float getPrice() {
            return Price*100F;
        }

        public void setPrice(float price) {
            Price = price;
        }

        public String getFuelGradeName() {
            return FuelGradeName;
        }

        public void setFuelGradeName(String fuelGradeName) {
            FuelGradeName = fuelGradeName;
        }
    }
}
