package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;
import com.smartfuel.service.messages.forecourt.POSTEC.enums.PumpStatus;

/*
/*
            0. IP Address:                 ***************
            1. Broadcast Node Number:      99
            2. Request Number:             000
            3. Sequence Number:            3336
            4. Parameter Count:            12
            5. Socket Status:              1000
            6. Dispenser Number:           01
            7. Hose Number:                0
            8. Grade Number:               12
            9. Pump Status:                02
            10. Sale Status:                048
            11. Dollar Sales:               00000000
            12. Memory:                     115
            13. Memory Dollars:             00000485
            14. Unfinalised Trn Count:      00
            15. 105555_1_Dispenser_status_update]
         */
public class DispenserStatusUpdate extends BaseResponse {
    private int dispenserNumber;
    private PumpStatus pumpStatus;
    private int hooseNumber;
    private int gradeId;
    private DispenserSaleStatus saleStatus;
    private long saleAmount;
    private DispenserSaleStatus memoryStatus;
    private long memoryAmount;


    public DispenserStatusUpdate(String response) throws RuntimeException {
        super(response, NetworkMessageType.UDP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[6]);
        this.hooseNumber = Integer.parseInt(super.responseData[7]);
        this.gradeId = Integer.parseInt(super.responseData[8]);
        this.pumpStatus = PumpStatus.getPumpStatusById(Integer.parseInt(super.responseData[9]));
        this.saleStatus = new DispenserSaleStatus(super.responseData[10].getBytes());
        this.saleAmount = Long.parseLong(super.responseData[11]);
        this.memoryStatus = new DispenserSaleStatus(super.responseData[12].getBytes());
        this.memoryAmount = Long.parseLong(super.responseData[13]);

    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }

    public int getHooseNumber() {
        return hooseNumber;
    }

    public int getGradeId() {
        return gradeId;
    }

    public PumpStatus getPumpStatus() {
        return pumpStatus;
    }

    public DispenserSaleStatus getSaleStatus() {
        return saleStatus;
    }

    public long getSaleAmount() {
        return saleAmount;
    }

    public DispenserSaleStatus getMemoryStatus() {
        return memoryStatus;
    }

    public long getMemoryAmount() {
        return memoryAmount;
    }
}
