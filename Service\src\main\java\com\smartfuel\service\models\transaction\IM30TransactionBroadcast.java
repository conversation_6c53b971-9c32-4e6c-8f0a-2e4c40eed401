package com.smartfuel.service.models.transaction;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;
//
// {"applicationId":"A0000000031010",
// "applicationLabel":"Visa Credit",
// "applicationTransactionCounter":389,
// "authorizationId":"303030303030",
// "cardExpiryDate":"",
// "cardSequenceNumber":"1",
// "cardSignature":"494052**********9173",
// "cardType":"VISA",
// "cardVerificationMethod":0,
// "completedUTCDateTime":"2023-05-15 04:27:32.131",
// "createdUTCDateTime":"2023-05-15 04:27:24.287",
// "externalReference":"5",
// "gatewayResponse":"Do Not Honour",
// "gatewayResponseCode":"05",
// "im30Reference":"8fc94afa-d2c8-4dcc-91b6-74882dfaf057"
@JsonIgnoreProperties(ignoreUnknown = true)
public class IM30TransactionBroadcast {
    private String applicationId;
    private String applicationLabel;
    private int applicationTransactionCounter;
    private String authorizationId;
    private String cardExpiryDate;
    private String cardSequenceNumber;
    private String cardSignature;
    private String cardType;
    private int cardVerificationMethod;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss.SSS")
    private Date completedUTCDateTime;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss.SSS")
    private Date createdUTCDateTime;
    private String externalReference;
    private String gatewayResponse;
    private String gatewayResponseCode;
    private String im30Reference;
    private int im30State;
    private int receiptState;
    private String im30TerminalId;
    private String merchantId;
    private String stan = "0";
    private long transactionAmount;
    private int transactionCurrency;
    private int transactionStatus;
    private int transactionType;
    private String errorMessage;
    //Default this property value to -1 : not all messages contain a state value.
    // Prevent default value of 0 which is a valid state when present.
    private int state = -1;
    private int authorizationType = 99;
    private int magneticState = -1;
    private String trackData2;

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationLabel() {
        return applicationLabel;
    }

    public void setApplicationLabel(String applicationLabel) {
        this.applicationLabel = applicationLabel;
    }

    public int getApplicationTransactionCounter() {
        return applicationTransactionCounter;
    }

    public void setApplicationTransactionCounter(int applicationTransactionCounter) {
        this.applicationTransactionCounter = applicationTransactionCounter;
    }

    public String getAuthorizationId() {
        return authorizationId;
    }

    public void setAuthorizationId(String authorizationId) {
        this.authorizationId = authorizationId;
    }

    public String getCardExpiryDate() {
        return cardExpiryDate;
    }

    public void setCardExpiryDate(String cardExpiryDate) {
        this.cardExpiryDate = cardExpiryDate;
    }

    public String getCardSequenceNumber() {
        return cardSequenceNumber;
    }

    public void setCardSequenceNumber(String cardSequenceNumber) {
        this.cardSequenceNumber = cardSequenceNumber;
    }

    public String getCardSignature() {
        return cardSignature;
    }

    public void setCardSignature(String cardSignature) {
        this.cardSignature = cardSignature;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public int getCardVerificationMethod() {
        return cardVerificationMethod;
    }

    public void setCardVerificationMethod(int cardVerificationMethod) {
        this.cardVerificationMethod = cardVerificationMethod;
    }

    public Date getCompletedUTCDateTime() {
        return completedUTCDateTime;
    }

    public void setCompletedUTCDateTime(Date completedUTCDateTime) {
        this.completedUTCDateTime = completedUTCDateTime;
    }

    public Date getCreatedUTCDateTime() {
        return createdUTCDateTime;
    }

    public void setCreatedUTCDateTime(Date createdUTCDateTime) {
        this.createdUTCDateTime = createdUTCDateTime;
    }

    public String getExternalReference() {
        return externalReference;
    }

    public void setExternalReference(String externalReference) {
        this.externalReference = externalReference;
    }

    public String getGatewayResponse() {
        return gatewayResponse;
    }

    public void setGatewayResponse(String gatewayResponse) {
        this.gatewayResponse = gatewayResponse;
    }

    public String getGatewayResponseCode() {
        return gatewayResponseCode;
    }

    public void setGatewayResponseCode(String gatewayResponseCode) {
        this.gatewayResponseCode = gatewayResponseCode;
    }

    public int getIm30State() {
        return im30State;
    }

    public void setIm30State(int im30State) {
        this.im30State = im30State;
    }

    public int getReceiptState() {
        return receiptState;
    }

    public void setReceiptState(int receiptState) {
        this.receiptState = receiptState;
    }

    public String getIm30TerminalId() {
        return im30TerminalId;
    }

    public void setIm30TerminalId(String im30TerminalId) {
        this.im30TerminalId = im30TerminalId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getStan() {
        return stan;
    }

    public void setStan(String stan) {
        this.stan = stan;
    }

    public long getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(long transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public int getTransactionCurrency() {
        return transactionCurrency;
    }

    public void setTransactionCurrency(int transactionCurrency) {
        this.transactionCurrency = transactionCurrency;
    }

    public int getTransactionStatus() {
        return transactionStatus;
    }

    public void setTransactionStatus(int transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public int getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(int transactionType) {
        this.transactionType = transactionType;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getIm30Reference() {
        return im30Reference;
    }

    public void setIm30Reference(String im30Reference) {
        this.im30Reference = im30Reference;
    }

    public int getAuthorizationType() {
        return authorizationType;
    }

    public void setAuthorizationType(int authorizationType) {
        this.authorizationType = authorizationType;
    }

    public int getMagneticState() {
        return magneticState;
    }

    public void setMagneticState(int magneticState) {
        this.magneticState = magneticState;
    }

    public String getTrackData2() {
        return trackData2;
    }

    public void setTrackData2(String trackData2) {
        this.trackData2 = trackData2;
    }
}

