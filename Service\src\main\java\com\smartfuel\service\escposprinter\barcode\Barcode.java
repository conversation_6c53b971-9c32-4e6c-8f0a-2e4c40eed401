package com.smartfuel.service.escposprinter.barcode;

import com.smartfuel.service.escposprinter.EscPosPrinterSize;
import com.smartfuel.service.escposprinter.exceptions.EscPosBarcodeException;

public abstract class Barcode {
    protected int barcodeType;

    protected String code;

    protected int colWidth;

    protected int height;

    protected int textPosition;

    Barcode(EscPosPrinterSize paramEscPosPrinterSize, int paramInt1, String paramString, float paramFloat1, float paramFloat2, int paramInt2) throws EscPosBarcodeException {
        this.barcodeType = paramInt1;
        this.code = paramString;
        this.height = paramEscPosPrinterSize.mmToPx(paramFloat2);
        this.textPosition = paramInt2;
        paramFloat2 = paramFloat1;
        if (paramFloat1 == 0.0F)
            paramFloat2 = paramEscPosPrinterSize.getPrinterWidthMM() * 0.7F;
        if (paramFloat2 > paramEscPosPrinterSize.getPrinterWidthMM()) {
            paramInt1 = paramEscPosPrinterSize.getPrinterWidthPx();
        } else {
            paramInt1 = paramEscPosPrinterSize.mmToPx(paramFloat2);
        }
        paramInt2 = (int)Math.round(paramInt1 / getColsCount());
        paramInt1 = paramInt2;
        if (getColsCount() * paramInt2 > paramEscPosPrinterSize.getPrinterWidthPx())
            paramInt1 = paramInt2 - 1;
        if (paramInt1 != 0) {
            this.colWidth = paramInt1;
            return;
        }
        throw new EscPosBarcodeException("Barcode is too long for the paper size.");
    }

    public int getBarcodeType() {
        return this.barcodeType;
    }

    public String getCode() {
        return this.code;
    }

    public abstract int getCodeLength();

    public int getColWidth() {
        return this.colWidth;
    }

    public abstract int getColsCount();

    public int getHeight() {
        return this.height;
    }

    public int getTextPosition() {
        return this.textPosition;
    }
}
