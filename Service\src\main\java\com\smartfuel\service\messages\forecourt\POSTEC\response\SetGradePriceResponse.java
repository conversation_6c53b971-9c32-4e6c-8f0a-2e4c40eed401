package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

import java.util.Calendar;
import java.util.Date;

/*
01 055 0123 12 2000 02 0 2001 08 24 22 30 1029 ? 1 Set_Grade_Prices
 01 = client node number
 055 = request number
 0123 = sequence number
 12 = number of parameters
 2000 = socket success code (2) + PCC success code (000)
 02 = Grade number
 0 = Set Cash prices
 2001 = Year
 08 = Month
 24 = Day
 22 = Hour
 30 = Minute
 1029 = Price of $1.029 per unit volume
 ? = Price change applies to all PCC operating schedules
 1 = Perform the price change without requiring console confirmation
 */
public class SetGradePriceResponse extends BaseResponse{
    private final int gradeNumber;
    private final int priceType;
    private final Date effectiveDate;
    private final long price;
    public SetGradePriceResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.gradeNumber = Integer.parseInt(super.responseData[5]);
        this.priceType = Integer.parseInt(super.responseData[6]);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR,Integer.parseInt(super.responseData[7]));
        calendar.set(Calendar.MONTH,Integer.parseInt(super.responseData[8]));
        calendar.set(Calendar.DAY_OF_MONTH,Integer.parseInt(super.responseData[9]));
        calendar.set(Calendar.HOUR_OF_DAY,Integer.parseInt(super.responseData[10]));
        calendar.set(Calendar.MINUTE,Integer.parseInt(super.responseData[11]));
        calendar.set(Calendar.SECOND,0);
        this.effectiveDate = calendar.getTime();
        this.price = Long.parseLong(super.responseData[12]);
    }

    public int getGradeNumber() {
        return gradeNumber;
    }

    public int getPriceType() {
        return priceType;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public long getPrice() {
        return price;
    }
}
