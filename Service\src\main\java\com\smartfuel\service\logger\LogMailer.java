package com.smartfuel.service.logger;

import android.content.Context;
import android.content.res.AssetManager;
import android.net.TrafficStats;

import com.smartfuel.service.repository.IConfigurationRepository;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;

import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

public class LogMailer {

    private Session session;
    private String username;
    private String kiosk;
    private String store;
    private String recipients;
    private String version;

    private Context context;

    private boolean isSetup = false;

    public Context getContext(){
        return context;
    }

    public LogMailer(String version){
        this.version = version;
    }

    public void setup(Context context, IConfigurationRepository configurationRepository){
        this.context = context;
        String host = configurationRepository.getConfigurationByName("smtp_host").getValue();
        String port = configurationRepository.getConfigurationByName("smtp_port").getValue();
        username = configurationRepository.getConfigurationByName("smtp_user").getValue();
        String password = configurationRepository.getConfigurationByName("smtp_password").getValue();
        recipients = configurationRepository.getConfigurationByName("smtp_recipients").getValue();
        kiosk = configurationRepository.getConfigurationByName("kiosk_name").getValue() + "(" + configurationRepository.getConfigurationByName("kiosk_id").getValue() + ")";
        store = configurationRepository.getConfigurationByName("store_name").getValue() + "(" + configurationRepository.getConfigurationByName("store_id").getValue() + ")";

        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.port", port);

        session = Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });

        isSetup = true;
    }

    private Message getMessage(String msg, Throwable e, LogcatReader logcatReader) throws MessagingException {
        String pattern = "dd-MM-yyyy HH:mm:ss";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        Date now = new Date();

        Message message = new MimeMessage(session);
        message.setFrom(new InternetAddress(username));
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipients));
        message.setSubject("Exception Occurred:" + simpleDateFormat.format(now) + "; " + msg);

        String text = loadHtmlFromAssets("reportEmail.html");
        text = text.replace("{{kiosk}}", kiosk);
        text = text.replace("{{version}}", version);
        text = text.replace("{{store}}", store);
        text = text.replace("{{date}}", simpleDateFormat.format(now));
        text = text.replace("{{exception}}", msg);
        text = text.replace("{{logcat}}", logcatReader.readAppLogs());

        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);

        text = text.replace("{{stackTrace}}", sw.toString());

        message.setContent(text, "text/html");
        return message;
    }

    private Message getDiagnosticMessage(String exceptions, String diagnostics) throws MessagingException {
        String pattern = "dd-MM-yyyy HH:mm:ss";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        Date now = new Date();

        Message message = new MimeMessage(session);
        message.setFrom(new InternetAddress(username));
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipients));
        message.setSubject("Diagnostic report:" + simpleDateFormat.format(now));

        String text = loadHtmlFromAssets("diagnosticEmail.html");
        text = text.replace("{{kiosk}}", kiosk);
        text = text.replace("{{version}}", version);
        text = text.replace("{{store}}", store);
        text = text.replace("{{date}}", simpleDateFormat.format(now));
        text = text.replace("{{exceptions}}", exceptions);
        text = text.replace("{{diagnostics}}", diagnostics);

        message.setContent(text, "text/html");
        return message;
    }

    public void sendEmail(String msg, Throwable e, LogcatReader logcatReader) {
        if(!isSetup)
            return;

        Thread thread = new Thread(() -> {
            synchronized (LogMailer.this) {
                try {
                    TrafficStats.setThreadStatsTag((int) Thread.currentThread().getId());
                    Transport.send(getMessage(msg, e, logcatReader));
                } catch (MessagingException ex) {
                    ex.printStackTrace();
                }
            }
        });
        thread.start();
    }

    public void sendDiagnosticEmail(String exceptions, String diagnostics) {
        if(!isSetup)
            return;

        Thread thread = new Thread(() -> {
            synchronized (LogMailer.this) {
                try {
                    TrafficStats.setThreadStatsTag((int) Thread.currentThread().getId());
                    Transport.send(getDiagnosticMessage(exceptions, diagnostics));
                } catch (MessagingException ex) {
                    ex.printStackTrace();
                }
            }
        });
        thread.start();
    }

    private String loadHtmlFromAssets(String filename) {
        StringBuilder stringBuilder = new StringBuilder();

        try {
            AssetManager assetManager = context.getAssets();
            InputStream inputStream = assetManager.open(filename);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }

            bufferedReader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return stringBuilder.toString();
    }

}
