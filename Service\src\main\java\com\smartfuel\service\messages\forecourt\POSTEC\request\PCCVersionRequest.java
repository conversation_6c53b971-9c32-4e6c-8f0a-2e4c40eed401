package com.smartfuel.service.messages.forecourt.POSTEC.request;

public class PCCVersionRe<PERSON> extends BaseRequest {

    public PCCVersionRequest(int nodeNumber, int dispenserNumber){
        super(nodeNumber,51,1,"Req_PCC_Version");
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
