package com.smartfuel.service.logic;

import android.content.Context;

import com.smartfuel.service.IKioskApiService;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.OPTService;
import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.diagnostics.DiagnosticListener;
import com.smartfuel.service.diagnostics.DiagnosticService;
import com.smartfuel.service.diagnostics.TCPDiagnostic;
import com.smartfuel.service.enums.transaction.TransactionStatus;
import com.smartfuel.service.enums.transaction.TransactionType;
import com.smartfuel.service.forecourtcontroller.ForecourtController;
import com.smartfuel.service.forecourtcontroller.ForecourtControllerFactory;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.GradePrice;
import com.smartfuel.service.models.forecourt.GradePrices;
import com.smartfuel.service.models.kiosk.request.TransactionAuditRequest;
import com.smartfuel.service.models.kiosk.response.AccountCardCapabilities;
import com.smartfuel.service.models.kiosk.response.AccountCardRestrictionResponse;
import com.smartfuel.service.models.posmaster.FuelPriceUpdate;
import com.smartfuel.service.models.transaction.IM30TransactionBroadcast;
import com.smartfuel.service.posmaster.IPOSMasterCallBack;
import com.smartfuel.service.posmaster.POSMaster;
import com.smartfuel.service.posmaster.POSMasterAPIResponse;
import com.smartfuel.service.repository.IConfigurationRepository;
import com.smartfuel.service.repository.IForecourtControllerTransactionRepository;
import com.smartfuel.service.repository.IRewardCardDiscountRepository;
import com.smartfuel.service.repository.ITransactionRepository;
import com.smartfuel.service.sqlite.models.Configuration;
import com.smartfuel.service.sqlite.models.ForecourtControllerTransaction;
import com.smartfuel.service.sqlite.models.FuelTransaction;
import com.smartfuel.service.sqlite.models.RewardCardDiscount;
import com.smartfuel.service.sqlite.models.Transaction;
import com.smartfuel.service.transaction.CardTransactionService;
import com.smartfuel.service.transaction.ITransactionCallbackManager;
import com.smartfuel.service.utils.ApiServiceHelper;
import com.smartfuel.service.utils.Utils;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ForecourtLogic<T> implements ForecourtController.Callback<T> {

    private IKioskApiService kioskApiService;
    private IForecourtControllerTransactionRepository pss5000TransactionRepository;
    private IConfigurationRepository configurationRepository;
    private ITransactionRepository transactionRepository;
    private IRewardCardDiscountRepository rewardCardDiscountRepository;

    private IPOSMasterCallBack POSMasterCallBackManager;
    private ITransactionCallbackManager TransactionCallbackManager;
    private IServiceEvents serviceEvents;
    private CardTransactionService cardTransactionService;

    public ForecourtController forecourtController;

    public Context context;
    protected int posId;
    protected String operationMode;
    protected String operationReference;
    protected int connectionAttempt = 0;
    protected Runnable afterConnection = null;
    protected boolean hasSetupCompleted = false;

    protected ScheduledExecutorService scheduledHeartbeatExecutorService = null;

    private static ForecourtLogic<?> instance = null;

    public static <T> ForecourtLogic<T> getInstance(IKioskApiService kioskApiService,
                                                    IForecourtControllerTransactionRepository pss5000TransactionRepository,
                                                    IConfigurationRepository configurationRepository,
                                                    ITransactionRepository transactionRepository,
                                                    IRewardCardDiscountRepository rewardCardDiscountRepository,
                                                    Context context, int posId, String type, int port, String ipAddress,
                                                    String operationMode, String posIpAddress) {
        if (instance == null) {
            instance = new ForecourtLogic<T>(posId, type, port, ipAddress, operationMode, posIpAddress);
        }

        instance.kioskApiService = kioskApiService;
        instance.pss5000TransactionRepository = pss5000TransactionRepository;
        instance.configurationRepository = configurationRepository;
        instance.transactionRepository = transactionRepository;
        instance.rewardCardDiscountRepository = rewardCardDiscountRepository;

        instance.context = context;
        instance.posId = posId;
        instance.operationMode = operationMode;
        instance.operationReference = posIpAddress;
        instance.forecourtController.setFuelGradeNames(getFuelGradeNames(configurationRepository));

        return (ForecourtLogic<T>) instance;
    }

    private ForecourtLogic(int posId, String type, int port, String ipAddress, String operationMode, String posIpAddress) {
        //forecourtController = ForecourtControllerFactory.getForecourtController(configurationRepository.getConfigurationByName("forecourt_controller_type").getValue(), Integer.parseInt(configurationRepository.getConfigurationByName("forecourt_controller_port").getValue()), configurationRepository.getConfigurationByName("forecourt_controller_ip").getValue(), this);

        forecourtController = ForecourtControllerFactory.getForecourtController(posId, type, port, ipAddress, operationMode, posIpAddress, this);
        DiagnosticService.setListener(new DiagnosticListener() {
            @Override
            public void onStopDetected(String tag) {
                if(tag.equals(TCPDiagnostic.class.getSimpleName())){
                    ArrayList<FuelPoint> list = getConfiguredFuelPoints();
                    if(list != null) {
                        boolean change = false;
                        for (FuelPoint currentFP : list) {
                            if(!currentFP.getState().equals("01H")) {
                                currentFP.setState("01H");
                                change = true;
                            }
                        }
                        if(serviceEvents != null && change){
                            serviceEvents.fuellingStatusUpdate();
                        }
                    }
                }
            }
            @Override public void onReestablishing(String tag, int attempt) { }
            @Override public void onRestored(String tag) {
                //if(tag.equals(TCPDiagnostic.class.getSimpleName())){
                //    LogonRequired();
                //}
            }
        });
    }

    public void setCardTransactionService(CardTransactionService cardTransactionService) {
        this.cardTransactionService = cardTransactionService;
    }

    public void registerClient(IServiceEvents activity) {
        this.serviceEvents = activity;
    }

    public void PreparePrepaidTransaction(String posId, String fuelpointId, String gradeId, String prepaidAmount) {
        forecourtController.PreparePrepaidTransaction(posId, fuelpointId, gradeId, prepaidAmount);
    }

    public String[] getValidGrades(int posId, int fp){
        Transaction  trx = transactionRepository.getCurrentTransaction(posId, TransactionStatus.CARD_AUTHORIZED.getState(), fp);
        if(trx != null) {
            if(trx.getType() == TransactionType.SALE_ACCOUNT.getType()) {
                try {
                    AccountCardRestrictionResponse restrictions = ApiServiceHelper.executeAPI(this.kioskApiService.getRestrictions(trx.getAuthorisationId(), trx.getStan(), trx.getTid()));
                    if (restrictions != null && restrictions.size() > 0) {
                        return restrictions.toStringArray();
                    }
                } catch (Exception e) {
                    //Log.e("getRestrictions", e);
                }
            }
        }
        return null;
    }

    public void Logon(String posId, String ApplicationId, String clientVersion) throws InterruptedException {
        forecourtController.Logon(posId, ApplicationId, clientVersion);
    }

    private long saveTransaction(Transaction trx, FuelTransaction fTrx){
        long trxId = transactionRepository.saveTransaction(trx, fTrx);
        if(trx.getId() <= 0)
            trx.setId(trxId);
        TransactionAuditRequest.send(kioskApiService, trx, fTrx, configurationRepository);
        return trxId;
    }

    @Override
    public <T> void ClearFuelTransactionBuffer(T response) {
        afterConnection = null;
        Thread worker = new Thread(() -> {
            String strTerminalId = configurationRepository.getConfigurationByName("pos_id").getValue();
            String kioskId = configurationRepository.getConfigurationByName("kiosk_id").getValue();

            int terminalId = Integer.parseInt(strTerminalId);
            int trnStatus = TransactionStatus.PUMP_FINISHED.getState();
            com.smartfuel.service.models.forecourt.FuelTransaction ft = forecourtController.getFuelTransactionDetails(response);
            if (ft != null) {
                Transaction trx = transactionRepository.getCurrentTransaction(terminalId, trnStatus, ft.getFuelPointId());
                if (trx != null) {
                    FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                    // TODO: Add save transaction with status CARD_IN_PROGRESS
                    trx.setStatus(TransactionStatus.CARD_IN_PROGRESS.getState());
                    transactionRepository.saveTransaction(trx, fTrx);
                    if (trx.getProcessor().equals("WC")) {
                        trx.setResponseCode("0");
                        trx.setHostResponse("APPROVED");
                        trx.setAuthorisationId(String.valueOf(trx.getId()));
                        trx.setPanSeqNumber(ft.getSeqNumber());
                        trx.setAccountType("WhiteCard");
                        trx.setAid("WHITECARD");
                        fTrx.setPrice(ft.getPrice());
                        trx.setStatus(TransactionStatus.CARD_CAPTURED.getState());
                    } else if (trx.getProcessor().equals("IX")) {
                        long finalDiscount = configureReward(trx.getId(), fTrx.getGradeId(), fTrx.getVolume());
                        trx.setFinalAmount(ft.getMoneyDue() - finalDiscount);
                        fTrx.setMoneyDue(ft.getMoneyDue());
                        if (ft.getVolume() != 0)
                            fTrx.setPrice(ft.getPrice());
                        try {
                            if (trx.getFinalAmount() != 0)
                                cardTransactionService.Capture(kioskId, trx);
                            else
                                cardTransactionService.Reversal(kioskId, trx);
                        } catch (Exception e) {
                            Log.e("ClearFuelTransactionBuffer", e);
                        }
                    }
                    saveTransaction(trx, fTrx);
                }

                forecourtController.CompleteFuelTransaction(strTerminalId, String.valueOf(ft.getFuelPointId()), ft.getSeqNumber(), ft.getVolume(), ft.getMoneyDue());

                pss5000TransactionRepository.clearForecourtTransaction(terminalId, ft.getFuelPointId(), ft.getFuelGradeId());
            }

        });
        worker.start();
    }

    @Override
    public <T> void FuelTransactionComplete(T response) {
        //update the transaction status = 4 and release the fuel point
        afterConnection = null;
        Thread worker = new Thread(() -> {

            String strTerminalId = configurationRepository.getConfigurationByName("pos_id").getValue();
            int terminalId = Integer.parseInt(strTerminalId);
            int trnStatus = TransactionStatus.PUMP_AUTHORIZED.getState();

            com.smartfuel.service.models.forecourt.FuelTransaction ft = forecourtController.getFuelTransactionDetails(response);
            if (ft != null) {
                // get the current transaction for this fuel point and terminal with a transaction  status = 3
                Transaction trx = transactionRepository.getCurrentTransaction(terminalId, trnStatus, ft.getFuelPointId());
                if (trx != null) {
                    FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());

                    trx.setStatus(TransactionStatus.PUMP_FINISHED.getState());
                    //trx.setFinalAmount(trx.getAuthorisedAmount() - ft.getMoneyDue());

                    if (ft.getVolume() != 0) {
                        fTrx.setGradeId(String.format("%1$" + 2 + "s", ft.getFuelGradeId()).replace(' ', '0'));
                        fTrx.setVolume(ft.getVolume());
                        configureReward(trx.getId(), fTrx.getGradeId(), fTrx.getVolume());
                    }
                    //fTrx.setMoneyDue(ft.getMoneyDue());
                    fTrx.setTransSeqNumber(ft.getSeqNumber());
                    fTrx.setTransInfoMask(ft.getInfoMask());
                    saveTransaction(trx, fTrx);
                    forecourtController.FetchFuelTransaction(strTerminalId, ft.getFuelPointId(), ft.getSeqNumber());
                }


            }
        });
        worker.start();
    }

    private long configureReward(long transactionId, String fuelGrade, long volume){
        long finalReward = 0;
        try {
            List<RewardCardDiscount> discounts = rewardCardDiscountRepository.GetByTransaction(transactionId);
            if (discounts != null && discounts.size() > 0) {
                for (RewardCardDiscount discount : discounts) {
                    if(discount.getFuelGrade().equals(fuelGrade)){
                        if(discount.getTotalDiscountApplied() == 0) {
                            long volumeDiscount = volume;
                            if(discount.getCap() != null && discount.getCap() > 0 && discount.getCap()*100 < volumeDiscount){
                                volumeDiscount = discount.getCap()*100;
                            }
                            discount.setTotalDiscountApplied((discount.getDiscount() * volumeDiscount)/100);
                            rewardCardDiscountRepository.update(discount);
                        }
                        finalReward = discount.getTotalDiscountApplied();
                    }
                    else{
                        rewardCardDiscountRepository.delete(discount);
                    }
                }
            }
        }catch (Exception e){
            Log.e("configure Reward", "Failed to apply reward", e);
        }

        return finalReward;
    }

    @Override
    public void Error(Throwable throwable) {
        Log.e("OPTService", "onError", throwable);
        if(!DiagnosticService.checkResolve(throwable))
            this.serviceEvents.systemError("Pump Communication Error\nAny pending transactions will be reversed within the hour.", throwable);
    }

    @Override
    public void FuelPointAuthorized(String fuelpointId) throws InterruptedException {
        //get the current transaction  - we know the fuel point
        //int terminalId, int transactionStatus, int pumpNumber
        afterConnection = null;
        Thread worker = new Thread(() -> {
            int terminalId = Integer.parseInt(configurationRepository.getConfigurationByName("pos_id").getValue());
            int transactionStatus = TransactionStatus.CARD_AUTHORIZED.getState();
            int pumpNumber = Integer.parseInt(fuelpointId);
            Transaction currentTrx = transactionRepository.getCurrentTransaction(terminalId, transactionStatus, pumpNumber);
            if (currentTrx != null) {
                FuelTransaction fTRx = transactionRepository.getFuelTransactionById(currentTrx.getFuelTransactionId());
                currentTrx.setStatus(TransactionStatus.PUMP_AUTHORIZED.getState());
                saveTransaction(currentTrx, fTRx);
                serviceEvents.pumpAuthorised(currentTrx.getId(), pumpNumber, getAuthorizedGrades(currentTrx.getAuthorisationId(), currentTrx.getStan(), currentTrx.getTid(), currentTrx.getType()));
            }
        });
        worker.start();
        worker.join();
    }

    private String[] getAuthorizedGrades(String authorizationId, int stan, String tid, int type){
        if(type == TransactionType.SALE_ACCOUNT.getType()) {
            try {
                AccountCardRestrictionResponse restrictions = ApiServiceHelper.executeAPI(this.kioskApiService.getRestrictions(authorizationId, stan, tid));
                if (restrictions != null && restrictions.size() > 0) {
                    return restrictions.toStringArray();
                }
            } catch (Exception e) {
                //Log.e("getRestrictions", e);
            }
        }
        return null;
    }

    @Override
    public <T> void FuelPointPrePaidSetupComplete(T response) throws InterruptedException {
        afterConnection = null;
        Thread worker = new Thread(() -> {
            // authorise the fuel point now
            String posId = configurationRepository.getConfigurationByName("pos_id").getValue();
            forecourtController.AuthoriseFuellingPoint(posId, response);
        });
        worker.start();
        worker.join();
    }

    @Override
    public void ForecourtControllerLoginComplete() {
        connectionAttempt = 0;

        if (scheduledHeartbeatExecutorService != null && !(scheduledHeartbeatExecutorService.isShutdown() || scheduledHeartbeatExecutorService.isTerminated()))
            scheduledHeartbeatExecutorService.shutdown();

        scheduledHeartbeatExecutorService = Executors.newSingleThreadScheduledExecutor();
        scheduledHeartbeatExecutorService.scheduleAtFixedRate(() -> {
            try {
                forecourtController.Heartbeat();
                Log.i("forecourtController", "Heartbeat");
                checkSchedulerMode();
            } catch (Exception e) {
                Log.e("OPTService", "Heartbeat Failed", e);
                serviceEvents.systemError("Out of Service", e);
            }
        }, 8, 8, TimeUnit.SECONDS);

        if (afterConnection != null) {
            afterConnection.run();
            afterConnection = null;
        }
    }

    private void checkSchedulerMode(){
        try{
            if(operationMode.equals("scheduler") && operationReference != null && !operationReference.isEmpty()){
                String TIME_PATTERN = "^([01]\\d|2[0-3]):([0-5]\\d)-([01]\\d|2[0-3]):([0-5]\\d)$";
                SimpleDateFormat TIME_FORMATTER = new SimpleDateFormat("HH:mm");

                Pattern pattern = Pattern.compile(TIME_PATTERN);
                Matcher matcher = pattern.matcher(operationReference);

                if (!matcher.matches()) {
                    return;
                }

                String[] times = operationReference.split("-");

                Date startDate = isValidTime(TIME_FORMATTER, times[0]);
                Date endDate = isValidTime(TIME_FORMATTER, times[1]);
                if(startDate != null && endDate != null){
                    Calendar currentCalendar = Calendar.getInstance();
                    int currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY);
                    int currentMinute = currentCalendar.get(Calendar.MINUTE);

                    Date currentTime = TIME_FORMATTER.parse(String.format("%02d:%02d", currentHour, currentMinute));

                    Calendar startCal = Calendar.getInstance();
                    startCal.setTime(startDate);

                    Calendar endCal = Calendar.getInstance();
                    endCal.setTime(endDate);

                    Calendar currentCal = Calendar.getInstance();
                    currentCal.setTime(currentTime);

                    if (startDate.after(endDate) || startDate.equals(endDate)) {
                        endCal.add(Calendar.DAY_OF_MONTH, 1);
                    }

                    if (currentCal.before(startCal)) {
                        currentCal.add(Calendar.DAY_OF_MONTH, 1);
                    }

                    if ((currentCal.after(startCal) || currentCal.equals(startCal)) && (currentCal.before(endCal) || currentCal.equals(endCal))){
                        ShowHomeScreen();
                        return;
                    }

                    ShowStandbyScreen();
                }
            }
        }catch (Exception e){
            Log.e("checkSchedulerMode", e);
        }
    }

    private static Date isValidTime(SimpleDateFormat TIME_FORMATTER, String time) {
        try {
            return TIME_FORMATTER.parse(time);
        } catch (ParseException e) {
            return null;
        }
    }

    @Override
    public void LogonRequired() {
        connectionAttempt++;
        serviceEvents.controllerConnectionAttempt(connectionAttempt);
        ((OPTService) context).LogonForecourtController();
    }

    @Override
    public void ShowHomeScreen() {
        serviceEvents.showHomeScreen();
    }

    @Override
    public void ShowStandbyScreen() {
        serviceEvents.showStandbyScreen();
    }

    @Override
    public void SetupComplete() {
        if (hasSetupCompleted)
            return;

        hasSetupCompleted = true;

        Log.i("onSetupComplete", "executing");

        ScheduledExecutorService scheduledKioskExecutorService = null;
        ScheduledExecutorService scheduledPOSMasterFuelPriceExecutorService = null;
        ScheduledExecutorService scheduledCardTerminalExecutorService = null;
        ScheduledExecutorService scheduledOrphanTransactionsExecutorService = null;
        ScheduledExecutorService scheduledUpdateDataBaseExecutorService = null;

        try {
            scheduledKioskExecutorService = Executors.newSingleThreadScheduledExecutor();
            scheduledPOSMasterFuelPriceExecutorService = Executors.newSingleThreadScheduledExecutor();
            scheduledCardTerminalExecutorService = Executors.newSingleThreadScheduledExecutor();
            scheduledOrphanTransactionsExecutorService = Executors.newSingleThreadScheduledExecutor();
            scheduledUpdateDataBaseExecutorService = Executors.newSingleThreadScheduledExecutor();

            scheduledPOSMasterFuelPriceExecutorService.scheduleAtFixedRate(new Runnable() {
                @Override
                public void run() {

                    POSMaster p = null;
                    try {
                        p = new POSMaster
                                (
                                        configurationRepository.getConfigurationByName("posmaster_host").getValue(),
                                        configurationRepository.getConfigurationByName("store_id").getValue(),
                                        configurationRepository.getConfigurationByName("posmaster_username").getValue(),
                                        configurationRepository.getConfigurationByName("posmaster_password").getValue(),
                                        configurationRepository.getConfigurationByName("ftp_host").getValue(),
                                        configurationRepository.getConfigurationByName("ftp_username").getValue(),
                                        configurationRepository.getConfigurationByName("ftp_password").getValue()
                                );
                    } catch (MalformedURLException e) {
                        Log.e("OPTService", e);
                        serviceEvents.systemError("OPTService", e);
                    }
                    p.HeartBeat(context, POSMasterCallBackManager);
                }
            }, 1, 5, TimeUnit.MINUTES);

            scheduledKioskExecutorService.scheduleAtFixedRate(() -> {
                try {
                    ((OPTService) context).publishKioskTransactions(); // send transaction data to Kiosk API
                    ((OPTService) context).exportKioskTransactions(); // upload transaction data to POSMaster
                } catch (Exception e) {
                    Log.e("OPTService", "Kiosk Upload Failed", e);
                }
            }, 30, 30, TimeUnit.SECONDS);

            scheduledOrphanTransactionsExecutorService.scheduleAtFixedRate(() -> {
                try {
                    ((OPTService) context).checkOrphanTransactions(); // check for orphan transactions and process
                } catch (Exception e) {
                    Log.e("OPTService", "Orphan Transactions Failed", e);
                }
            }, 1, 5, TimeUnit.MINUTES);

            scheduledUpdateDataBaseExecutorService.scheduleAtFixedRate(() -> {
                try {
                    ((OPTService) context).uploadKioskDatabase(); // create and upload internal database
                } catch (Exception e) {
                    Log.e("OPTService", "Upload Kiosk Database Failed", e);
                }
            }, 0, 1, TimeUnit.DAYS);

            scheduledCardTerminalExecutorService.scheduleAtFixedRate(() -> {
                //TODO: Remove comment when IM30 Device available
                //Log.i("CardTerminal Reading", "Reading messages from card terminal.");
                try {
                    cardTransactionService.Monitor(TransactionCallbackManager);
                } catch (ConnectionException e) {
                    Log.e("CardMessageMonitor", "Failed", e);
                    //serviceEvents.systemError("Card Terminal Failed", e);
                }

            }, 500, 500, TimeUnit.MILLISECONDS);
            TransactionCallbackManager = new ITransactionCallbackManager() {
                @Override
                public void CardTransactionUpdate(IM30TransactionBroadcast broadcast) {
                    Log.i("TransactionCallbackManager", "CardTransactionUpdate:Entry");
                    try {
                        Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                        FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                        trx.importIm30Transaction(broadcast);
                        saveTransaction(trx, fTrx);
                    } catch (Exception e) {
                        Log.e("TransactionCallbackManager", "CardTransactionUpdate", e);
                        serviceEvents.systemError("Card Terminal Failed", e);
                    }
                    Log.i("TransactionCallbackManager", "CardTransactionUpdate:Exit");
                }

                @Override
                public void CardTransactionReadComplete(IM30TransactionBroadcast broadcast) {
                    try {
                        Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                        FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                        trx.setStatus(TransactionStatus.CARD_AUTHORIZING.getState()); // Authorising
                        saveTransaction(trx, fTrx);
                        serviceEvents.cardTransactionInProgress(String.valueOf(fTrx.getPumpNumber()), trx.getAuthorisedAmount()); //move the UI to the transaction processing screen
                    } catch (Exception e) {
                        Log.e("TransactionCallbackManager", "CardTransactionReadComplete", e);
                        serviceEvents.systemError("Card Terminal Failed", e);
                    }
                }

                @Override
                public void CardTransactionTerminalFailed(IM30TransactionBroadcast broadcast) {
                    try {
                        Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                        FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                        trx.importIm30Transaction(broadcast);
                        trx.setStatus(TransactionStatus.TRX_ERROR.getState()); // TRX ERROR
                        if(trx.getHostResponse() == null || trx.getHostResponse().isEmpty()){
                            trx.setHostResponse(broadcast.getIm30State() == 2 ? "Card Read Failed" : (broadcast.getIm30State() == 5 ? "Sending Online Failed" : "Pre-Authorization Failed"));
                            trx.setResponseCode("96");
                        }
                        saveTransaction(trx, fTrx);
                        //serviceEvents.systemError("Card Terminal Failed", new RuntimeException("Card Terminal Failure"));
                        serviceEvents.cardTransactionDeclined(broadcast.getGatewayResponse(), broadcast.getCardSignature());
                    } catch (Exception e) {
                        Log.e("TransactionCallbackManager", "CardTransactionTerminalFailed", e);
                        serviceEvents.systemError("Card Terminal Failed", e);
                    }
                }

                @Override
                public void CardTransactionAuthorised(IM30TransactionBroadcast broadcast) {
                    try {
                        Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                        FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                        trx.importIm30Transaction(broadcast);
                        trx.setStatus(TransactionStatus.CARD_AUTHORIZED.getState()); // AUTHORISED
                        saveTransaction(trx, fTrx);
                        if(broadcast.getAuthorizationType() == 1) {//White Card
                            AccountCardCapabilities capabilities = ApiServiceHelper.executeAPI(kioskApiService.getCapabilities(trx.getAuthorisationId(), trx.getStan(), trx.getTid()));
                            if(capabilities != null){
                                if(capabilities.hasPrompt())
                                    serviceEvents.getWhiteCardUserInformation(Long.parseLong(broadcast.getExternalReference()), broadcast.getIm30Reference(), capabilities);
                                else{
                                    afterConnection = () -> {
                                        try {
                                            PreparePrepaidTransaction(String.valueOf(posId), String.valueOf(fTrx.getPumpNumber()), "0", String.valueOf(trx.getAuthorisedAmount()));
                                        }catch (Exception e){
                                            Log.e("CardTransactionAuthorised", e);
                                        }
                                    };
                                    afterConnection.run();
                                }
                            } else {
                                throw new Exception("Error to get account card capabilities.");
                            }
                        }
                        else { //Bankcard
                            //check if RewardCard is enabled on this terminal, if so, ask to user before pump
                            Configuration rewardCardStatus = configurationRepository.getConfigurationByName("rewardcard_status");
                            if(rewardCardStatus != null && rewardCardStatus.getValue().equals("enabled")){
                                serviceEvents.getRewardCardUserInformation(Long.parseLong(broadcast.getExternalReference()));
                                return;
                            }

                            afterConnection = () -> {
                                try {
                                    PreparePrepaidTransaction(String.valueOf(posId), String.valueOf(fTrx.getPumpNumber()), "0", String.valueOf(trx.getAuthorisedAmount()));
                                }catch (Exception e){
                                    Log.e("CardTransactionAuthorised", e);
                                }
                            };
                            afterConnection.run();
                            //forecourtController.PreparePrepaidTransaction(String.valueOf(posId), String.valueOf(fTrx.getPumpNumber()), "0", String.valueOf(trx.getAuthorisedAmount()));
                        }
                    } catch (Exception e) {
                        Log.e("TransactionCallbackManager", "CardTransactionTerminalFailed", e);
                        serviceEvents.systemError("Card Terminal Failed", e);
                    }
                }

                @Override
                public void CardTransactionDeclined(IM30TransactionBroadcast broadcast) {
                    try {
                        Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                        FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                        trx.importIm30Transaction(broadcast);
                        trx.setStatus(TransactionStatus.CARD_AUTH_REJECTED.getState()); // AUTH REJECTED
                        saveTransaction(trx, fTrx);
                        serviceEvents.cardTransactionDeclined(broadcast.getGatewayResponse(), broadcast.getCardSignature());
                    } catch (Exception e) {
                        Log.e("TransactionCallbackManager", "CardTransactionDeclined", e);
                        serviceEvents.systemError("Card Terminal Failed", e);
                    }
                }

                @Override
                public void CardTransactionCaptureComplete(IM30TransactionBroadcast broadcast) {
                    try {
                        Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                        FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                        trx.importIm30Transaction(broadcast);
                        trx.setStatus(TransactionStatus.CARD_CAPTURED.getState());
                        saveTransaction(trx, fTrx);
                        if (serviceEvents != null)
                            serviceEvents.paymentComplete(trx);
                    } catch (Exception e) {
                        Log.e("TransactionCallbackManager", "CardTransactionCaptureComplete", e);
                    }
                }

                @Override
                public void CardTransactionReversalComplete(IM30TransactionBroadcast broadcast) {
                    try {
                        Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                        FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                        trx.importIm30Transaction(broadcast);
                        trx.setStatus(TransactionStatus.CARD_REVERSED.getState());
                        saveTransaction(trx, fTrx);
                        //serviceEvents.paymentComplete(trx);
                    } catch (Exception e) {
                        Log.e("TransactionCallbackManager", "CardTransactionReversalComplete", e);
                    }
                }

                @Override
                public void CardTransactionTimeout(IM30TransactionBroadcast broadcast) {
                    Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                    FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                    trx.importIm30Transaction(broadcast);
                    trx.setStatus(TransactionStatus.TRX_ERROR.getState());
                    if(trx.getHostResponse() == null || trx.getHostResponse().isEmpty()){
                        trx.setHostResponse("Card Timed Out");
                        trx.setResponseCode("91");
                    }
                    saveTransaction(trx, fTrx);
                    serviceEvents.serviceReady();
                }

                @Override
                public void CardTransactionCancelled(IM30TransactionBroadcast broadcast) {
                    Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                    FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                    trx.importIm30Transaction(broadcast);
                    trx.setStatus(TransactionStatus.USER_CANCELLED.getState());
                    saveTransaction(trx, fTrx);
                }

                @Override
                public void CardTransactionCaptureFailed(IM30TransactionBroadcast broadcast) {
                    try {
                        //get the transaction record
                        Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                        FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                        String kioskId = configurationRepository.getConfigurationByName("kiosk_id").getValue();
                        int trxCount = trx.getTrxCount();
                        if (trxCount < 3) //check trxCount < 3
                        {
                            //re-try the card capture
                            cardTransactionService.Capture(kioskId, trx);
                            trx.setTrxCount(trx.getTrxCount() + 1);
                            saveTransaction(trx, fTrx);
                        } else {
                            trx.setStatus(TransactionStatus.CARD_CAPTURE_REJECTED.getState());
                            saveTransaction(trx, fTrx);
                        }
                    } catch (Exception e) {

                    }

                }

                @Override
                public void CardTransactionReversalFailed(IM30TransactionBroadcast broadcast) {
                    try {
                        //get the transaction record
                        Transaction trx = transactionRepository.getTransactionById(Long.parseLong(broadcast.getExternalReference()));
                        FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());
                        String kioskId = configurationRepository.getConfigurationByName("kiosk_id").getValue();
                        int trxCount = trx.getTrxCount();
                        if (trxCount < 3) //check trxCount < 3
                        {
                            //re-try the card capture
                            cardTransactionService.Reversal(kioskId, trx);
                            trx.setTrxCount(trx.getTrxCount() + 1);
                            saveTransaction(trx, fTrx);
                        } else {
                            trx.setStatus(TransactionStatus.CARD_REVERSAL_REJECTED.getState());
                            saveTransaction(trx, fTrx);
                        }
                    } catch (Exception e) {

                    }
                }

                @Override
                public void CardReceiptReadComplete(IM30TransactionBroadcast broadcast) {
                    try {
                        List<Integer> trx = transactionRepository.getCardReceiptTransactions(broadcast.getCardSignature());
                        if (trx != null && trx.size() > 0)
                            serviceEvents.cardReceiptComplete(trx); //move the UI to the receipt processing screen
                        else {
                            serviceEvents.serviceReady();
                            serviceEvents.cardReceiptNotFound();
                            //serviceEvents.systemError("No Receipts for this Card", new Exception("no receipt found for this card"));
                        }

                    } catch (Exception e) {
                        Log.e("TransactionCallbackManager", "CardReceiptReadComplete", e);
                        serviceEvents.systemError("Card Terminal Failed", e);
                    }
                }

                @Override
                public void CardReceiptTerminalFailed(IM30TransactionBroadcast broadcast) {
                    Log.i("TransactionCallbackManager", "CardReceiptTerminalFailed (Card Read Failed)");
                    serviceEvents.systemError("Card Terminal Failed", new Exception("Card Read Failed"));
                    serviceEvents.cardReceiptDeclined();
                }

                @Override
                public void CardReceiptCancelled(IM30TransactionBroadcast broadcast) {
                    serviceEvents.serviceReady();
                    serviceEvents.cardReceiptCancelled();
                }

                @Override
                public void CardReceiptTimeout(IM30TransactionBroadcast broadcast) {
                    serviceEvents.serviceReady();
                    serviceEvents.cardReceiptTimeOut();
                }

                @Override
                public void CardMagneticReadComplete(IM30TransactionBroadcast broadcast) {
                    try {
                        serviceEvents.cardMagneticComplete(broadcast.getTrackData2());
                    } catch (Exception e) {
                        Log.e("TransactionCallbackManager", "CardMagneticReadComplete", e);
                        serviceEvents.systemError("Card Terminal Failed", e);
                    }
                }

                @Override
                public void CardMagneticTerminalFailed(IM30TransactionBroadcast broadcast) {
                    Log.e("TransactionCallbackManager", "CardMagneticTerminalFailed", new Exception("CardMagneticTerminalFailed"));
                    serviceEvents.cardMagneticDeclined("Card Reader Failed");
                }

                @Override
                public void CardMagneticCancelled(IM30TransactionBroadcast broadcast) {
                    serviceEvents.cardMagneticDeclined("Card Reader Cancelled");
                }

                @Override
                public void CardMagneticTimeout(IM30TransactionBroadcast broadcast) {
                    serviceEvents.cardMagneticDeclined("Card Reader Timeout");
                }
            };
            POSMasterCallBackManager = result -> {
                if (result instanceof POSMasterAPIResponse.Success) {

                    switch (Utils.getTypeName(((POSMasterAPIResponse.Success) result).data.getClass())) {
                        case "com.smartfuel.service.models.posmaster.FuelPriceUpdate":
                            //here we have details for the fuel price changes to be applied - received from POSMaster
                            // need to apply the changes to the forecourt controller and have the display board data updated
                            for (String name : ((POSMasterAPIResponse.Success<FuelPriceUpdate>) result).data.filePaths) {
                                applyFuelPriceUpdate(name);
                                Log.i("POSMaster OnComplete", String.valueOf(((POSMasterAPIResponse.Success<FuelPriceUpdate>) result).data.filePaths.get(0)));
                            }
                            break;
                        default:
                            Log.i("POSMasterCallBackManager", Utils.getTypeName(((POSMasterAPIResponse.Success) result).data.getClass()));
                            break;
                    }
                } else {
                    // Show error in UI
                }
            };


        } catch (Exception e) {
            Log.e("OPTService", "onSetupComplete Failed", e);
            //decide how to manage this - needs a new logon request to controller?
        }
        try {
            // populateFuelGradeNames();
            this.serviceEvents.serviceReady();
        } catch (Exception e) {
            Log.e("SetupFailed", e);
            serviceEvents.systemError("SetupFailed", e);
        }
    }

    @Override
    public void ForecourtControllerReady() {
        populateFuelGradeNames();
        serviceEvents.gradePriceUpdate();
    }

    public void ProceedPrepaidTransaction(long transactionId) throws InterruptedException {
        Thread workerThread = new Thread(() -> {
            try {
                Transaction trx = transactionRepository.getTransactionById(transactionId);
                FuelTransaction fTrx = transactionRepository.getFuelTransactionById(trx.getFuelTransactionId());

                if(trx.getStatus() == TransactionStatus.CARD_AUTHORIZED.getState()) { // AUTHORISED
                    afterConnection = () -> {
                        try {
                            PreparePrepaidTransaction(String.valueOf(posId), String.valueOf(fTrx.getPumpNumber()), "0", String.valueOf(trx.getAuthorisedAmount()));
                        }catch (Exception e){
                            Log.e("ProceedPrepaidTransaction", e);
                        }
                    };
                    afterConnection.run();
                }

            } catch (Exception e) {
                Log.e("TransactionCallbackManager", "CardTransactionTerminalFailed", e);
                serviceEvents.systemError("Card Terminal Failed", e);
            }
        });
        workerThread.start();
        workerThread.join();
    }

    public <T> void FuellingStatusUpdate(T response) throws InterruptedException {
        Thread worker = new Thread(() -> {
            ForecourtControllerTransaction transactionData = forecourtController.getTransactionData(response);

            boolean changeNozzle = true;
            //look in the local datastore for a current transaction which is being controlled by this service - in the last 1 mins
            Transaction currentTransaction = transactionRepository.getTransactionByFuelPointStatusAndTimeout(transactionData.getFpId());
            if(currentTransaction!=null){
                changeNozzle = false;
            }

            Log.i("FuelStatusUpdateDebug", String.format("%1$" + 2 + "s", transactionData.getFpId()).replace(' ', '0'));
            if (forecourtController.getConfiguredFuelPoints() != null) {
                //locate the fuelpoint relating to this transaction and update it's status
                //FuelPoint currentFP = forecourtController.getConfiguredFuelPoints().stream().filter(fuelPoint -> fuelPoint.getId().equals(String.format("%02d", transactionData.getFpId()))).findFirst().orElse(null);
                for (FuelPoint currentFP : forecourtController.getConfiguredFuelPoints()) {

                    if (currentFP.getId().equals(String.format("%02d", transactionData.getFpId()))
                            && ((currentFP.getState() != null && !currentFP.getState().equals(transactionData.getFpMainState()))
                                || (currentFP.isNozzleLifted() != transactionData.getFpSubState2().equals("NozzleLifted") && operationMode.equals("single")))) {
                        currentFP.setState(transactionData.getFpMainState());
                        currentFP.setNozzleLifted(transactionData.getFpSubState2().equals("NozzleLifted") && operationMode.equals("single") && changeNozzle);
                        serviceEvents.fuellingStatusUpdate();
                    }
                }

                if (transactionData.getFpId() != 0 && !transactionData.getFcGradeId().equals("00")) {
                    if (transactionData.getFpLockId() != 0)
                        pss5000TransactionRepository.saveForecourtTransaction(transactionData);
                }
                forecourtController.GetFuelPriceData();
                //saveFuelControllerStatus(response.data);
            }
            //}

        });
        worker.start();
        worker.join();

    }

    private void populateFuelGradeNames() {

        Thread thread = new Thread(() -> {

            for (GradePrice gradePrice : forecourtController.getConfiguredGradePrices().getGradePrices())
                gradePrice.setName(configurationRepository.getConfigurationByName(gradePrice.getLabel()).getValue());
            for (FuelPoint fuelPoint : forecourtController.getConfiguredFuelPoints()) {
                if (fuelPoint.getGradePrices() != null)
                    for (GradePrice gradePrice : fuelPoint.getGradePrices())
                        gradePrice.setName(configurationRepository.getConfigurationByName(gradePrice.getLabel()).getValue());
            }

        });
        try {
            thread.start();
            thread.join();
            return;
        } catch (InterruptedException interruptedException) {

        }
    }

    private void applyFuelPriceUpdate(String fileName) {
        // use the file data to apply the new prices

        ArrayList<GradePrice> currentGradePrices = forecourtController.getConfiguredGradePrices().getGradePrices();
        String currentPriceSetId = String.valueOf(forecourtController.getConfiguredGradePrices().getPriceSetId());

        try {
            // read the file data from the POSMaster download
            ArrayList<String> fuelPriceFileData = readFuelPriceFile(fileName);
            String catalogId = fileName.substring(0, 36);
            String catalogType = fuelPriceFileData.get(0);

            String PriceSetActivationDateTime = fuelPriceFileData.get(3).replace(':', ' ').replace('-', ' ').replace('T', ' ').trim();
            PriceSetActivationDateTime = PriceSetActivationDateTime.substring(0, PriceSetActivationDateTime.indexOf('.'));
            PriceSetActivationDateTime = PriceSetActivationDateTime.replace(" ", "");

            ArrayList<String> priceGroupIds = this.forecourtController.getConfiguredGradePrices().getPriceGroupId();

            ArrayList<String> fuelGrades = new ArrayList<>();
            ArrayList<String> priceGroups = new ArrayList<>();


            for (int i = 4; i < fuelPriceFileData.size(); i++) { // fuel grades and prices are from line 5 onwards in the file from POSMaster
                // find this grade in the Json from forecourt controller - then apply this new price data
                String currentGrade = fuelPriceFileData.get(i).toString().split(",")[1]; // current grade
                String currentPrice = fuelPriceFileData.get(i).toString().split(",")[2]; // current price
                currentPrice = String.valueOf((int) (Float.parseFloat(currentPrice) * 10)); // convert to int to remove decimal place


                for (int x = 0; x < currentGradePrices.size(); x++) {
                    if (currentGradePrices.get(x).getId().equals(currentGrade)) {
                        // we matched the grade from forecourt controller data
                        // assign the price from POSMaster to the forecourt controller object
                        fuelGrades.add(currentGradePrices.get(x).getId());
                        priceGroups.add(currentPrice);
                        break;
                    }

                }
            }
            forecourtController.ChangeFuelPrices(currentPriceSetId, priceGroupIds, fuelGrades, priceGroups, PriceSetActivationDateTime);
            FuelPriceUpdateComplete(fileName, catalogId, catalogType);

        } catch (Exception e) {

        }
    }

    private void FuelPriceUpdateComplete(String tempFuelPriceFile, String catalogId, String catalogType) {
        // update POSMaster to confirm fuel price change is applied
        POSMaster p = null;
        try {
            p = new POSMaster(
                    configurationRepository.getConfigurationByName("posmaster_host").getValue(),
                    configurationRepository.getConfigurationByName("store_id").getValue(),
                    configurationRepository.getConfigurationByName("posmaster_username").getValue(),
                    configurationRepository.getConfigurationByName("posmaster_password").getValue(),
                    configurationRepository.getConfigurationByName("ftp_host").getValue(),
                    configurationRepository.getConfigurationByName("ftp_username").getValue(),
                    configurationRepository.getConfigurationByName("ftp_password").getValue()
            );
        } catch (MalformedURLException e) {
            Log.e("Service Offline", e);
            serviceEvents.systemError("Service Offline", e);
        }
        p.CatalogApplied(context, catalogId, catalogType);
        // delete the temp file with fuel price data
        context.deleteFile(tempFuelPriceFile);
    }

    private ArrayList<String> readFuelPriceFile(String filename) throws FileNotFoundException {
        FileInputStream fis;
        ArrayList<String> stringBuilder = new ArrayList<>();
        try {

            fis = context.openFileInput(filename);
            InputStreamReader inputStreamReader = new InputStreamReader(fis, StandardCharsets.UTF_8);
            try (BufferedReader reader = new BufferedReader(inputStreamReader)) {
                String line = reader.readLine();
                while (line != null) {
                    stringBuilder.add(line);
                    line = reader.readLine();
                }
            } catch (IOException e) {
                // Error occurred when opening raw file for reading.
            }
        } catch (FileNotFoundException e) {
            throw e;
        }
        return stringBuilder;
    }

    public GradePrices getConfiguredGradePrices() {
        List<GradePrice> clone = (List<GradePrice>)forecourtController.getConfiguredGradePrices().getGradePrices().clone();
        GradePrices ret = new GradePrices();
        ret.getGradePrices().addAll(clone.stream().filter(x -> x.getPrice() != 0.0).collect(Collectors.toList()));
        return ret;
    }

    public ArrayList<FuelPoint> getConfiguredFuelPoints() {
        return forecourtController.getConfiguredFuelPoints();
    }

    public static Map<String, String> getFuelGradeNames(IConfigurationRepository configurationRepository) {
        Map<String, String> names = new HashMap<>();
        for (int i = 1; i < 10; i++) {
            names.put("0" + i, configurationRepository.getConfigurationByName("FG_0" + i).getValue());
        }
        return names;
    }

    /*private void saveFuelControllerStatus(JsonNode paramJsonNode) {
        try {

            if (pSS5000Transaction.getFpId() != 0 && pSS5000Transaction.getFpLockId() != 0 && !pSS5000Transaction.getFcGradeId().equals("00"))
                this.pss5000TransactionRepository.savePss5000Transaction(pSS5000Transaction);
            ///TODO: REFRESH FUEL POINTS STATUS
            forecourtController.GetFuelPriceData();

            return;
        } catch (Exception exception) {
            throw exception;
        }
    }*/
}
