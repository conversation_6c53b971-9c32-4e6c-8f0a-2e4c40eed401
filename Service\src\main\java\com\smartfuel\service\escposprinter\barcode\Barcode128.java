package com.smartfuel.service.escposprinter.barcode;

import com.smartfuel.service.escposprinter.EscPosPrinterSize;
import com.smartfuel.service.escposprinter.exceptions.EscPosBarcodeException;

public class Barcode128 extends Barcode {
    public Barcode128(EscPosPrinterSize paramEscPosPrinterSize, String paramString, float paramFloat1, float paramFloat2, int paramInt) throws EscPosBarcodeException {
        super(paramEscPosPrinterSize, 73, paramString, paramFloat1, paramFloat2, paramInt);
    }

    public int getCodeLength() {
        return this.code.length();
    }

    public int getColsCount() {
        return (getCodeLength() + 5) * 11;
    }
}
