package com.smartfuel.service.enums.forecourt.PSS5000;

public enum FuelPointState {

    Calling("04H"),
    Closed("01H"),
    Error("03H"),
    Fuelling("09H"),
    Fuelling_paused("0AH"),
    Fuelling_terminated("0BH"),
    Idle("02H"),
    PreAuthorized("05H"),
    Starting("06H"),
    Starting_paused("07H"),
    Starting_terminated("08H"),
    Unavailable("0CH"),
    Unavailable_and_calling("0DH"),
    Unconfigured("00H");


    // declaring private variable for getting values
    private String state;

    // getter method
    public String getState() {
        return this.state;
    }

    // enum constructor - cannot be public or protected
    private FuelPointState(String state) {
        this.state = state;
    }

}