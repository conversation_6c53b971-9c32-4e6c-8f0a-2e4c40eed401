package com.smartfuel.service.messages.forecourt.POSTEC.response;

//import com.google.gson.JsonObject;
//
//import org.json.JSONException;

import com.smartfuel.service.logger.Log;

public class PCCResponse<T> {
    private final String data;

    public String getData() {
        return data;
    }

    public PCCResponse(){
        this.data = null;
    }
    public PCCResponse(String responseData)  {
        this.data = responseData;
        Log.w("PCCBaseResponse",responseData);

    }
    public String getTcpResponseType(){
        String[] msgResponse = this.data.split(" ");
        int msgSize = msgResponse.length - 1;
        Log.w("getTcpResponseType",msgResponse[msgSize].substring(0, msgResponse[msgSize].length() - 2));
        return msgResponse[msgSize].substring(0, msgResponse[msgSize].length() - 2);
    }
    public String getUdpResponseType(){
        String[] msgResponse = this.data.split(" ");
        int msgSize = msgResponse.length - 1;
        return msgResponse[msgSize].substring(0, msgResponse[msgSize].length() - 1);
    }
    public String ToString() {
        return ToString();
    }

}
