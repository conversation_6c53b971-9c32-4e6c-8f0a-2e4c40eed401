package com.smartfuel.service.repository;

import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;
import com.smartfuel.service.models.posmaster.OPTExport;
import com.smartfuel.service.sqlite.models.FuelTransaction;
import com.smartfuel.service.sqlite.models.Transaction;

import java.util.List;

public interface ITransactionRepository {
    Transaction getCurrentTransaction(int terminalId, int transactionStatus, int pumpNumber);

    FuelTransaction getFuelTransactionById(long paramLong);

    Transaction getTransactionById(long paramLong);
    Transaction getTransactionByFuelPointStatusAndTimeout(int fuelPoint);

    List<Transaction> getOrphanTransactions();

    OPTExport getPosMasterOrders(String posId);

    List<Transaction> getTransactionsForPublish();

    List<WhiteCardReceipt> getWhitecardUserReceipt(String whitecardUserId);
    List<CardReceipt> getCardUserReceipt(List<Integer> terminalTransactionId);

    List<Integer> getCardReceiptTransactions(String cardSignature);
    long saveTransaction(Transaction paramTransaction, FuelTransaction paramFuelTransaction);
    void setTransactionExported(List<String> orderIds);
}
