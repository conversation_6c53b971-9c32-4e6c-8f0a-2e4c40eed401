package com.smartfuel.service.messages.forecourt.POSTEC.response;
/*
            0. IP Address:                  ***************
            1. Broadcast Node Number:       99
            2. Request Number:              080
            3. Sequence Number:             0302
            4. Parameter Count:             04
            5. Socket Status[1]|PCC success
                code[3]:                    3000
            6. Node list count:             02
            7. Client Node number:          06
            7. Client Node number:          08
            8. Client_Login_UpdateN]
         */
public class LoginClientUpdate {
    int numberOfNodeOnline;
    int[] clinetNodeNumber;

    public LoginClientUpdate(String response) {
        String[] responseData = response.split(" ");
        this.numberOfNodeOnline = Integer.parseInt(responseData[6]);
        clinetNodeNumber = new int[numberOfNodeOnline];
        for (int i = 0; i < numberOfNodeOnline; i++) {
            clinetNodeNumber[i] = Integer.parseInt(responseData[7 + i]);
        }
    }

    public int getNumberOfNodeOnline() {
        return numberOfNodeOnline;
    }

    public int[] getClinetNodeNumber() {
        return clinetNodeNumber;
    }
}
