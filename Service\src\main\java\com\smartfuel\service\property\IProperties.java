package com.smartfuel.service.property;

import android.content.Context;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

public interface IProperties {

    void load(InputStream inStream) throws IOException;
    default void load(File file) throws IOException{}

    String getProperty(String key);
    String getProperty(String key, String defaultValue);

    String getAndroidId(Context context);

}
