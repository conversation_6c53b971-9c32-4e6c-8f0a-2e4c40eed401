package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;

import org.json.JSONException;
public class CancelFuellingPointAuthorizeRequest {
    private final FcCancelAuthorizeRequestData data;

    private final String name = "cancel_FpAuth_req";

    private final String subCode = "00H";

    public CancelFuellingPointAuthorizeRequest(String posId, String fuelpointId) {
        this.data = new FcCancelAuthorizeRequestData(posId, fuelpointId);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class FcCancelAuthorizeRequestData {
        private final String FpId;

        private final String PosId;

        FcCancelAuthorizeRequestData(String posId, String fuelpointId) {
            this.FpId = fuelpointId;
            this.PosId = posId;
        }
    }
}
