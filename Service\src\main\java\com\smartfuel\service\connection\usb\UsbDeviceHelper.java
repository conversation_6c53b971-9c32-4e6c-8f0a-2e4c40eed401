package com.smartfuel.service.connection.usb;

import android.hardware.usb.UsbConstants;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbEndpoint;
import android.hardware.usb.UsbInterface;

public class UsbDeviceHelper {
    public static UsbEndpoint findEndpointWrite(UsbInterface paramUsbInterface) {
        if (paramUsbInterface != null) {
            int i = paramUsbInterface.getEndpointCount();
            for (byte b = 0; b < i; b++) {
                UsbEndpoint usbEndpoint = paramUsbInterface.getEndpoint(b);
                if (usbEndpoint.getType() == 2 && usbEndpoint.getDirection() == UsbConstants.USB_DIR_OUT)
                    return usbEndpoint;
            }
        }
        return null;
    }
    public static UsbEndpoint findEndpointRead(UsbInterface paramUsbInterface) {
        if (paramUsbInterface != null) {
            int i = paramUsbInterface.getEndpointCount();
            for (byte b = 0; b < i; b++) {
                UsbEndpoint usbEndpoint = paramUsbInterface.getEndpoint(b);
                if (usbEndpoint.getType() == 2 && usbEndpoint.getDirection() == UsbConstants.USB_DIR_IN)
                    return usbEndpoint;
            }
        }
        return null;
    }

    public static UsbInterface findDeviceInterface(UsbDevice paramUsbDevice) {
        if (paramUsbDevice == null)
            return null;
        int i = paramUsbDevice.getInterfaceCount();
        for (byte b = 0; b < i; b++) {
            UsbInterface usbInterface = paramUsbDevice.getInterface(b);

            if (usbInterface.getInterfaceClass() == 7 || usbInterface.getInterfaceClass() == 10) // currently support a printer interface (7) [Element Printer] or a communication interface (2) [IM30 Terminal]
                return usbInterface;
        }
        return null;
    }
}

