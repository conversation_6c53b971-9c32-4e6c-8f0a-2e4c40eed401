package com.smartfuel.service.messages.forecourt.POSTEC.request;


public class Tank<PERSON>ataRequest extends BaseRequest {
    private char[] tankNumber = new char[2];

    public TankDataRequest(int nodeNumber, int tankNumber){
        super(nodeNumber,14,2,"Req_Tank_Data");
        this.tankNumber = String.format("%02d", tankNumber).toCharArray();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.tankNumber);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
