package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public class TankParametersResponse extends BaseResponse{
    private int tankNumber;
    private int gradeNumber;
    private int ATGType;
    private long capacity;
    private long diameter;
    private long fullRaw;
    private long emptyRaw;
    private int step;
    private long highProductAlarm;
    private long lowProductAlarm;
    private long highWaterAlarm;
    private long theftAlarm;
    private long leakAlarm;
    private int manifoldType;
    private int startAutoCal;
    private int endAutoCal;
    private String thermalCoeff;

    public TankParametersResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.tankNumber = Integer.parseInt(super.responseData[5]);
        this.gradeNumber = Integer.parseInt(super.responseData[6]);
        this.ATGType =  Integer.parseInt(super.responseData[7]);
        this.capacity = Long.parseLong(super.responseData[8]);
        this.diameter = Long.parseLong(super.responseData[9]);
        this.fullRaw = Long.parseLong(super.responseData[10]);
        this.emptyRaw =  Integer.parseInt(super.responseData[11]);
        this.step = Integer.parseInt(super.responseData[12]);
        this.highProductAlarm = Long.parseLong(super.responseData[13]);
        this.lowProductAlarm = Long.parseLong(super.responseData[14]);
        this.highWaterAlarm = Long.parseLong(super.responseData[15]);
        this.theftAlarm = Long.parseLong(super.responseData[16]);
        this.leakAlarm = Long.parseLong(super.responseData[17]);
        this.manifoldType =  Integer.parseInt(super.responseData[18]);
        this.startAutoCal =  Integer.parseInt(super.responseData[19]);
        this.endAutoCal =  Integer.parseInt(super.responseData[20]);
        this.thermalCoeff = super.responseData[21];
    }

    public int getTankNumber() {
        return tankNumber;
    }

    public int getGradeNumber() {
        return gradeNumber;
    }

    public long getATGType() { return  ATGType; }

    public long getCapacity() { return capacity; }

    public long getDiameter() { return diameter; }

    public long getFullRaw() { return fullRaw; }

    public long getEmptyRaw() { return  emptyRaw; }

    public long getStep() { return step; }

    public long getHighProductAlarm() { return highProductAlarm; }

    public long getLowProductAlarm() { return lowProductAlarm; }

    public long getHighWaterAlarm() { return highWaterAlarm; }

    public long getTheftAlarm() { return  theftAlarm; }

    public long getLeakAlarm() { return leakAlarm; }

    public int getManifoldType() { return manifoldType; }

    public int getStartAutoCal() { return startAutoCal; }

    public int getEndAutoCal() { return endAutoCal; }

    public String getThermalCoeff() { return thermalCoeff; }
}
