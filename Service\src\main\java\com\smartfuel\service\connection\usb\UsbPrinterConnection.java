package com.smartfuel.service.connection.usb;

import android.app.AlertDialog;
import android.content.Context;
import android.hardware.usb.UsbConstants;
import android.hardware.usb.UsbDevice;
import androidx.annotation.Nullable;

import com.smartfuel.service.BuildConfig;

public class UsbPrinterConnection extends UsbConnections {

    /**
     * Create a new instance of UsbPrintersConnections
     *
     * @param context Application context
     */
    public UsbPrinterConnection(Context context) {
        super(context);
    }

    /**
     * Easy way to get the first USB printer paired / connected.
     *
     * @return a UsbConnection instance
     */
    @Nullable
    public static UsbConnection selectElementUSBPrinter(Context context) {
        UsbConnection myConnection = null;
        try{
            UsbPrinterConnection printers = new UsbPrinterConnection(context);
            UsbConnection[] usbPrinters = printers.getList();

            for (UsbConnection usbPrinter : usbPrinters) {
                UsbDevice thisDevice = usbPrinter.getDevice();
                // lookup the Element Device which is vendor 8401 and Product 28680
                if (thisDevice.getVendorId() == BuildConfig.printerVendorId  && thisDevice.getProductId() == BuildConfig.printerProductId ) {
                    myConnection = usbPrinter;
                }
            }
        }
        catch(Exception e){
            new AlertDialog.Builder(context)
                    .setTitle("USB Connection")
                    .setMessage(e.getMessage())
                    .show();
        }
        return myConnection;
    }

    @Nullable
    public static UsbConnection selectPAXIM30USB(Context context) {
        UsbConnection myConnection = null;
        try{
            UsbPrinterConnection printers = new UsbPrinterConnection(context);
            UsbConnection[] usbPrinters = printers.getList();

            for (UsbConnection usbPrinter : usbPrinters) {
                UsbDevice thisDevice = usbPrinter.getDevice();
                // lookup the Element Device which is vendor 8401 and Product 28680
                if (thisDevice.getVendorId() == 12216 && (thisDevice.getProductId() == 8789 || thisDevice.getProductId() == 8774)) {
                    myConnection = usbPrinter;
                    break;
                }
            }
        }
        catch(Exception e){
            new AlertDialog.Builder(context)
                    .setTitle("USB Connection")
                    .setMessage(e.getMessage())
                    .show();
        }
        return myConnection;
    }

    @Nullable
    public static UsbConnection selectFirstConnected(Context context) {
        UsbPrinterConnection printers = new UsbPrinterConnection(context);
        UsbConnection[] bluetoothPrinters = printers.getList();

        if (bluetoothPrinters == null || bluetoothPrinters.length == 0) {
            return null;
        }

        return bluetoothPrinters[0];
    }


    /**
     * Get a list of USB printers.
     *
     * @return an array of UsbConnection
     */
    @Nullable
    public UsbConnection[] getList() {
        UsbConnection[] masterUsbConnections = super.getList();

        if (masterUsbConnections == null) {
            return null;
        }

        int i = 0;
        UsbConnection[] connectionsTemp = new UsbConnection[masterUsbConnections.length];
        for (UsbConnection usbConnection : masterUsbConnections) {
            UsbDevice device = usbConnection.getDevice();
            int usbClass = device.getDeviceClass();

            if ((usbClass == UsbConstants.USB_CLASS_PER_INTERFACE || usbClass == UsbConstants.USB_CLASS_MISC) && UsbDeviceHelper.findDeviceInterface(device) != null) {
                usbClass = UsbConstants.USB_CLASS_PRINTER;
            } else if (usbClass == UsbConstants.USB_TYPE_STANDARD && UsbDeviceHelper.findDeviceInterface(device) != null) {
                usbClass = UsbConstants.USB_TYPE_STANDARD;
            }

            if (usbClass == UsbConstants.USB_CLASS_PRINTER || usbClass == UsbConstants.USB_TYPE_STANDARD) {
                connectionsTemp[i++] = new UsbConnection(this.usbManager, device);
            }
        }

        UsbConnection[] usbConnections = new UsbConnection[i];
        System.arraycopy(connectionsTemp, 0, usbConnections, 0, i);
        return usbConnections;
    }

}