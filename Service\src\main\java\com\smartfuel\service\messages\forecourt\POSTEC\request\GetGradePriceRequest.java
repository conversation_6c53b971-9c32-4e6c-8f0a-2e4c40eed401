package com.smartfuel.service.messages.forecourt.POSTEC.request;

public class GetGradePriceRequest extends BaseRequest {
    private final char[] gradeNumber;
    private final char[] cashCreditFlag;
    private final char[] priceType;
    public GetGradePriceRequest(int nodeNumber, int  GradeNumber){
        super(nodeNumber,54,3,"Req_Grade_Prices");
        this.gradeNumber = String.format("%02d",GradeNumber).toCharArray();
        this.cashCreditFlag = "0".toCharArray();
        this.priceType = "0".toCharArray();

    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.gradeNumber);
        returnValue.append(" ");
        returnValue.append(this.cashCreditFlag);
        returnValue.append(" ");
        returnValue.append(this.priceType);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
