package com.smartfuel.service.connection.usb;

import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import com.smartfuel.service.connection.IDeviceConnection;
import com.smartfuel.service.connection.ConnectionException;
import java.io.IOException;

public class UsbConnection implements IDeviceConnection {

    protected UsbOutputStream outputStream;
    protected UsbInputStream inputStream;
    protected byte[] data;

    private UsbManager usbManager;
    private UsbDevice usbDevice;

    /**
     * Create un instance of UsbConnection.
     *
     * @param usbManager an instance of UsbManager
     * @param usbDevice  an instance of UsbDevice
     */
    public UsbConnection(UsbManager usbManager, UsbDevice usbDevice) {
        super();

        this.outputStream = null;
        this.inputStream = null;
        this.data = new byte[0];

        this.usbManager = usbManager;
        this.usbDevice = usbDevice;
    }

    /**
     * Get the instance UsbDevice connected.
     *
     * @return an instance of UsbDevice
     */
    public UsbDevice getDevice() {
        return this.usbDevice;
    }

    /**
     * Start socket connection with the usbDevice.
     */
    public UsbConnection connect() throws ConnectionException {
        if (this.isConnected()) {
            return this;
        }

        try {
            this.outputStream = new UsbOutputStream(this.usbManager, this.usbDevice);
            this.inputStream = new UsbInputStream(this.usbManager, this.usbDevice);
            this.data = new byte[0];
        } catch (IOException e) {
            e.printStackTrace();
            this.outputStream = null;
            throw new ConnectionException("Unable to connect to USB device.");
        }
        return this;
    }

    /**
     * Close the socket connection with the usbDevice.
     */
    public UsbConnection disconnect() {
        this.data = new byte[0];
        if (this.isConnected()) {
            try {
                this.outputStream.close();
                this.inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            this.outputStream = null;
            this.inputStream = null;
        }
        return this;
    }

    /**
     * Check if OutputStream is open.
     *
     * @return true if is connected
     */
    public boolean isConnected() {
        return this.outputStream != null;
    }

    /**
     * Add data to send.
     */
    public void write(byte[] bytes) {
        byte[] data = new byte[bytes.length + this.data.length];
        System.arraycopy(this.data, 0, data, 0, this.data.length);
        System.arraycopy(bytes, 0, data, this.data.length, bytes.length);
        this.data = data;
    }

    /**
     * Send data to the device.
     */
    public void send() throws ConnectionException {
        this.send(0);
    }

    public String read() throws ConnectionException {
        //if (!this.isConnected()) {
        //    throw new ConnectionException("Unable to read data from device.");
        //}
        //int size = this.inputStream.read();
        //while (size!= -1) {
        //    size = this.inputStream.read();
        //}
        //return this.inputStream.getResponse();

        try {
            inputStream.read();
            return inputStream.getResponse();
        }catch (IOException e){
            throw new ConnectionException(e);
        }
    }

    /**
     * Send data to the device.
     */
    public void send(int addWaitingTime) throws ConnectionException {

//        if (!this.isConnected()) {
//            throw new ConnectionException("Unable to send data to device.");
//        }
//        try {
//            this.outputStream.write(this.data);
//            this.outputStream.flush();
//            int waitingTime = addWaitingTime + this.data.length / 16;
//            this.data = new byte[0];
//            if (waitingTime > 0) {
//
//                Thread.sleep(waitingTime);
//            }
//        } catch (IOException | InterruptedException e) {
//            throw new ConnectionException(e.getMessage());
//        }

        try {
            this.outputStream.write(this.data);
            this.data = new byte[0];
        } catch (IOException e) {
            e.printStackTrace();
            throw new ConnectionException(e);
        }

    }
}