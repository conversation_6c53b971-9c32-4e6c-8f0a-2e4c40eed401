package com.smartfuel.service.escposprinter.barcode;

import com.smartfuel.service.escposprinter.EscPosPrinterSize;
import com.smartfuel.service.escposprinter.exceptions.EscPosBarcodeException;

public class BarcodeEAN8 extends BarcodeNumber {
    public BarcodeEAN8(EscPosPrinterSize paramEscPosPrinterSize, String paramString, float paramFloat1, float paramFloat2, int paramInt) throws EscPosBarcodeException {
        super(paramEscPosPrinterSize, 68, paramString, paramFloat1, paramFloat2, paramInt);
    }

    public int getCodeLength() {
        return 8;
    }
}