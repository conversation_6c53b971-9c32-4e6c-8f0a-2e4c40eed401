package com.smartfuel.service.messages.forecourt.POSTEC.request;

/**
 * To request the PCC to clear the transaction data and free up the Sale or Memory buffer for
 * a dispenser.
 * This would normally be sent after the finalisation process.(Download Transaction)
 */
public class ConfirmTransactionExRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] paymentMethod = new char[4];
    private char[] sequenceNumber = new char[6];


    public ConfirmTransactionExRequest(int nodeNumber, int dispenserNumber, int sequenceNumber, int paymentMethod){
        super(nodeNumber, 90,4,"Confirm_Transaction_Ex");
        this.dispenserNumber = String.format("%02d",dispenserNumber).toCharArray();
        this.sequenceNumber = String.format("%06d", sequenceNumber).toCharArray();
        this.paymentMethod = String.format("%04d", paymentMethod).toCharArray();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.sequenceNumber);
        returnValue.append(" ");
        returnValue.append(this.paymentMethod);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
