package com.smartfuel.service.models.kiosk.request;

import com.smartfuel.service.sqlite.models.RewardCardDiscount;

import java.util.List;

public class TransactionRequest {

    public int terminalTransactionId;

    public String transactionDateTime;

    public int type;

    public int status;

    public float authorisedAmount;

    public float finalAmount;

    public float gstAmount;

    public int currency;

    public String applicationId;

    public String applicationLabel;

    public String cardSignature;

    public String cardExpiryDate;

    public int cardATC;

    public String vehicleRegistration;

    public String vehicleOdometer;

    public String accountType;

    public String tid;

    public int stan;

    public String responseCode;

    public String hostResponse;

    public int authorizationID;

    public int panSeqNo;

    public String cvm;

    public String processor;

    public int terminalId;

    public String storeId;

    public String kioskId;

    public String accountId;

    public String userId;

    public FuelTransaction fuelTransaction;

    public List<RewardCardDiscount> rewardCardTransactions;


    public int getTerminalTransactionId() {
        return terminalTransactionId;
    }

    public String getTransactionDateTime() {
        return transactionDateTime;
    }

    public int getType() {
        return type;
    }

    public int getStatus() {
        return status;
    }

    public float getAuthorisedAmount() {
        return authorisedAmount;
    }

    public float getFinalAmount() {
        return finalAmount;
    }

    public float getGstAmount() {
        return gstAmount;
    }

    public int getCurrency() {
        return currency;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public String getApplicationLabel() {
        return applicationLabel;
    }

    public String getCardSignature() {
        return cardSignature;
    }

    public String getCardExpiryDate() {
        return cardExpiryDate;
    }

    public int getCardATC() {
        return cardATC;
    }

    public String getVehicleRegistration() {
        return vehicleRegistration;
    }

    public String getVehicleOdometer() {
        return vehicleOdometer;
    }

    public String getAccountType() {
        return accountType;
    }

    public String getTid() {
        return tid;
    }

    public int getStan() {
        return stan;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public String getHostResponse() {
        return hostResponse;
    }

    public int getAuthorizationID() {
        return authorizationID;
    }

    public int getPanSeqNo() {
        return panSeqNo;
    }

    public String getCvm() {
        return cvm;
    }

    public String getProcessor() {
        return processor;
    }

    public int getTerminalId() {
        return terminalId;
    }

    public String getStoreId() {
        return storeId;
    }

    public String getKioskId() {
        return kioskId;
    }

    public String getAccountId() {
        return accountId;
    }

    public String getUserId() {
        return userId;
    }

    public FuelTransaction getFuelTransaction() {
        return fuelTransaction;
    }

    public void setTerminalTransactionId(int terminalTransactionId) {
        this.terminalTransactionId = terminalTransactionId;
    }

    public void setTransactionDateTime(String transactionDateTime) {
        this.transactionDateTime = transactionDateTime;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setAuthorisedAmount(float authorisedAmount) {
        this.authorisedAmount = authorisedAmount;
    }

    public void setFinalAmount(float finalAmount) {
        this.finalAmount = finalAmount;
    }

    public void setGstAmount(float gstAmount) {
        this.gstAmount = gstAmount;
    }

    public void setCurrency(int currency) {
        this.currency = currency;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public void setApplicationLabel(String applicationLabel) {
        this.applicationLabel = applicationLabel;
    }

    public void setCardSignature(String cardSignature) {
        this.cardSignature = cardSignature;
    }

    public void setCardExpiryDate(String cardExpiryDate) {
        this.cardExpiryDate = cardExpiryDate;
    }

    public void setCardATC(int cardATC) {
        this.cardATC = cardATC;
    }

    public void setVehicleRegistration(String vehicleRegistration) {
        this.vehicleRegistration = vehicleRegistration;
    }

    public void setVehicleOdometer(String vehicleOdometer) {
        this.vehicleOdometer = vehicleOdometer;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public void setStan(int stan) {
        this.stan = stan;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public void setHostResponse(String hostResponse) {
        this.hostResponse = hostResponse;
    }

    public void setAuthorizationID(int authorizationID) {
        this.authorizationID = authorizationID;
    }

    public void setPanSeqNo(int panSeqNo) {
        this.panSeqNo = panSeqNo;
    }

    public void setCvm(String cvm) {
        this.cvm = cvm;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public void setTerminalId(int terminalId) {
        this.terminalId = terminalId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public void setKioskId(String kioskId) {
        this.kioskId = kioskId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public void setFuelTransaction(FuelTransaction fuelTransaction) {
        this.fuelTransaction = fuelTransaction;
    }

    public List<RewardCardDiscount> getRewardCardTransactions() {
        return rewardCardTransactions;
    }

    public void setRewardCardTransactions(List<RewardCardDiscount> rewardCardTransactions) {
        this.rewardCardTransactions = rewardCardTransactions;
    }

    public static class FuelTransaction {
        public long fuelAmount;

        public String fuelGradeId;

        public String fuelGradeName;

        public long fuelPrice;

        public long fuelVolume;

        public int pumpNumber;

        public int transactionSeqNumber;
    }
}
