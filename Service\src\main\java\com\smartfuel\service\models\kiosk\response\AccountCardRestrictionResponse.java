package com.smartfuel.service.models.kiosk.response;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;

public class AccountCardRestrictionResponse extends BaseResponse implements List<AccountCardRestriction> {

    private ArrayList<AccountCardRestriction> list;

    public AccountCardRestrictionResponse(){
        list = new ArrayList<>();
    }

    public String[] toStringArray(){
        return list.stream()
                .map(AccountCardRestriction::getFuelGrade)
                .toArray(String[]::new);
    }

    @Override
    public int size() {
        return list.size();
    }

    @Override
    public boolean isEmpty() {
        return list.isEmpty();
    }

    @Override
    public boolean contains(@Nullable Object o) {
        return list.contains(o);
    }

    @NonNull
    @Override
    public Iterator<AccountCardRestriction> iterator() {
        return list.iterator();
    }

    @NonNull
    @Override
    public Object[] toArray() {
        return list.toArray();
    }

    @NonNull
    @Override
    public <T> T[] toArray(@NonNull T[] a) {
        return list.toArray(a);
    }

    @Override
    public boolean add(AccountCardRestriction AccountCardRestriction) {
        return list.add(AccountCardRestriction);
    }

    @Override
    public boolean remove(@Nullable Object o) {
        return list.remove(o);
    }

    @Override
    public boolean containsAll(@NonNull Collection<?> c) {
        return list.containsAll(c);
    }

    @Override
    public boolean addAll(@NonNull Collection<? extends AccountCardRestriction> c) {
        return list.addAll(c);
    }

    @Override
    public boolean addAll(int index, @NonNull Collection<? extends AccountCardRestriction> c) {
        return list.addAll(index, c);
    }

    @Override
    public boolean removeAll(@NonNull Collection<?> c) {
        return list.removeAll(c);
    }

    @Override
    public boolean retainAll(@NonNull Collection<?> c) {
        return list.retainAll(c);
    }

    @Override
    public void clear() {
        list.clear();
    }

    @Override
    public AccountCardRestriction get(int index) {
        return list.get(index);
    }

    @Override
    public AccountCardRestriction set(int index, AccountCardRestriction element) {
        return list.set(index, element);
    }

    @Override
    public void add(int index, AccountCardRestriction element) {
        list.add(index, element);
    }

    @Override
    public AccountCardRestriction remove(int index) {
        return list.remove(index);
    }

    @Override
    public int indexOf(@Nullable Object o) {
        return list.indexOf(o);
    }

    @Override
    public int lastIndexOf(@Nullable Object o) {
        return list.lastIndexOf(o);
    }

    @NonNull
    @Override
    public ListIterator<AccountCardRestriction> listIterator() {
        return list.listIterator();
    }

    @NonNull
    @Override
    public ListIterator<AccountCardRestriction> listIterator(int index) {
        return list.listIterator(index);
    }

    @NonNull
    @Override
    public List<AccountCardRestriction> subList(int fromIndex, int toIndex) {
        return list.subList(fromIndex, toIndex);
    }
}
