package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;
import org.json.JSONException;
public class UnlockSupTransactionRequest {
    private final UnlockSupTransactionRequestData data;

    private final String name = "unlock_FpSupTrans_req";

    private final String subCode = "00H";

    public UnlockSupTransactionRequest(String fuelpointId, String tranSeqNo, String posId) {
        this.data = new UnlockSupTransactionRequestData(fuelpointId, posId, tranSeqNo);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class UnlockSupTransactionRequestData {
        private final String FpId;

        private final String PosId;

        private final String TransSeqNo;

        UnlockSupTransactionRequestData(String fuelpointId, String posId, String transSeqNo) {
            this.FpId = fuelpointId;
            this.PosId = posId;
            this.TransSeqNo = transSeqNo;
        }
    }
}
