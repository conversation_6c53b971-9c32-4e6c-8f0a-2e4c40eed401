package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

import java.util.Calendar;
import java.util.Date;

/*
01 005 0123 15 2000 04 S 0 01 00468 00048 9845 01 06 22 12 54 42 000009 1234
Download_Transaction
 01 = client node number
 005 = request number
 0123 = sequence number
 16 = number of parameters
 2000 = socket success code (2) + PCC success code (000)
 04 = Dispenser number
 S = Transaction type
 0 = Hose number
 01 = Grade number
 000468 = Money
 000048 = Volume
 9845 = Unit price
 01 = Year
 06 = Month
 22 = Day
 12 = Hour
 54 = Minute
 42 = Second
 000009 = Transaction number
 1234 = Attendant number
 */
public class DownloadTransactionResponse extends BaseResponse {
    private char transactionType;
    private int dispenserNumber;
    private int hoseNumber;
    private int gradeId;
    private long money;
    private long volume;
    private long unitPrice;
    private Date transactionDate;
    private int sequenceNumber;
    private String transactionNumber;
    private String attendantNumber;

    public DownloadTransactionResponse(String response) throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.transactionType = super.responseData[6].toCharArray()[0];
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
        this.hoseNumber =Integer.parseInt(super.responseData[7]);
        this.gradeId = Integer.parseInt(super.responseData[8]);
        this.money = Long.parseLong(super.responseData[9]);
        this.volume = Long.parseLong(super.responseData[10]);
        this.unitPrice = Long.parseLong(super.responseData[11]);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR,Integer.parseInt(super.responseData[12]));
        calendar.set(Calendar.MONTH,Integer.parseInt(super.responseData[13]));
        calendar.set(Calendar.DAY_OF_MONTH,Integer.parseInt(super.responseData[14]));
        calendar.set(Calendar.HOUR_OF_DAY,Integer.parseInt(super.responseData[15]));
        calendar.set(Calendar.MINUTE,Integer.parseInt(super.responseData[16]));
        calendar.set(Calendar.SECOND,Integer.parseInt(super.responseData[17]));
        this.transactionDate = calendar.getTime();
        this.sequenceNumber = Integer.parseInt(super.responseData[2]);
        this.transactionNumber = super.responseData[18];
        this.attendantNumber = super.responseData[19];
    }

    public char getTransactionType() {
        return transactionType;
    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }

    public int getHoseNumber() {
        return hoseNumber;
    }

    public int getGradeId() {
        return gradeId;
    }

    public long getMoney() {
        return money;
    }

    public long getVolume() {
        return volume;
    }

    public long getUnitPrice() {
        return unitPrice;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    @Override
    public int getSequenceNumber() {
        return sequenceNumber;
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public String getAttendantNumber() {
        return attendantNumber;
    }
}
