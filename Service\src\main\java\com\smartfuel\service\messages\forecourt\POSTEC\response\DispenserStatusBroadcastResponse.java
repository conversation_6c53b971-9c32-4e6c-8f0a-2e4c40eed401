package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public class DispenserStatusBroadcastResponse extends BaseResponse{
    private int dispenserNumber;

    public DispenserStatusBroadcastResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
    }

    public int getGradeNumber() {
        return dispenserNumber;
    }
}
