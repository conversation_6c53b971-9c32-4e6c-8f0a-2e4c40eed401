package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public class GradeTitleResponse extends BaseResponse{
    private int gradeNumber;
    private String gradeTitle;

    public GradeTitleResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.gradeNumber = Integer.parseInt(super.responseData[5]);
        this.gradeTitle = super.responseData[6];
    }

    public int getGradeNumber() {
        return gradeNumber;
    }

    public String getGradeTitle() {
        return gradeTitle;
    }
}
