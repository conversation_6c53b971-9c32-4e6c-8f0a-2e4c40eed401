package com.smartfuel.service.messages.forecourt.POSTEC.request;

import java.util.Calendar;
import java.util.Date;


public class SetSystemTimeDateRequest extends BaseRequest {
    /*String gradeNumber, Date effectiveChangeDateTime, String gradePrice*/
    private char[] year = new char[2];
    private char[] month = new char[2];
    private char[] day = new char[2];
    private char[] hour = new char[2];
    private char[] minute = new char[2];
    private char[] second = new char[2];
    private char[] dayOfWeek = new char[2];



    public SetSystemTimeDateRequest(int nodeNumber, Date effectiveChangeDateTime){
        super(nodeNumber,40,8,"Set_System_Time_Date");

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(effectiveChangeDateTime);
        this.year = String.format("%02d",calendar.get(Calendar.YEAR)).toCharArray();
        this.month = String.format("%02d",calendar.get(Calendar.MONTH)).toCharArray();
        this.day = String.format("%02d",calendar.get(Calendar.DAY_OF_MONTH)).toCharArray();
        this.hour = String.format("%02d",calendar.get(Calendar.HOUR_OF_DAY)).toCharArray();
        this.minute = String.format("%02d",calendar.get(Calendar.MINUTE)).toCharArray();
        this.second = String.format("%02d",calendar.get(Calendar.SECOND)).toCharArray();
        this.dayOfWeek = String.format("%02d",calendar.get(Calendar.DAY_OF_WEEK)).toCharArray();
    }

    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.year);
        returnValue.append(" ");
        returnValue.append(this.month);
        returnValue.append(" ");
        returnValue.append(this.day);
        returnValue.append(" ");
        returnValue.append(this.hour);
        returnValue.append(" ");
        returnValue.append(this.minute);
        returnValue.append(" ");
        returnValue.append(this.second);
        returnValue.append(" ");
        returnValue.append(this.dayOfWeek);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
