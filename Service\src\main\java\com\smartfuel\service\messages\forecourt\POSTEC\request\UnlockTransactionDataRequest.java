package com.smartfuel.service.messages.forecourt.POSTEC.request;


/**
 * This enables the POS client to release exclusive rights to a transaction. This facilitates the
 * voiding of the dispenser transaction item from the current sale or voiding the entire sale.
 * Unlocking the transaction enables another PCC client to have access to the transaction for
 * finalization.
 */
public class UnlockTransactionDataRequest extends BaseRequest {
    private char[] dispenserNumber = new char[2];
    private char[] sequenceNumber = new char[6];

    public UnlockTransactionDataRequest(int nodeNumber, int dispenserNumber, long sequenceNumber)
    {
        super(nodeNumber,89,3,"Unlock_Transaction_Data");
        this.dispenserNumber = String.format("%02d", dispenserNumber).toCharArray();
        this.sequenceNumber = String.format("%06d", sequenceNumber).toCharArray();
    }
    @Override
    public String toString(){
        StringBuilder returnValue = new StringBuilder();
        returnValue.append(super.getNodeNumber());
        returnValue.append(" ");
        returnValue.append(super.getRequestNumber());
        returnValue.append(" ");
        returnValue.append(super.getParameterCount());
        returnValue.append(" ");
        returnValue.append(this.dispenserNumber);
        returnValue.append(" ");
        returnValue.append(this.sequenceNumber);
        returnValue.append(" ");
        returnValue.append(super.getFunctionName());
        returnValue.append(super.generateMessageCheckSum(returnValue.toString()));
        returnValue.append("\r\n");
        return returnValue.toString();
    }
}
