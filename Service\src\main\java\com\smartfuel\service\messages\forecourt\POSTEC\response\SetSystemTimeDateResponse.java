package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

import java.util.Calendar;
import java.util.Date;


public class SetSystemTimeDateResponse extends BaseResponse {

    private Date transactionDate;

    public SetSystemTimeDateResponse(String response) throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, Integer.parseInt(super.responseData[5]));
        calendar.set(Calendar.MONTH, Integer.parseInt(super.responseData[6]));
        calendar.set(Calendar.DAY_OF_MONTH, Integer.parseInt(super.responseData[7]));
        calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(super.responseData[8]));
        calendar.set(Calendar.MINUTE, Integer.parseInt(super.responseData[9]));
        calendar.set(Calendar.SECOND, Integer.parseInt(super.responseData[10]));
        this.transactionDate = calendar.getTime();
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

}
