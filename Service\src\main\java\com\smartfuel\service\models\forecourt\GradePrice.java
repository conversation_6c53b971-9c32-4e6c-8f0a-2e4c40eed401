package com.smartfuel.service.models.forecourt;

import android.graphics.Color;

public class GradePrice {
    String Id;

    String Label;

    String Name;

    Float Price;
    String PriceGroupId;

    public String getId() {
        return this.Id;
    }

    public String getLabel() {
        return this.Label;
    }

    public String getName() {
        return this.Name;
    }

    public Float getPrice() {
        return this.Price;
    }
    public String getPriceGroupId(){return this.PriceGroupId;}
    public void setId(String paramString) {
        this.Id = paramString;
    }

    public void setLabel(String paramString) {
        this.Label = paramString;
    }

    public void setName(String paramString) {
        this.Name = paramString;
    }

    public void setPrice(Float paramFloat) {
        this.Price = paramFloat;
    }
    public void setPriceGroupId(String priceGroupId){
        this.PriceGroupId = priceGroupId;
    }

    public int getColor(){
        switch (Id){
            case "01":
                return Color.rgb(250, 235, 215);
            case "02":
                return Color.rgb(255, 236, 67);
            case "03":
                return Color.rgb(251, 167, 47);
            case "04":
                return Color.rgb(34, 127, 25);
            case "05":
                return Color.rgb(247, 37, 28);
            case "06":
                return Color.rgb(0, 0, 0);
            case "07":
                return Color.rgb(128, 128, 128);
            case "08":
                return Color.rgb(0, 14, 249);
            case "09":
                return Color.rgb(160, 48, 46);
            default:
                return Color.BLACK;
        }
    }
}
