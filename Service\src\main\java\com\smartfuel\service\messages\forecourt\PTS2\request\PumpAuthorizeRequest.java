package com.smartfuel.service.messages.forecourt.PTS2.request;

import java.util.ArrayList;

public class PumpAuthorizeRequest {
    private ArrayList<PumpAuthorizeRequest.Packet> Packets;
    private String Protocol;
    public PumpAuthorizeRequest(int pumpNumber, float amount, String[] fuelGradeIds){
        PumpAuthorizeRequest.Data d = new PumpAuthorizeRequest.Data();
        d.setPump(pumpNumber);
        d.setDose(amount);
        if(fuelGradeIds != null && fuelGradeIds.length > 0){
            int[] intArray = new int[fuelGradeIds.length];
            for (int i = 0; i < fuelGradeIds.length; i++) {
                intArray[i] = Integer.parseInt(fuelGradeIds[i]);
            }
            d.setFuelGradeIds(intArray);
        }

        PumpAuthorizeRequest.Packet p = new PumpAuthorizeRequest.Packet();
        p.setId(1);
        p.setType("PumpAuthorize");
        p.setData(d);

        this.Protocol = "jsonPTS";

        Packets = new ArrayList<PumpAuthorizeRequest.Packet>();
        this.Packets.add(p);
    }
    public class Packet {
        public int getId() {
            return Id;
        }

        public void setId(int id) {
            this.Id = id;
        }

        public String getType() {
            return Type;
        }

        public void setType(String type) {
            this.Type = type;
        }

        private int Id;
        private String Type;

        public PumpAuthorizeRequest.Data getData() {
            return Data;
        }

        public void setData(PumpAuthorizeRequest.Data data) {
            Data = data;
        }

        private PumpAuthorizeRequest.Data Data;
    }
    public class Data{


        private int Pump;
        private boolean AutoCloseTransaction = true;
        private String Type = "Amount";
        private float Dose;
        private int[] FuelGradeIds;

        public Data(){
            FuelGradeIds = null;
        }

        public float getDose() {
            return Dose;
        }

        public void setDose(float dose) {
            Dose = dose;
        }
        public int getPump() {
            return Pump;
        }

        public void setPump(int pump) {
            Pump = pump;
        }

        public int[] getFuelGradeIds() {
            return FuelGradeIds;
        }

        public void setFuelGradeIds(int[] fuelGradeIds) {
            FuelGradeIds = fuelGradeIds;
        }
    }
}
