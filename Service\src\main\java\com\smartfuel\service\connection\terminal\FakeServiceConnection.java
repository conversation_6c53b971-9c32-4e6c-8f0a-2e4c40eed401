package com.smartfuel.service.connection.terminal;

import android.content.Context;

import com.google.gson.Gson;
import com.smartfuel.service.connection.ConnectionException;
import com.smartfuel.service.connection.IDeviceConnection;

public class FakeServiceConnection implements IDeviceConnection {

    private static final String TAG = FakeServiceConnection.class.getSimpleName();

    private Context context;
    private ReceiveMessage1 dataReceive1;
    private ReceiveMessage2 dataReceive2;
    private SendMessage dataSend;

    Gson gson = new Gson();

    int imReference = 1;

    private static FakeServiceConnection instance = null;

    public static FakeServiceConnection getInstance(Context context){
        if(instance == null)
            instance = new FakeServiceConnection();

        instance.dataReceive1 = null;
        instance.dataReceive2 = null;
        instance.dataSend = null;
        instance.context = context;

        return instance;
    }

    private FakeServiceConnection(){
    }

    @Override
    public IDeviceConnection connect() throws ConnectionException {
        return this;
    }

    @Override
    public IDeviceConnection disconnect(){
        return this;
    }

    @Override
    public boolean isConnected(){
        return true;
    }

    @Override
    public void write(byte[] bytes){
        String str = new String(bytes);
        dataSend = gson.fromJson(str, SendMessage.class);
    }

    @Override
    public String read() throws ConnectionException{
        String read = "";
        if(dataSend != null){
            if(dataSend.processType == 0){
                if(dataReceive1 == null) {
                    dataReceive1 = new ReceiveMessage1();
                    dataReceive1.externalReference = dataSend.externalReference;
                    dataReceive1.state = 1;
                    read = gson.toJson(dataReceive1);
                }
                else if (dataReceive2 == null){
                    dataReceive2 = new ReceiveMessage2();
                    dataReceive2.authorizationType = 99;
                    //dataReceive2.completedUTCDateTime = date
                    //dataReceive2.createdUTCDateTime = date
                    dataReceive2.externalReference = dataReceive1.externalReference;
                    dataReceive2.im30State = 0;
                    dataReceive2.im30TerminalId = "VP000001";
                    dataReceive2.transactionAmount = dataSend.transactionAmount;
                    dataReceive2.transactionCurrency = dataSend.transactionCurrency;
                    dataReceive2.transactionStatus = 0;
                    dataReceive2.transactionType = 0;
                    read = gson.toJson(dataReceive2);
                }
                else if(dataReceive2.im30State == 0){
                    if(dataReceive2.transactionAmount == 50100) {
                        dataReceive2.authorizationType = 1;
                        dataReceive2.cardExpiryDate = "1012";
                        dataReceive2.cardSignature = "704600**********4800";
                        dataReceive2.cardType = "CompacCard";
                    }
                    else {
                        dataReceive2.authorizationType = 0;
                        dataReceive2.applicationId = "A0000000031010";
                        dataReceive2.applicationLabel = "Visa Credit";
                        dataReceive2.applicationTransactionCounter = 1497;
                        dataReceive2.cardExpiryDate = "2406";
                        dataReceive2.cardSequenceNumber = "1";
                        dataReceive2.cardSignature = "494052**********9173";
                        dataReceive2.cardType = "VISA";
                    }
                    dataReceive2.im30State = 1;
                    read = gson.toJson(dataReceive2);
                }
                else if(dataReceive2.im30State == 1){
                    dataReceive2.im30State = 3;
                    read = gson.toJson(dataReceive2);
                }
                else if(dataReceive2.im30State == 3){
                    if(dataReceive2.transactionAmount == 50000) {
                        dataReceive2.gatewayResponse = "Insufficient Funds";
                        dataReceive2.gatewayResponseCode = "51";
                        dataReceive2.im30Reference = String.valueOf(++imReference);
                        dataReceive2.merchantId = "MERCH1";
                        dataReceive2.stan = "1";
                        dataReceive2.transactionStatus = 2;
                        dataReceive2.im30State = 5;
                        read = gson.toJson(dataReceive2);
                        dataSend = null;
                        dataReceive1 = null;
                        dataReceive2 = null;
                    }
                    else {
                        dataReceive2.gatewayResponse = "Transaction Approved";
                        dataReceive2.gatewayResponseCode = "00";
                        dataReceive2.im30Reference = String.valueOf(++imReference);
                        dataReceive2.merchantId = "MERCH1";
                        if (dataReceive2.transactionAmount == 50100) {
                            dataReceive2.stan = "120";
                            dataReceive2.authorizationId = "953188";
                        }
                        else{
                            dataReceive2.stan = "1";
                            dataReceive2.authorizationId = "000000";
                        }
                        dataReceive2.transactionStatus = 1;
                        dataReceive2.im30State = 4;
                        read = gson.toJson(dataReceive2);
                    }
                }
                else if(dataReceive2.im30State == 4){
                    dataReceive2.im30State = 6;
                    read = gson.toJson(dataReceive2);
                    dataSend = null;
                    dataReceive1 = null;
                    dataReceive2 = null;
                }
            }
            else if(dataSend.processType == 1) {
                if (dataReceive1 == null) {
                    dataReceive1 = new ReceiveMessage1();
                    dataReceive1.externalReference = dataSend.externalReference;
                    dataReceive1.state = 1;
                    read = gson.toJson(dataReceive1);
                }
                else if (dataReceive2 == null){
                    dataReceive2 = new ReceiveMessage2();
                    dataReceive2.externalReference = dataReceive1.externalReference;
                    dataReceive2.im30State = 7;
                    dataReceive2.im30TerminalId = "VP000001";
                    dataReceive2.transactionAmount = dataSend.transactionAmount;
                    dataReceive2.transactionCurrency = dataSend.transactionCurrency;
                    dataReceive2.transactionStatus = 0;
                    dataReceive2.transactionType = 0;
                    dataReceive2.applicationId = "A0000000031010";
                    dataReceive2.applicationLabel = "Visa Credit";
                    dataReceive2.applicationTransactionCounter = 1497;
                    dataReceive2.cardExpiryDate = "2406";
                    dataReceive2.cardSequenceNumber = "1";
                    dataReceive2.cardSignature = "494052**********9173";
                    dataReceive2.cardType = "VISA";
                    dataReceive2.authorizationType = 0;
                    dataReceive2.gatewayResponse = "Transaction Approved";
                    dataReceive2.gatewayResponseCode = "00";
                    dataReceive2.im30Reference = String.valueOf(++imReference);
                    dataReceive2.merchantId = "MERCH1";
                    dataReceive2.stan = "1";
                    dataReceive2.authorizationId = "000000";
                    dataReceive2.transactionStatus = 1;
                    read = gson.toJson(dataReceive2);
                }else if(dataReceive2.im30State == 7){
                    dataReceive2.im30State = 8;
                    read = gson.toJson(dataReceive2);
                    dataSend = null;
                    dataReceive1 = null;
                    dataReceive2 = null;
                }
            }
            else if(dataSend.processType == 2) {
                if (dataReceive1 == null) {
                    dataReceive1 = new ReceiveMessage1();
                    dataReceive1.externalReference = dataSend.externalReference;
                    dataReceive1.state = 1;
                    read = gson.toJson(dataReceive1);
                }
                else if (dataReceive2 == null){
                    dataReceive2 = new ReceiveMessage2();
                    dataReceive2.externalReference = dataReceive1.externalReference;
                    dataReceive2.im30State = 10;
                    dataReceive2.im30TerminalId = "VP000001";
                    dataReceive2.transactionAmount = dataSend.transactionAmount;
                    dataReceive2.transactionCurrency = dataSend.transactionCurrency;
                    dataReceive2.transactionStatus = 0;
                    dataReceive2.transactionType = 0;
                    dataReceive2.applicationId = "A0000000031010";
                    dataReceive2.applicationLabel = "Visa Credit";
                    dataReceive2.applicationTransactionCounter = 1497;
                    dataReceive2.cardExpiryDate = "2406";
                    dataReceive2.cardSequenceNumber = "1";
                    dataReceive2.cardSignature = "494052**********9173";
                    dataReceive2.cardType = "VISA";
                    dataReceive2.authorizationType = 0;
                    dataReceive2.gatewayResponse = "Transaction Approved";
                    dataReceive2.gatewayResponseCode = "00";
                    dataReceive2.im30Reference = String.valueOf(++imReference);
                    dataReceive2.merchantId = "MERCH1";
                    dataReceive2.stan = "1";
                    dataReceive2.authorizationId = "000000";
                    dataReceive2.transactionStatus = 1;
                    read = gson.toJson(dataReceive2);
                }else if(dataReceive2.im30State == 10){
                    dataReceive2.im30State = 11;
                    read = gson.toJson(dataReceive2);
                    dataSend = null;
                    dataReceive1 = null;
                    dataReceive2 = null;
                }
            }
            else if(dataSend.processType == 3){
                if(dataReceive1 == null) {
                    dataReceive1 = new ReceiveMessage1();
                    dataReceive1.externalReference = dataSend.externalReference;
                    dataReceive1.state = 1;
                    read = gson.toJson(dataReceive1);
                }
                else if (dataReceive2 == null){
                    dataReceive2 = new ReceiveMessage2();
                    //dataReceive2.completedUTCDateTime = date
                    //dataReceive2.createdUTCDateTime = date
                    dataReceive2.externalReference = dataReceive1.externalReference;
                    dataReceive2.magneticState = 0;
                    read = gson.toJson(dataReceive2);
                }
                else if(dataReceive2.magneticState == 0){
                    dataReceive2.magneticState = 1;
                    dataReceive2.trackData2 = "4940521800539173";
                    read = gson.toJson(dataReceive2);
                    dataSend = null;
                    dataReceive1 = null;
                    dataReceive2 = null;
                }
            }
            else if(dataSend.processType == 4){
                if(dataReceive1 == null) {
                    dataReceive1 = new ReceiveMessage1();
                    dataReceive1.externalReference = dataSend.externalReference;
                    dataReceive1.state = 1;
                    read = gson.toJson(dataReceive1);
                }
                else if (dataReceive2 == null){
                    dataReceive2 = new ReceiveMessage2();
                    //dataReceive2.completedUTCDateTime = date
                    //dataReceive2.createdUTCDateTime = date
                    dataReceive2.externalReference = dataReceive1.externalReference;
                    dataReceive2.receiptState = 0;
                    read = gson.toJson(dataReceive2);
                }
                else if(dataReceive2.receiptState == 0){
                    dataReceive2.receiptState = 1;
                    dataReceive2.cardSignature = "494052**********9173";
                    read = gson.toJson(dataReceive2);
                    dataSend = null;
                    dataReceive1 = null;
                    dataReceive2 = null;
                }
            }
            else if(dataSend.processType == 99) {
                if (dataReceive1 == null) {
                    dataReceive1 = new ReceiveMessage1();
                    dataReceive1.externalReference = dataSend.externalReference;
                    dataReceive1.state = 1;
                    read = gson.toJson(dataReceive1);
                    dataSend = null;
                    dataReceive1 = null;
                    dataReceive2 = null;
                }
            }
        }

        return read;
    }

    @Override
    public void send() throws ConnectionException {

    }

    @Override
    public void send(int addWaitingTime) throws ConnectionException {

    }

    private class SendMessage{
        public String externalDeviceToken;
        public String externalReference;
        public int processType;
        public long transactionAmount;
        public int transactionCurrency;
    }
    private class ReceiveMessage1{
        public String externalReference;
        public String im30Reference;
        public int state;
    }
    private class ReceiveMessage2 {
        public String applicationId;
        public String applicationLabel;
        public int applicationTransactionCounter;
        public String authorizationId;
        public int authorizationType;
        public String cardExpiryDate;
        public String cardSequenceNumber;
        public String cardSignature;
        public String cardType;
        public int cardVerificationMethod;
        public String completedUTCDateTime;
        public String createdUTCDateTime;
        public String externalReference;
        public String gatewayResponse;
        public String gatewayResponseCode;
        public String im30Reference;
        public int im30State;
        public String im30TerminalId;
        public String merchantId;
        public String stan;
        public long transactionAmount;
        public int transactionCurrency;
        public int transactionStatus;
        public int transactionType;

        public int magneticState;
        public String trackData1;
        public String trackData2;
        public String trackData3;

        public int receiptState;
    }
}

