package com.smartfuel.service.logger;

import android.content.Context;

import com.smartfuel.service.IKioskApiService;
import com.smartfuel.service.OPTService;
import com.smartfuel.service.models.kiosk.request.InfoRequest;
import com.smartfuel.service.utils.ApiServiceHelper;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class LogInfo {

    private Context context;
    private ScheduledExecutorService scheduledSendInfoExecutorService;
    private IKioskApiService kioskApiService;

    private boolean isSetup = false;
    private boolean isStarted = false;
    private String version = "";

    public LogInfo(String version){
        this.version = version;
        this.scheduledSendInfoExecutorService = Executors.newSingleThreadScheduledExecutor();
    }

    public void setup(Context context, IKioskApiService kioskApiService){
        this.context = context;
        this.kioskApiService = kioskApiService;
        isSetup = true;
    }

    public void start() {
        if(isStarted)
            return;
        isStarted = true;

        scheduledSendInfoExecutorService.scheduleAtFixedRate(() -> {
            if(!isSetup)
                return;

            synchronized (LogInfo.this) {
                try {
                    InfoRequest info = new InfoRequest();
                    info.setVersion(version);
                    if(context != null && context instanceof OPTService)
                        info.setLastScreen(((OPTService)context).getCurrentActivityName());
                    info.setMemoryUsage(getMemoryUsage());
                    ApiServiceHelper.executeAPI(this.kioskApiService.sendKioskInfo(info));
                } catch (Exception e) {
                    Log.w("LogInfo", "Send Kiosk Info Failed");
                }
            }
        }, 1, 30, TimeUnit.MINUTES);
    }

    private int getMemoryUsage(){
        try {
            FileReader fileReader = new FileReader("/proc/meminfo");
            BufferedReader bufferedReader = new BufferedReader(fileReader);

            long totalMemory = 0;
            long freeMemory = 0;

            String line;
            while ((line = bufferedReader.readLine()) != null) {
                if (line.startsWith("MemTotal:")) {
                    totalMemory = Long.parseLong(line.split("\\s+")[1]);
                }
                if (line.startsWith("MemFree:")) {
                    freeMemory = Long.parseLong(line.split("\\s+")[1]);
                }
            }

            bufferedReader.close();
            fileReader.close();

            return (int)((totalMemory > 0) ? 100 - (freeMemory * 100 / totalMemory) : 0);

        } catch (Exception e) {
            Log.w("getMemoryUsage", e);
        }
        return 0;
    }

}
