package com.smartfuel.service.network;

public class SocketConfig {
    public int port;

    public int timeOut;

    public SocketConfig(int port, int timeout) {
        this.port = port;
        this.timeOut = timeout;
    }

    public boolean equals(Object socketConfig) {
        boolean bool = true;
        if (this == socketConfig)
            return true;
        if (socketConfig == null || getClass() != socketConfig.getClass())
            return false;
        socketConfig = socketConfig;
        if (this.port != ((SocketConfig)socketConfig).port)
            return false;
        if (this.timeOut != ((SocketConfig)socketConfig).timeOut)
            bool = false;
        return bool;
    }

    public int hashCode() {
        return this.port * 31 + this.timeOut;
    }
}


