package com.smartfuel.service.components;

import android.app.Service;
import com.smartfuel.service.sqlite.dao.IConfiguration;
import com.smartfuel.service.repository.IConfigurationRepository;
import com.smartfuel.service.repository.OptDatabase;
import com.smartfuel.service.OPTService;
import com.smartfuel.service.modules.KioskModule;
import com.smartfuel.service.modules.NetworkModule;
import com.smartfuel.service.modules.OptModule;
import dagger.Component;
import javax.inject.Singleton;

@Component(dependencies = {}, modules = {KioskModule.class, OptModule.class, NetworkModule.class})
@Singleton
public interface IKioskComponent {
    IConfiguration configurationDao();

    IConfigurationRepository configurationRepository();

    void inject(OPTService<Service> paramOPTService);

    OptDatabase optDatabase();

    Service service();
}
