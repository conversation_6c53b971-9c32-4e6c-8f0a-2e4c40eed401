package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

/*
01 004 0123 03 2000 05 Hold_Dispenser
 01 = client node number
 004 = request number
 0123 = sequence number
 03 = number of parameters
 2000 = socket success code (2) + PCC success code (000)
 05 = Dispenser number
 */
public class HoldDispenserResponse extends BaseResponse{
    private int dispenserNumber;
    public HoldDispenserResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }
}
