package com.smartfuel.service.repository;

import javax.inject.Inject;
import com.smartfuel.service.sqlite.dao.IForecourtControllerTransaction;
import com.smartfuel.service.sqlite.models.ForecourtControllerTransaction;

public class ForeceourtControllerTransactionDataSource implements IForecourtControllerTransactionRepository {
    private final IForecourtControllerTransaction iForecourtControllerTransaction;

    @Inject
    public ForeceourtControllerTransactionDataSource(IForecourtControllerTransaction forecourtControllerTransaction) {
        this.iForecourtControllerTransaction = forecourtControllerTransaction;
    }

    public void clearForeceourtControllerTransaction(int posId, int fuelpointId, String fuelGradeId) {
        this.iForecourtControllerTransaction.deleteForeceourtControllerTransaction(posId, fuelpointId, fuelGradeId);
    }

    public void saveForeceourtControllerTransaction(ForecourtControllerTransaction paramForecourtControllerTransaction) {
        this.iForecourtControllerTransaction.upsertForeceourtControllerTransaction(paramForecourtControllerTransaction);
    }

    @Override
    public void clearForecourtTransaction(int posId, int fuelpointId, String fuelgradeId) {
        this.iForecourtControllerTransaction.deleteForeceourtControllerTransaction(posId, fuelpointId, fuelgradeId);
    }

    @Override
    public void saveForecourtTransaction(ForecourtControllerTransaction forecourtControllerTransaction) {
        this.iForecourtControllerTransaction.upsertForeceourtControllerTransaction(forecourtControllerTransaction);
    }
}