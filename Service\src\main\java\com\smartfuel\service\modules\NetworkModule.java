package com.smartfuel.service.modules;

import com.smartfuel.service.IKioskApiService;
import com.smartfuel.service.OPTService;
import com.smartfuel.service.network.TCPMessenger;
import com.smartfuel.service.repository.IConfigurationRepository;
import com.smartfuel.service.sqlite.models.Configuration;

import java.io.IOException;

import dagger.Module;
import dagger.Provides;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import retrofit2.Converter;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

@Module
public class NetworkModule {
    private OPTService myOptService;
    private String myKioskAPI;

    public NetworkModule(OPTService paramOPTService, String kioskAPI) {
        this.myOptService = paramOPTService;
        this.myKioskAPI = kioskAPI;
    }

    @Provides
    TCPMessenger provideForecourtControllerService() {
        return TCPMessenger.getDefaultInstance();
    }

    @Provides
    IKioskApiService provideKioskApiService() {

        OkHttpClient.Builder httpClient = new OkHttpClient.Builder();
        httpClient.addInterceptor(chain -> {
            Request original = chain.request();

            if(myOptService.configurationRepository != null) {
                Configuration config = myOptService.configurationRepository.getConfigurationByName("kiosk_bearer_token");
                if(config != null) {
                    String token = config.getValue();
                    if (token != null && !token.isEmpty()) {
                        Request request = original.newBuilder()
                                .header("Authorization", token)
                                .method(original.method(), original.body())
                                .build();

                        return chain.proceed(request);
                    }
                }
            }
            return chain.proceed(original);
        });

        return (IKioskApiService)(new Retrofit
                .Builder())
                .baseUrl(myKioskAPI)
                .addConverterFactory((Converter.Factory)GsonConverterFactory.create())
                .client(httpClient.build())
                .build()
                .create(IKioskApiService.class);
    }
}

