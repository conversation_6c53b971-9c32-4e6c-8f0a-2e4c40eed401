package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public class HoseGradeTankAssociationResponse extends BaseResponse{
    private final String dispenserNumber;
    private final int hoseNumber;
    private final String gradeNumber;
    public HoseGradeTankAssociationResponse(String response) {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = super.responseData[5];
        this.hoseNumber = Integer.parseInt(super.responseData[6]);
        this.gradeNumber = super.responseData[7];
    }

    public String getDispenserNumber() {
        return dispenserNumber;
    }

    public int getHoseNumber() {
        return hoseNumber;
    }

    public String getGradeNumber() {
        return gradeNumber;
    }
}
