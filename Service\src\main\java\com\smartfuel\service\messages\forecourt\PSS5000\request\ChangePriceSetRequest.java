package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;

import org.json.JSONException;

import java.util.ArrayList;

public class ChangePriceSetRequest {
    private final FcChangePriceSetRequestData data;

    private final String name = "change_FcPriceSet_req";

    private final String subCode = "02H";

    public ChangePriceSetRequest(String priceSetId, ArrayList<String> priceGroupIds, ArrayList<String> fuelGradeIds, ArrayList<String> priceGroups, String priceActivationDateTime) {
        this.data = new FcChangePriceSetRequestData(priceSetId, priceGroupIds, fuelGradeIds, priceGroups, priceActivationDateTime);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class FcChangePriceSetRequestData {
        private final ArrayList<String> FcGradeId;

        private final ArrayList<String> FcPriceGroupId;

        private final ArrayList<ArrayList<String>> FcPriceGroups;

        private final String FcPriceSetId;

        private final String PriceSetActivationDateAndTime;

        FcChangePriceSetRequestData(String priceSetId, ArrayList<String> priceGroupIds, ArrayList<String> fuelGradeIds, ArrayList<String> priceGroups, String priceActivationDateTime) {
            this.FcPriceSetId = priceSetId;
            this.FcPriceGroupId = priceGroupIds;
            this.FcGradeId = fuelGradeIds;
            ArrayList<ArrayList<String>> jsonArray = new ArrayList<ArrayList<String>>(1);
            jsonArray.add(priceGroups);
            this.FcPriceGroups = jsonArray;
            this.PriceSetActivationDateAndTime = priceActivationDateTime;
        }
    }
}
