package com.smartfuel.service.modules;

import android.app.Service;
import android.content.Context;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.smartfuel.service.repository.IRewardCardDiscountRepository;
import com.smartfuel.service.repository.RewardCardDiscountDataSource;
import com.smartfuel.service.sqlite.dao.IConfiguration;
import com.smartfuel.service.sqlite.dao.IFuelTransaction;
import com.smartfuel.service.sqlite.dao.IForecourtControllerTransaction;
import com.smartfuel.service.sqlite.dao.IRewardCardDiscount;
import com.smartfuel.service.sqlite.dao.ITransaction;
import com.smartfuel.service.repository.ConfigurationDataSource;
import com.smartfuel.service.repository.IConfigurationRepository;
import com.smartfuel.service.repository.IForecourtControllerTransactionRepository;
import com.smartfuel.service.repository.ForeceourtControllerTransactionDataSource;
import com.smartfuel.service.repository.ITransactionRepository;
import com.smartfuel.service.repository.OptDatabase;
import com.smartfuel.service.repository.TransactionDataSource;
import dagger.Module;
import dagger.Provides;
import javax.inject.Singleton;

@Module
public class OptModule {
    private RoomDatabase.Callback CALLBACK = new RoomDatabase.Callback() {
        public void onCreate(SupportSQLiteDatabase param1SupportSQLiteDatabase) {
            super.onCreate(param1SupportSQLiteDatabase);
            param1SupportSQLiteDatabase.execSQL("CREATE TRIGGER SetTerminalTransactionId \n   AFTER INSERT\n   ON `transaction`\nBEGIN\n UPDATE `transaction`\nSET TerminalTransactionId = substr(TerminalId||'00'||ID,1)\nWHERE\n   id = new.ID;\nEND");
        }

        public void onOpen(SupportSQLiteDatabase param1SupportSQLiteDatabase) {
            super.onOpen(param1SupportSQLiteDatabase);
        }
    };

    private OptDatabase optDatabase;

    public OptModule(Service paramService) {
        this.optDatabase = Room.databaseBuilder((Context)paramService, OptDatabase.class, "opt-db").addMigrations(OptDatabase.MIGRATION_1_2).addCallback(this.CALLBACK).build();
    }

    @Provides
    @Singleton
    IConfigurationRepository configurationRepository(IConfiguration paramIConfigurationDao) {
        return new ConfigurationDataSource(paramIConfigurationDao);
    }

    @Provides
    @Singleton
    IConfiguration providesConfigurationDao(OptDatabase paramOptDatabase) {
        return paramOptDatabase.getConfigurationDao();
    }

    @Provides
    @Singleton
    IFuelTransaction providesFuelTransactionDao(OptDatabase paramOptDatabase) {
        return paramOptDatabase.getFuelTransactionDao();
    }

    @Provides
    @Singleton
    OptDatabase providesOptDatabase() {
        return this.optDatabase;
    }

    @Provides
    @Singleton
    IForecourtControllerTransaction providesForeceourtControllerTransaction(OptDatabase paramOptDatabase) {
        return paramOptDatabase.getForeceourtControllerTransactionDao();
    }

    @Provides
    @Singleton
    ITransaction providesTransactionDao(OptDatabase paramOptDatabase) {
        return paramOptDatabase.getTransactionDao();
    }

    @Provides
    @Singleton
    IRewardCardDiscount providesRewardCardDiscount(OptDatabase paramOptDatabase) {
        return paramOptDatabase.getRewardCardDiscountDao();
    }

    @Provides
    @Singleton
    IForecourtControllerTransactionRepository ForeceourtControllerTransactionRespository(IForecourtControllerTransaction IForeceourtControllerTransactionDao) {
        return (IForecourtControllerTransactionRepository) new ForeceourtControllerTransactionDataSource(IForeceourtControllerTransactionDao);
    }

    @Provides
    @Singleton
    ITransactionRepository transactionRepository(ITransaction paramITransactionDao, IFuelTransaction paramIFuelTransactionDao) {
        return new TransactionDataSource(paramITransactionDao, paramIFuelTransactionDao);
    }

    @Provides
    @Singleton
    IRewardCardDiscountRepository rewardCardDiscountRepository(IRewardCardDiscount paramIRewardCardDiscountDao) {
        return new RewardCardDiscountDataSource(paramIRewardCardDiscountDao);
    }
}

