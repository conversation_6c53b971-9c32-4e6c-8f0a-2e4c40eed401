package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public class TankDataResponse extends BaseResponse{
    private int tankNumber;
    private int gradeNumber;
    private long grossVolume;
    private long netVolume;
    private long productLevel;
    private long waterLevel;
    private int temprature;
    private long tankCapacity;
    private long tankUllage;
    private long startOfDayVolume;
    private long deliveries;
    private long dispenserSales;
    private long operatingVariance;
    private int flag1Status;
    private int flag2Status;
    private int tankAlarmStatus;
    private int tankCalibrationStatus;
    private String lastCalibrationDate;

    public TankDataResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.tankNumber = Integer.parseInt(super.responseData[5]);
        this.gradeNumber = Integer.parseInt(super.responseData[6]);
        this.grossVolume = Long.parseLong(super.responseData[7]);
        this.netVolume = Long.parseLong(super.responseData[8]);
        this.productLevel = Long.parseLong(super.responseData[9]);
        this.waterLevel = Long.parseLong(super.responseData[10]);
        this.temprature =  Integer.parseInt(super.responseData[11]);
        this.tankCapacity = Long.parseLong(super.responseData[12]);
        this.tankUllage = Long.parseLong(super.responseData[13]);
        this.startOfDayVolume = Long.parseLong(super.responseData[14]);
        this.deliveries = Long.parseLong(super.responseData[15]);
        this.dispenserSales = Long.parseLong(super.responseData[16]);
        this.operatingVariance = Long.parseLong(super.responseData[17]);
        this.flag1Status =  Integer.parseInt(super.responseData[18]);
        this.flag2Status =  Integer.parseInt(super.responseData[19]);
        this.tankAlarmStatus =  Integer.parseInt(super.responseData[20]);
        this.tankCalibrationStatus =  Integer.parseInt(super.responseData[21]);
        this.lastCalibrationDate =  super.responseData[22];
    }

    public int getTankNumber() {
        return tankNumber;
    }

    public int getGradeNumber() {
        return gradeNumber;
    }

    public long getGrossVolume() { return  grossVolume; }

    public long getNetVolume() { return netVolume; }

    public long getProductLevel() { return productLevel; }

    public long getWaterLevel() { return waterLevel; }

    public int getTemprature() { return  temprature; }

    public long getTankCapacity() { return tankCapacity; }

    public long getTankUllage() { return tankUllage; }

    public long getStartOfDayVolume() { return startOfDayVolume; }

    public long getDeliveries() { return deliveries; }

    public long getDispenserSales() { return  dispenserSales; }

    public long getOperatingVariance() { return operatingVariance; }

    public int getFlag1Status() { return flag1Status; }

    public int getFlag2Status() { return flag2Status; }

    public int getTankAlarmStatus() { return tankAlarmStatus; }

    public int getTankCalibrationStatus() { return tankCalibrationStatus; }

    public String getLastCalibrationDate() { return lastCalibrationDate; }
}
