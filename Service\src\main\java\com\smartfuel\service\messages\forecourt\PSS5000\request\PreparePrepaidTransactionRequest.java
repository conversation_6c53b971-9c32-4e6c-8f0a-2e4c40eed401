package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;
import org.json.JSONException;
public class PreparePrepaidTransactionRequest {
    private final PreparePrepaidTransactionRequestData data;

    private final String name = "prepare_PrepaidTrans_req";

    private final String subCode = "00H";

    public PreparePrepaidTransactionRequest(String posId, String fuelpointId, String gradeId, String prepaidAmount) {
        this.data = new PreparePrepaidTransactionRequestData(posId, fuelpointId, gradeId, prepaidAmount);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class PreparePrepaidTransactionRequestData {
        private final String FpId;

        private final String PosId;

        private final String PrepaidMoney;

        private final String PresetGradeId;

        PreparePrepaidTransactionRequestData(String posId, String fuelpointId, String presetGradeId, String prepaidMoney) {
            this.PosId = posId;
            this.FpId = fuelpointId;
            this.PresetGradeId = presetGradeId;
            this.PrepaidMoney = prepaidMoney;
        }
    }
}
