package com.smartfuel.service.sqlite.dao;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.smartfuel.service.models.posmaster.FuelTransaction;
import com.smartfuel.service.models.posmaster.Order;
import com.smartfuel.service.models.posmaster.OrderItem;
import com.smartfuel.service.models.transaction.CardReceipt;
import com.smartfuel.service.models.transaction.WhiteCardReceipt;

import com.smartfuel.service.sqlite.models.Transaction;

import java.util.Date;
import java.util.List;

@Dao
public interface ITransaction {
    @Query("Select t.* from `Transaction` t inner join FuelTransaction ft on t.FuelTransactionId = ft.Id " +
            "where t.TerminalId = :terminalId and t.Status = :transactionStatus and ft.PumpNumber=:pumpNumber " +
            "order by t.Time desc LIMIT 1;")
    Transaction getCurrentTransaction(int terminalId, int transactionStatus, int pumpNumber);

    @Query("SELECT " +
            "terminalTransactionId" +
            ", strftime('%Y-%m-%dT%H:%M:%f',datetime([time]/1000, 'unixepoch', 'localtime')) as transactionDate " +
            ", PRINTF('%.2f',(finalAmount)/100.00) AS amount " +
            ",0 AS discount " +
            ", TerminalId AS terminalId " +
            ",'PAID' AS paymentType " +
            "FROM `Transaction`"+
            "WHERE " +
            "Exported = 0 " +
            "AND Status = 5 " +
            "AND TerminalId = :posId")
    List<Order> getPosMasterOrders(String posId);

    @Query("SELECT " +
            "t.TerminalTransactionId as TerminalTransactionId," +
            "'FG-' || ft.GradeID AS Barcode, " +
            "'' AS ProductName, " +
            "0 AS Cost, " +
            "ft.price / 1000.000 as Price, " +
            "ft.volume / 100.00 AS Quantity, " +
            "1 AS Taxable, " +
            "ft.gradeId AS FGID, " +
            "PRINTF(\"%d%04d\",t.TerminalId,ft.id) AS FTID " +
            "FROM " +
            "`Transaction` t " +
            "INNER JOIN " +
            "FuelTransaction ft " +
            "ON " +
            "ft.id = t.fuelTransactionId " +
            "WHERE " +
            "t.TerminalTransactionId IN (:orderId)")
    List<OrderItem> getPosMasterOrderItems(List<String> orderId);

    @Query("SELECT " +
            "PRINTF(\"%d%04d\",t.TerminalId,ft.ID) as Id, " +
            "ft.pumpNumber AS FuelPoint, " +
            "ft.GradeID AS FuelGrade, " +
            "ft.volume / 100.0 AS Quantity, " +
            "ft.price / 10.0 as Price, " +
            "PRINTF(\"%.2f\",(FinalAmount)/100.00) AS Amount, " +
            "strftime('%Y-%m-%dT%H:%M:%f',datetime([time]/1000, 'unixepoch', 'localtime')) AS [TransactionDate], " +
            "1 AS Processed " +
            "FROM " +
            "`Transaction` t " +
            "INNER JOIN " +
            "FuelTransaction ft " +
            "ON " +
            "ft.id = t.fuelTransactionId " +
            "WHERE " +
            "t.TerminalTransactionId IN (:orderId)")
    List<FuelTransaction> getPosMasterFuelTransactions(List<String> orderId);

    @Query("SELECT " +
            "t.terminalTransactionId AS OrderId, " +
            "strftime('%Y-%m-%dT%H:%M:%f',datetime(t.[time]/1000, 'unixepoch', 'localtime')) AS TransactionDate, " +
            "CASE " +
            "   WHEN (whitecardUserId is not NULL or type = 1) " +
            "       THEN 'ACCOUNT' " +
            "   WHEN (cardSignature IS NOT NULL AND cardSignature <> '') " +
            "       THEN 'CREDIT CARD' " +
            "   ELSE 'MANUAL TRANSACTION' " +
            "END PaymentStatus, " +
            "PRINTF('%.2f',(t.finalAmount)/100.00) AS Amount, " +
            "1 AS Processed, " +
            "coalesce(nullif(CardSignature,'null'),ft.whitecardUsername) as CardSignature, " +
            "AccountType, " +
            "coalesce(t.STAN,t.FuelTransactionID) as STAN, " +
            "t.Processor as Processor, " +
            "t.Type as Type " +
            "FROM " +
            "`Transaction` t " +
            " INNER JOIN FuelTransaction ft on t.fuelTransactionId = ft.id " +
            "WHERE " +
            "TerminalTransactionId IN (:orderId)")
    List<com.smartfuel.service.models.posmaster.Transaction> getPosMasterTransactions(List<String> orderId);

    @Query("Select * from `Transaction` where KioskPublished = 0 and status = 5")
    List<Transaction> getPublishableTransactions();

    @Query("Select * from `Transaction` where id = :transactionId")
    Transaction getTransactionById(long transactionId);

    @Query("Select * " +
            "from `Transaction` " +
            "where status in (:statusIds) " +
            "and time < :timeout " +
            "and processor = 'IX'")
    List<Transaction> getTransactionsByStatusAndTimeOut(long timeout, int ...statusIds);

    @Query("Select t.* " +
            "from `Transaction` t " +
            "join FuelTransaction ft " +
            "on t.fuelTransactionId = ft.id " +
            "where ft.pumpNumber = :fuelPoint " +
            "and t.status in (:statusIds) " +
            "and t.time > :timeout " +
            "Order by t.Id desc limit 1")
    Transaction getTransactionByFuelPointStatusAndTimeout(int fuelPoint, long timeout, int ...statusIds );

    @Query("select " +
            "c_storename.Value as [StoreName] " +
            ", c_storeaddress.Value as [StoreAddress1] " +
            ", '' as [StoreAddress2] " +
            ", c_storecity.Value as [StoreCity] " +
            ", c_storestate.Value as [StoreState]" +
            ", c_storepostcode.Value as [StorePostalCode]" +
            ", c_storeabn.Value as [StoreABN]" +
            ", c_kioskname.Value as [KioskName]" +
            ", t.TerminalTransactionId as [TransactionNumberDetail]" +
            ", strftime('%Y/%m/%d %H:%M:%S',datetime(t.time/1000, 'unixepoch', 'localtime')) as [TransactionDateTime]" +
            ", PRINTF('$%.2f',(t.FinalAmount)/100.00) as  [TransactionTotalAmount]" +
            ", PRINTF('$%.2f',(((t.FinalAmount)/100.00)/11)) as [TransactionGSTAmount]" +
            ", t.Status as [TransactionStatus]" +
            ", t.hostResponse as [HostResponse]" +
            ", t.responseCode as [HostResponseCode]" +
            ", t.Type as [TransactionType]" +
            ", 'AUD' as [TransactionCurrency]" +
            ", ft.TransSeqNumber as [FuelTransactionNumber]" +
            ", c_grade.[value] as [FuelGradeName]" +
            ", printf('%.2fL',(ft.Volume/100.00)) as [FuelVolume]" +
            ", PRINTF('$%.2f',(ft.Money/100.00)) as [FuelValue]" +
            ", ft.PumpNumber as [FuelPumpNumber]" +
            ", PRINTF('%.1fc/L',(ft.Price/10)) as [FuelPricePerLitre]" +
            ", ft.WhitecardAccountName as [AccountName]\n" +
            ", ft.WhitecardUsername as [AccountUser]\n" +
            ", ft.WhitecardAccountEmail as [AccountEmailAddress]\n" +
            ", ft.VehicleRegistration as [VehicleRegistration]\n" +
            ", ft.VehicleOdometer as [VehicleOdometer]\n" +
            ", 'Thank You!' as [InvoiceFooter]" +
            "from `Transaction` t " +
            "left join FuelTransaction ft on t.FuelTransactionID = ft.id" +
            " join Configuration c_grade on ('FG_' || ft.GradeID) = c_grade.name" +
            " join Configuration c_storename on c_storename.name = 'store_name'" +
            "left join Configuration c_storeaddress on c_storeaddress.name = 'store_address_1'" +
            "left join Configuration c_storecity on c_storecity.name = 'store_city'" +
            "left join Configuration c_storestate on c_storestate.name = 'store_state'" +
            "left join Configuration c_storepostcode on c_storepostcode.name = 'store_postcode'" +
            "left join Configuration c_storeabn on c_storeabn.name = 'store_abn'" +
            "left join Configuration c_kioskname on c_kioskname.name = 'kiosk_name'" +
            "where t.Status = 5 and ft.WhitecardUserId = :whitecardUserId Order by t.Id desc limit 1")
    List<WhiteCardReceipt> getWhiteCardReceiptData(String whitecardUserId);

    @Query("select " +
            " t.TerminalTransactionId as [TerminalTransactionId]" +
            ", strftime('%d/%m/%Y %H:%M:%S',datetime(t.time/1000, 'unixepoch', 'localtime')) as [TransactionDateTime]" +
            ", CASE " +
            "WHEN t.Type = 0 then 'PURCHASE' " +
            "WHEN t.Type = 1 then 'ACCOUNT' " +
            "WHEN t.Type = 2 then 'REFUND' " +
            "WHEN t.Type = 3 then 'CANCELLED' " +
            "END as [TransactionType]" +
            ", t.Status as [TransactionStatus]" +
            ", CASE " +
            "WHEN t.Status in (5,7,25,26) OR (t.Status = 4 AND ft.Volume <> 0)" +
            "   THEN PRINTF('$%.2f',(ft.moneyDue)/100.00) " +
            "ELSE PRINTF('$%.2f',(t.authorisedAmount)/100.00) " +
            "END as  [TransactionPartialAmount]" +
            ", CASE " +
            "WHEN t.Status in (5,7,25,26) OR (t.Status = 4 AND ft.Volume <> 0)" +
            "   THEN PRINTF('$%.2f',(t.FinalAmount)/100.00) " +
            "ELSE PRINTF('$%.2f',(t.authorisedAmount)/100.00) " +
            "END as  [TransactionFinalAmount]" +
            ", CASE " +
            "WHEN t.Status in (5,7,25,26) OR (t.Status = 4 AND ft.Volume <> 0)" +
            "   THEN PRINTF('$%.2f',(((t.FinalAmount)/100.00)/11)) " +
            "ELSE PRINTF('$%.2f',(((t.authorisedAmount)/100.00)/11)) " +
            "END as [TransactionGSTAmount]" +
            ", CASE " +
            "WHEN rd.totalDiscountApplied is not null" +
            "   THEN PRINTF('-$%.2f',rd.totalDiscountApplied/100.00) " +
            "ELSE '' " +
            "END as [RewardTotalDiscount]" +
            ", CASE " +
            "WHEN rd.discount is not null" +
            "   THEN PRINTF('%.1fc/L',rd.discount) " +
            "ELSE '' " +
            "END as [RewardDiscountPerLiter]" +
            ", 'AUD' as [TransactionCurrency]" +
            ", t.aid as [ApplicationId]" +
            ", t.applicationLabel as [ApplicationLabel]" +
            ", t.cardSignature as [CardSignature]" +
            ", t.atc as [CardATC]" +
            ", t.tid as [TID]" +
            ", t.stan as [STAN]" +
            ", CASE " +
            "WHEN t.Status in (2,3,4) THEN 'Authorized'" +
            "WHEN t.Status = 5 THEN 'Captured'" +
            "WHEN t.Status = 7 THEN 'Reversed'" +
            "WHEN t.Status = 8 THEN 'User cancelled'" +
            "ELSE t.hostResponse " +
            "END as [HostResponse]" +
            ", COALESCE(t.responseCode, '-1') as [HostResponseCode]" +
            ", t.authorisationId as [AuthorisationId]" +
            ", t.panSeqNumber as [PanSeqNo]" +
            ", t.cvm as [CVM]" +
            ", c_storename.Value as [StoreName] " +
            ", c_storeabn.Value as [StoreABN]" +
            ", c_storeaddress.Value as [StoreAddress1] " +
            ", '' as [StoreAddress2] " +
            ", c_storecity.Value as [StoreCity] " +
            ", c_storepostcode.Value as [StorePostalCode]" +
            ", c_storestate.Value as [StoreState]" +
            ", c_kioskname.Value as [KioskName]" +
            ", COALESCE(c_grade.[value], 'none') as [FuelGradeName]" +
            ", PRINTF('%.1fc/L',(ft.Price)) as [FuelPricePerLitre]" +
            ", ft.PumpNumber as [FuelPumpNumber]" +
            ", COALESCE(ft.TransSeqNumber, 0) as [FuelTransactionNumber]" +
            ", PRINTF('$%.2f',(ft.Money/100.00)) as [FuelValue]" +
            ", printf('%.2fL',(ft.Volume/100.00)) as [FuelVolume]" +
            ", COALESCE(ft.VehicleRegistration,'') as [VehicleRegistration]" +
            ", COALESCE(ft.VehicleOdometer,'') as [VehicleOdometer]" +
            ", 'Thank You!' as [InvoiceFooter]" +
            "from `Transaction` t " +
            " left join FuelTransaction ft on t.FuelTransactionID = ft.id" +
            " left join RewardCardDiscount rd on t.ID = rd.transactionId and rd.totalDiscountApplied != 0" +
            " left join Configuration c_grade on ('FG_' || ft.GradeID) = c_grade.name" +
            " join Configuration c_storename on c_storename.name = 'store_name'" +
            " left join Configuration c_storeaddress on c_storeaddress.name = 'store_address_1'" +
            " left join Configuration c_storecity on c_storecity.name = 'store_city'" +
            " left join Configuration c_storestate on c_storestate.name = 'store_state'" +
            " left join Configuration c_storepostcode on c_storepostcode.name = 'store_postcode'" +
            " left join Configuration c_storeabn on c_storeabn.name = 'store_abn'" +
            " left join Configuration c_kioskname on c_kioskname.name = 'kiosk_name'" +
            " where t.TerminalTransactionId in (:terminalTransactionId) Order by t.Id desc")
    List<CardReceipt> getCardReceiptData(List<Integer> terminalTransactionId);

    @Query("SELECT t.TerminalTransactionId " +
            "from `Transaction` t " +
            "where t.cardSignature = :cardSignature " +
            "and status in (:statusIds) " +
            "and time > :timeFrom " +
            "Order by t.Id desc")
    List<Integer> getCardReceiptTransactions(String cardSignature, long timeFrom, int ...statusIds);

    @Query("UPDATE `transaction` set Exported = 1 where terminalTransactionId in (:terminalTransactionIds)")
    void updateTransactionExportStatus(List<String> terminalTransactionIds);
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long upsertTransaction(Transaction paramTransaction);
}

