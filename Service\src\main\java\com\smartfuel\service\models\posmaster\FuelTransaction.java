package com.smartfuel.service.models.posmaster;

public class FuelTransaction {
    public String getId() {
        return Id;
    }

    public void setId(String id) {
        Id = id;
    }

    public String getFuelPoint() {
        return FuelPoint;
    }

    public void setFuelPoint(String fuelPoint) {
        FuelPoint = fuelPoint;
    }

    public String getFuelGrade() {
        return FuelGrade;
    }

    public void setFuelGrade(String fuelGrade) {
        FuelGrade = fuelGrade;
    }

    public String getQuantity() {
        return Quantity;
    }

    public void setQuantity(String quantity) {
        Quantity = quantity;
    }

    public String getPrice() {
        return Price;
    }

    public void setPrice(String price) {
        Price = price;
    }

    public String getAmount() {
        return Amount;
    }

    public void setAmount(String amount) {
        Amount = amount;
    }

    public String getTransactionDate() {
        return TransactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        TransactionDate = transactionDate;
    }

    public String getProcessed() {
        return Processed;
    }

    public void setProcessed(String processed) {
        Processed = processed;
    }

    @Override
    public String toString(){
        return Id + "," + FuelPoint + "," + FuelGrade + "," + Quantity + "," + Price + "," + Amount + "," + TransactionDate + "," + Processed;
    }

    private String Id;
    private String FuelPoint;
    private String FuelGrade;
    private String Quantity;
    private String Price;
    private String Amount;
    private String TransactionDate;
    private String Processed;
}

