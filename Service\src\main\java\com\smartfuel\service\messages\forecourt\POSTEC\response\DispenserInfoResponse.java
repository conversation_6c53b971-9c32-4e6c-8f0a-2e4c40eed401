package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public class DispenserInfoResponse extends BaseResponse {
    private int dispenserNumber;
    private int hoseNumber;
    private int dispenserType;
    private int numHoses;

    public DispenserInfoResponse(String response) throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
        this.hoseNumber = Integer.parseInt(super.responseData[6]);
        this.dispenserType = Integer.parseInt(super.responseData[7]);
        this.numHoses = Integer.parseInt(super.responseData[8]);
    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }

    public int getHoseNumber() {
        return hoseNumber;
    }

    public int getDispenserType() {
        return dispenserType;
    }

    public int getNumHoses() {
        return numHoses;
    }
}
