package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;

import org.json.JSONException;

public class PosConnectionStatusRequest {
    private final String name = "PosConnectionStatus_req";

    private final String subCode = "00H";

    private final PosConnectionStatusRequestData data;

    public PosConnectionStatusRequest() {
        data = new PosConnectionStatusRequestData();
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class PosConnectionStatusRequestData{

    }
}
