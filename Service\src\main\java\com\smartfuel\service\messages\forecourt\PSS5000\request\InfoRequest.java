package com.smartfuel.service.messages.forecourt.PSS5000.request;
import com.google.gson.GsonBuilder;
import java.util.ArrayList;
import org.json.JSONException;

public class InfoRequest {
    private final InfoRequestData data;

    private final String name = "FpInfo_req";

    private final String subCode = "00H";

    public InfoRequest(String paramString, ArrayList<String> paramArrayList) {
        this.data = new InfoRequestData(paramString, paramArrayList);
    }

    public String GetJson() throws JSONException {
        return (new GsonBuilder()).create().toJson(this);
    }

    public class InfoRequestData {
        private final String FpId;

        private final ArrayList<String> FpInfoItemId;

        InfoRequestData(String param1String, ArrayList<String> param1ArrayList) {
            this.FpId = param1String;
            this.FpInfoItemId = param1ArrayList;
        }
    }
}
