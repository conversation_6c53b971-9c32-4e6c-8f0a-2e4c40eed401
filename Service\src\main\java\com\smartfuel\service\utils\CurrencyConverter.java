package com.smartfuel.service.utils;

import java.text.NumberFormat;
import java.util.Locale;

public class CurrencyConverter {

    private static Locale locale = Locale.UK;

    public static void setCurrency(String currencyCode){
        locale = getLocale(Integer.parseInt(currencyCode));
    }

    private static Locale getLocale(int numCode) {
        try {
            for (Locale locale : NumberFormat.getAvailableLocales()) {
                int code = NumberFormat.getCurrencyInstance(locale).getCurrency().getNumericCode();
                if (numCode == code) {
                    return locale;
                }
            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
        return locale;
    }

    public static NumberFormat getFormatter() {
        NumberFormat formatter;
        formatter = NumberFormat.getCurrencyInstance(locale);

        return formatter;
    }
}
