package com.smartfuel.service.models.kiosk.request;

import com.smartfuel.service.IKioskApiService;
import com.smartfuel.service.logger.Log;
import com.smartfuel.service.repository.IConfigurationRepository;
import com.smartfuel.service.sqlite.models.FuelTransaction;
import com.smartfuel.service.sqlite.models.Transaction;
import com.smartfuel.service.utils.ApiServiceHelper;

import java.text.SimpleDateFormat;
import java.util.Date;

public class TransactionAuditRequest {

    private String transactionCreatedDate;
    private long kioskTransactionNumber;
    private String cardSignature;
    private String cardExpiryDate;
    private String processor;
    private int transactionStatus;
    private float transactionAmount;
    private Float transactionFinalAmount;
    private String transactionResponseCode;
    private String transactionResponse;
    private String vehicleRegistration;
    private String vehicleOdometer;
    private String pumpNumber;
    private String fuelGrade;
    private String fuelGradeName;
    public String authorizationID;
    public String tid;
    public int stan;

    private static final Object sync = new Object();

    private TransactionAuditRequest(Transaction transaction, FuelTransaction fuelTransaction, IConfigurationRepository configurationRepository){
        Date date = new Date(transaction.getTime());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

        transactionCreatedDate = simpleDateFormat.format(date);
        kioskTransactionNumber = transaction.getId();
        cardSignature = transaction.getCardSignature();
        cardExpiryDate = transaction.getCardExpiryDate();
        processor = transaction.getProcessor();
        transactionStatus = transaction.getStatus();
        transactionAmount = transaction.getAuthorisedAmount() / 100.0f;
        if(transaction.getFinalAmount() != 0)
            transactionFinalAmount = transaction.getFinalAmount() / 100.0f;
        transactionResponseCode = transaction.getResponseCode();
        transactionResponse = transaction.getHostResponse();
        vehicleRegistration = fuelTransaction.getVehicleRegistration();
        vehicleOdometer = fuelTransaction.getVehicleOdometer();
        pumpNumber = fuelTransaction.getPumpNumber() + "";
        fuelGrade = fuelTransaction.getGradeId();
        authorizationID = transaction.getAuthorisationId();
        stan = transaction.getStan();
        tid = transaction.getTid();
        if(fuelTransaction.getGradeId() != null && !fuelTransaction.getGradeId().isEmpty()) {
            StringBuilder strFuelGradeName = new StringBuilder();
            fuelGradeName = configurationRepository.getConfigurationByName(strFuelGradeName.append("FG_").append(String.format("%1$" + 2 + "s", fuelTransaction.getGradeId()).replace(' ', '0')).toString()).getValue();
        }
    }

    public static void send(IKioskApiService kioskApiService, Transaction transaction, FuelTransaction fuelTransaction, IConfigurationRepository configurationRepository){
        Thread thread = new Thread(() -> {
            synchronized (sync) {
                try {
                    TransactionAuditRequest request = new TransactionAuditRequest(transaction, fuelTransaction, configurationRepository);
                    ApiServiceHelper.executeAPI(kioskApiService.sendTransactionAudit(request));
                    Log.i("TransactionAuditRequest", String.format("Transaction audit %d has been sent", transaction.getId()));
                } catch (Exception ex) {
                    Log.e("TransactionAuditRequest", ex);
                }
            }
        });
        thread.start();
    }

    public String getTransactionCreatedDate() {
        return transactionCreatedDate;
    }

    public void setTransactionCreatedDate(String transactionCreatedDate) {
        this.transactionCreatedDate = transactionCreatedDate;
    }

    public long getKioskTransactionNumber() {
        return kioskTransactionNumber;
    }

    public void setKioskTransactionNumber(long kioskTransactionNumber) {
        this.kioskTransactionNumber = kioskTransactionNumber;
    }

    public String getCardSignature() {
        return cardSignature;
    }

    public void setCardSignature(String cardSignature) {
        this.cardSignature = cardSignature;
    }

    public String getCardExpiryDate() {
        return cardExpiryDate;
    }

    public void setCardExpiryDate(String cardExpiryDate) {
        this.cardExpiryDate = cardExpiryDate;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public int getTransactionStatus() {
        return transactionStatus;
    }

    public void setTransactionStatus(int transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public float getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(float transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public Float getTransactionFinalAmount() {
        return transactionFinalAmount;
    }

    public void setTransactionFinalAmount(Float transactionFinalAmount) {
        this.transactionFinalAmount = transactionFinalAmount;
    }

    public String getTransactionResponseCode() {
        return transactionResponseCode;
    }

    public void setTransactionResponseCode(String transactionResponseCode) {
        this.transactionResponseCode = transactionResponseCode;
    }

    public String getTransactionResponse() {
        return transactionResponse;
    }

    public void setTransactionResponse(String transactionResponse) {
        this.transactionResponse = transactionResponse;
    }

    public String getVehicleRegistration() {
        return vehicleRegistration;
    }

    public void setVehicleRegistration(String vehicleRegistration) {
        this.vehicleRegistration = vehicleRegistration;
    }

    public String getVehicleOdometer() {
        return vehicleOdometer;
    }

    public void setVehicleOdometer(String vehicleOdometer) {
        this.vehicleOdometer = vehicleOdometer;
    }

    public String getPumpNumber() {
        return pumpNumber;
    }

    public void setPumpNumber(String pumpNumber) {
        this.pumpNumber = pumpNumber;
    }

    public String getFuelGrade() {
        return fuelGrade;
    }

    public void setFuelGrade(String fuelGrade) {
        this.fuelGrade = fuelGrade;
    }

    public String getFuelGradeName() {
        return fuelGradeName;
    }

    public void setFuelGradeName(String fuelGradeName) {
        this.fuelGradeName = fuelGradeName;
    }

    public String getAuthorizationID() {
        return authorizationID;
    }

    public void setAuthorizationID(String authorizationID) {
        this.authorizationID = authorizationID;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public int getStan() {
        return stan;
    }

    public void setStan(int stan) {
        this.stan = stan;
    }
}
