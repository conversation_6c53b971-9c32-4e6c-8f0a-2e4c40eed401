package com.smartfuel.service.forecourtcontroller;

import com.smartfuel.service.logger.Log;
import com.smartfuel.service.messages.forecourt.PTS2.request.ConfigurationRequest;
import com.smartfuel.service.messages.forecourt.PTS2.request.PumpAuthorizeRequest;
import com.smartfuel.service.messages.forecourt.PTS2.request.PumpPriceChangeRequest;
import com.smartfuel.service.messages.forecourt.PTS2.request.PumpStatusRequest;
import com.smartfuel.service.messages.forecourt.PTS2.request.TransactionInformationRequest;
import com.smartfuel.service.messages.forecourt.PTS2.response.FuelGradesConfigurationResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpAuthorizeResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpConfigurationResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpNozzlesConfigurationResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpPriceChangeResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.PumpStatusResponse;
import com.smartfuel.service.messages.forecourt.PTS2.response.TransactionInformationResponse;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.FuelTransaction;
import com.smartfuel.service.models.forecourt.GradePrice;
import com.smartfuel.service.models.forecourt.GradePrices;
import com.smartfuel.service.network.Constant;
import com.smartfuel.service.network.SocketConfig;
import com.smartfuel.service.sqlite.models.ForecourtControllerTransaction;
import com.smartfuel.service.utils.ApiException;
import com.smartfuel.service.utils.ApiServiceHelper;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import retrofit2.Converter;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class PTS2Service extends ForecourtController implements IForecourtController {
    private String forecourtControllerIP;
    private final Callback callbackHandler;
    private String myOperationMode;
    private String myPosReference;
    private final SocketConfig socketConfig;
    private String encodedCredentials;
    private GradePrices configuredGradePrices;
    private static IPTS2ApiService pts2ApiService;

    private ArrayList<FuelPoint> configuredFuelPoints;

    ScheduledExecutorService scheduledGetPumpStatus = null;

    <T> PTS2Service(String ipAddress, int port, String operationMode, String posReference, Callback<T> callbackHandler) {
        if (this.configuredFuelPoints == null) // There are no configured fuelling points yet
        {
            this.configuredFuelPoints = new ArrayList<>();
        }

        this.forecourtControllerIP = "http://" + ipAddress + ":" + String.valueOf(port);
        this.callbackHandler = callbackHandler;

        this.myOperationMode = operationMode;
        if (this.myOperationMode.isEmpty())
            this.myOperationMode = "single"; // Single is the default operation mode if none is provided.

        this.myPosReference = posReference;
        if (this.myPosReference == null)
            this.myPosReference = "";

        socketConfig = new SocketConfig(port, Constant.DEFAULT_TIME_OUT);
        pts2ApiService = Pts2ApiSetup();
        ApiServiceHelper.setInstances(pts2ApiService);
    }

    private IPTS2ApiService Pts2ApiSetup() {
        OkHttpClient.Builder httpClient = new OkHttpClient.Builder();
        httpClient.addInterceptor(chain -> {
            Request original = chain.request();
            //TODO: Extend or Override the ForecourtLogic Login method to cater for username and password. create and store the base64 encoded string for use on future requests
            String token = "admin:admin";
            byte[] bytes = token.getBytes(StandardCharsets.UTF_8);
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                encodedCredentials = Base64.getEncoder().encodeToString(bytes);
            }
            if (token != null && !token.isEmpty()) {
                Request request = original.newBuilder()
                        .header("Authorization", "Basic " + encodedCredentials)
                        .method(original.method(), original.body())
                        .build();

                return chain.proceed(request);
            }


            return chain.proceed(original);
        });

        return (IPTS2ApiService) (new Retrofit
                .Builder())
                .baseUrl(forecourtControllerIP)
                .addConverterFactory((Converter.Factory) GsonConverterFactory.create())
                .client(httpClient.build())
                .build()
                .create(IPTS2ApiService.class);
    }

    private void setConfiguredFuelPoints(PumpConfigurationResponse.Pump pump, ArrayList<FuelGradesConfigurationResponse.FuelGrade> fuelGradeConfig, PumpNozzlesConfigurationResponse.PumpNozzle pumpNozzleConfig) throws ApiException {
        FuelPoint fuelPoint = new FuelPoint();
        PumpStatusResponse pumpStatus;

        pumpStatus = ApiServiceHelper.executeControllerAPI(pts2ApiService.getPumpStatus(new PumpStatusRequest(pump.getId())));
        fuelPoint.setId(String.format("%02d", pump.getId()));
        fuelPoint.setNozzleLifted(pumpStatus.Packets.get(0).getData().isNozzleLifted());
        switch (pumpStatus.Packets.get(0).getType()) {
            case "PumpIdleStatus":
                if(pumpStatus.Packets.get(0).getData().isNozzleLifted())
                    fuelPoint.setState("02H");
                else
                    fuelPoint.setState("05H");
                break;
            case "PumpFillingStatus":
                fuelPoint.setState("09H");
                break;
            default:
                fuelPoint.setState("");
                break;
        }

        GradePrices configuredPrices = new GradePrices();
        configuredPrices.setGradePrices(new ArrayList<GradePrice>());
        fuelPoint.setGradePrices(new ArrayList<GradePrice>());

        //ArrayList<GradePrice> gradePrices = new ArrayList<>();
        //This gets the available grade for the current fuel point
        pumpNozzleConfig.getFuelGradeIds().stream().filter(gradeId -> gradeId != 0).forEach(grade -> {
            // build the grades price item for this grade and add tot he array list for this fuel point
            FuelGradesConfigurationResponse.FuelGrade fg = fuelGradeConfig.stream().filter(fuelGrade -> fuelGrade.getId() == grade).findFirst().orElse(null);
            GradePrice gp = new GradePrice();
            gp.setId(String.format("%02d", fg.getId()));
            gp.setName(fg.getName());
            gp.setLabel("FG_" + String.format("%02d", fg.getId()));
            gp.setPrice((float) fg.getPrice());
            configuredPrices.getGradePrices().add(gp);
            fuelPoint.getGradePrices().add(gp);
            Log.i("setConfiguredFuelPoint", String.valueOf(grade));
        });

        FuelPoint currentFP = this.configuredFuelPoints.stream().filter(fuelPoint1 -> fuelPoint.getId().equals(fuelPoint1.getId())).findFirst().orElse(null);
        int i = this.configuredFuelPoints.indexOf(currentFP);
        if (currentFP == null) // fuelpoint not found in the configured array list - add
        {
            this.configuredFuelPoints.add(fuelPoint);
        } else // fuelpoint found in the configured array list - update
        {
            this.configuredFuelPoints.set(i, currentFP);
        }

    }

    private void setConfiguredGrades(ArrayList<FuelGradesConfigurationResponse.FuelGrade> fuelGradeConfig) {
        GradePrices configuredPrices = new GradePrices();
        configuredPrices.setGradePrices(new ArrayList<GradePrice>());
        fuelGradeConfig.forEach(fuelGrade -> {
            GradePrice gp = new GradePrice();
            gp.setId(String.format("%02d", fuelGrade.getId()));
            gp.setName(fuelGrade.getName());
            gp.setLabel("FG_" + String.format("%02d", fuelGrade.getId()));
            gp.setPrice((float) fuelGrade.getPrice());
            configuredPrices.getGradePrices().add(gp);
            Log.i("readConfig", fuelGrade.getName());
        });
        this.configuredGradePrices = configuredPrices;
    }

    private void readConfiguration() {

        PumpConfigurationResponse pumpConfig;
        FuelGradesConfigurationResponse fuelGradeConfig;
        PumpNozzlesConfigurationResponse pumpNozzleConfig;


        try {
            pumpConfig = ApiServiceHelper.executeControllerAPI(pts2ApiService.getPumpConfiguration(new ConfigurationRequest("GetPumpsConfiguration")));
            fuelGradeConfig = ApiServiceHelper.executeControllerAPI(pts2ApiService.getFuelGradesConfiguration(new ConfigurationRequest("GetFuelGradesConfiguration")));
            pumpNozzleConfig = ApiServiceHelper.executeControllerAPI(pts2ApiService.getPumpNozzleConfiguration(new ConfigurationRequest("GetPumpNozzlesConfiguration")));

            //foreach pump that is configured we need to get it's current status (idle, offline etc) and set it's current grades and prices
            pumpConfig.getPackets().forEach((pkt) -> pkt.getData().getPumps().forEach((pump -> {
                try {
                    setConfiguredFuelPoints(pump, fuelGradeConfig.Packets.get(0).getData().getFuelGrades(), pumpNozzleConfig.Packets.get(0).getData().getPumpNozzles().stream().filter(pumpNozzle -> pumpNozzle.getPumpId() == pump.getId()).findFirst().get());
                } catch (ApiException e) {
                    throw new RuntimeException(e);
                }
            })));
            // for all configured grades we need to set the grade and prices
            setConfiguredGrades(fuelGradeConfig.Packets.get(0).getData().getFuelGrades());

        } catch (Exception e) {
            Log.e("PTS2Controller", e);
        }


    }

    private void setupGetFuelPointStatus(){
        if(scheduledGetPumpStatus != null && !scheduledGetPumpStatus.isShutdown())
            scheduledGetPumpStatus.shutdown();

        scheduledGetPumpStatus = Executors.newSingleThreadScheduledExecutor();
        scheduledGetPumpStatus.scheduleAtFixedRate(() -> {
            try {
                for (FuelPoint fp : configuredFuelPoints) {
                    PumpStatusResponse pumpStatus = ApiServiceHelper.executeControllerAPI(pts2ApiService.getPumpStatus(new PumpStatusRequest(Integer.parseInt(fp.getId()))));
                    callbackHandler.FuellingStatusUpdate(pumpStatus);
                }
            } catch (ApiException|InterruptedException e) {
                Log.e("setupGetFuelPointStatus", "Failed", e);
            }

        }, 5, 5, TimeUnit.SECONDS);
    }

    private void fuelPointStatusMonitor(int fuelPointId, int transactionId) {
// on my own thread execute and monitor the PTS2 Controller for Transaction information

        Thread myTransactionMonitor = new Thread(new Runnable() {
            @Override
            public void run() {
                TransactionInformationRequest transactionInfoRequest = new TransactionInformationRequest(fuelPointId, transactionId);

                Log.i("fuelPointStatusMonitor:TransactionId", String.valueOf(transactionId));
                try {
                    boolean pollTransactionStatus = true;
                    TransactionInformationResponse transactionInfoResponse = ApiServiceHelper.executeControllerAPI(pts2ApiService.getTransactionInformation(transactionInfoRequest));
                    callbackHandler.FuellingStatusUpdate(transactionInfoResponse);

                    switch (transactionInfoResponse.Packets.get(0).getData().getState()) {
                        case "EndOfTransaction":
                        case "Finished":
                        case "Not found":
                            pollTransactionStatus = false;
                            callbackHandler.FuelTransactionComplete(transactionInfoResponse);
                            break;
                    }

                    while (pollTransactionStatus) {
                        callbackHandler.FuellingStatusUpdate(transactionInfoResponse);
                        //For DEBUG - just log the info from the response
                        Log.i("fuelPointStatusMonitor:Nozzle", String.valueOf(transactionInfoResponse.Packets.get(0).getData().getNozzle()));
                        Log.i("fuelPointStatusMonitor:State", transactionInfoResponse.Packets.get(0).getData().getState());
                        Log.i("fuelPointStatusMonitor:FuelGrade", transactionInfoResponse.Packets.get(0).getData().getFuelGradeName());
                        Log.i("fuelPointStatusMonitor:Volume", String.valueOf(transactionInfoResponse.Packets.get(0).getData().getVolume()));
                        Log.i("fuelPointStatusMonitor:Amount", String.valueOf(transactionInfoResponse.Packets.get(0).getData().getAmount()));
                        Thread.sleep(500);
                        transactionInfoResponse = ApiServiceHelper.executeControllerAPI(pts2ApiService.getTransactionInformation(transactionInfoRequest));
                        switch (transactionInfoResponse.Packets.get(0).getData().getState()) {
                            case "EndOfTransaction":
                            case "Finished":
                            case "Not found":
                                pollTransactionStatus = false;
                                callbackHandler.FuelTransactionComplete(transactionInfoResponse);
                                break;
                        }
                    }


                } catch (ApiException e) {
                    throw new RuntimeException(e);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });
        myTransactionMonitor.start();
    }

    @Override
    public void FetchFuelTransaction(String posId, int fuelpointId, String trnSeqNumber) {
        try {
            TransactionInformationResponse response = ApiServiceHelper.executeControllerAPI(pts2ApiService.getTransactionInformation(new TransactionInformationRequest(fuelpointId, Integer.parseInt(trnSeqNumber))));
            this.callbackHandler.ClearFuelTransactionBuffer(response);
            fuelPointStatusMonitor(response.Packets.get(0).getData().getPump(), response.Packets.get(0).getData().getTransaction());
        } catch (ApiException e) {
            Log.e("FetchTransactionFailed", e);
        }

    }

    @Override
    public void Logon(String posId, String ApplicationId, String clientVersion) throws InterruptedException {
        readConfiguration();
        this.callbackHandler.ForecourtControllerLoginComplete();
        this.callbackHandler.SetupComplete();
        this.callbackHandler.ForecourtControllerReady();
        setupGetFuelPointStatus();
    }

    @Override
    public GradePrices getConfiguredGradePrices() {
        if (configuredGradePrices != null)
            return this.configuredGradePrices;
        else
            return new GradePrices();
    }

    @Override
    public ArrayList<FuelPoint> getConfiguredFuelPoints() {
        return this.configuredFuelPoints;
    }

    @Override
    public void PreparePrepaidTransaction(String posId, String fuelpointId, String gradeId, String prepaidAmount) {
        try {
            String[] validGrades = this.callbackHandler.getValidGrades(Integer.parseInt(posId), Integer.parseInt(fuelpointId));
            PumpAuthorizeResponse response = ApiServiceHelper.executeControllerAPI(pts2ApiService.setPumpAuth(new PumpAuthorizeRequest(Integer.parseInt(fuelpointId), Float.valueOf(prepaidAmount) / 100f, validGrades)));
            if (!response.isError()) {
                // pump has been authorised - start monitoring the status of the fuelpoint and raise the FuelPointAuthorised event
                this.callbackHandler.FuelPointAuthorized(fuelpointId);
                fuelPointStatusMonitor(response.Packets.get(0).getData().getPump(), response.Packets.get(0).getData().getTransaction());

            }
        } catch (Exception e) {
            this.callbackHandler.Error(e);
            Log.e("PTS2 Exception", e);
        }
    }

    @Override
    public void CompleteFuelTransaction(String posId, String fuelpointId, String trnSeqNumber, long volume, long fueldispensedAmount) {
    //PTS-2 Transactions are auto-closed. No action required here.
       /* try {
            TransactionInformationResponse response = ApiServiceHelper.executeControllerAPI(pts2ApiService.getTransactionInformation(new TransactionInformationRequest(Integer.parseInt(fuelpointId), Integer.parseInt(trnSeqNumber))));
            callbackHandler.ClearFuelTransactionBuffer(response);
        } catch (ApiException e) {
            Log.e("FetchTransactionFailed", e);
        }*/
    }

    @Override
    public void setFuelGradeNames(Map fuelGradeNames) {

    }

    @Override
    public <T> FuelTransaction getFuelTransactionDetails(T response) {

        TransactionInformationResponse.Data transactionInformationResponse = ((TransactionInformationResponse) response).Packets.get(0).getData();

        FuelTransaction fuelTransaction = new FuelTransaction();
        fuelTransaction.setFuelPointId(transactionInformationResponse.getPump());
        fuelTransaction.setFuelGradeId(String.format("%02d", transactionInformationResponse.getFuelGradeId()));
        fuelTransaction.setSeqNumber(String.valueOf(transactionInformationResponse.getTransaction()));
        fuelTransaction.setVolume((long) transactionInformationResponse.getVolume()/10);
        fuelTransaction.setMoneyDue((long) transactionInformationResponse.getAmount());
        fuelTransaction.setPrice((long) transactionInformationResponse.getPrice()*10);
        return fuelTransaction;
    }

    @Override
    public <T> ForecourtControllerTransaction getTransactionData(T response) {
        ForecourtControllerTransaction forecourtControllerTransaction = new ForecourtControllerTransaction();
        forecourtControllerTransaction.setTime(System.currentTimeMillis());
        if(response instanceof TransactionInformationResponse) {
            TransactionInformationResponse.Data responseData = ((TransactionInformationResponse) response).Packets.get(0).getData();
            forecourtControllerTransaction.setFpId(responseData.getPump());
            forecourtControllerTransaction.setFcGradeId(String.format("%02d", responseData.getFuelGradeId()));
            forecourtControllerTransaction.setFuellingDataMonE((long) responseData.getAmount());
            forecourtControllerTransaction.setFuellingDataVolE((long) responseData.getVolume()/10);
            switch (responseData.getState()) {
                case "WaitingNozzleUpForAuthorization":
                    forecourtControllerTransaction.setFpMainState("05H");
                    forecourtControllerTransaction.setFpSubState2("");
                    break;
                case "Authorized":
                    forecourtControllerTransaction.setFpMainState("05H");
                    forecourtControllerTransaction.setFpSubState2("NozzleLifted");
                    break;
                case "Filling":
                    forecourtControllerTransaction.setFpMainState("09H");
                    forecourtControllerTransaction.setFpSubState2("NozzleLifted");
                    break;
                case "EndOfTransaction":
                case "Finished":
                    forecourtControllerTransaction.setFpMainState("02H");
                    forecourtControllerTransaction.setFpSubState2("");
                    break;
                default:
                    forecourtControllerTransaction.setFpMainState("");
                    forecourtControllerTransaction.setFpSubState2("");
                    break;
            }
        }
        else if(response instanceof PumpStatusResponse){
            PumpStatusResponse pumpStatus = (PumpStatusResponse) response;

            forecourtControllerTransaction.setFpId(pumpStatus.Packets.get(0).getData().getPump());
            forecourtControllerTransaction.setFcGradeId(String.format("%02d", pumpStatus.Packets.get(0).getData().getFuelGradeId()));
            forecourtControllerTransaction.setFpSubState2(pumpStatus.Packets.get(0).getData().isNozzleLifted() ? "NozzleLifted" : "");
            switch (pumpStatus.Packets.get(0).getType()) {
                case "PumpIdleStatus":
                    if(!pumpStatus.Packets.get(0).getData().isNozzleLifted())
                        forecourtControllerTransaction.setFpMainState("02H");
                    else
                        forecourtControllerTransaction.setFpMainState("05H");
                    break;
                case "PumpFillingStatus":
                    forecourtControllerTransaction.setFpMainState("09H");
                    break;
                default:
                    forecourtControllerTransaction.setFpMainState("");
                    break;
            }
        }


        return forecourtControllerTransaction;
    }

    @Override
    public void ChangeFuelPrices(String priceSetId, ArrayList<String> priceGroupIds, ArrayList<String> fuelGradeIds, ArrayList<String> priceGroups, String priceActivationDateTime) {
        //Get the PumpNozzle Configuration and map the grades to new grade prices in order to construct the price change request object
        if (fuelGradeIds.size() != 0) {
            ConfigurationRequest fuelGradeConfig = new ConfigurationRequest("GetFuelGradesConfiguration");
            try {
                FuelGradesConfigurationResponse fuelGradesConfigurationResponse = ApiServiceHelper.executeControllerAPI(pts2ApiService.getFuelGradesConfiguration(fuelGradeConfig));
                //price changes need to be made for each Pump
                ArrayList<PumpPriceChangeRequest.FuelGrade> newGradePriceList = new ArrayList<>();
                fuelGradesConfigurationResponse.Packets.get(0).getData().getFuelGrades().forEach(fuelGrade -> {
                    PumpPriceChangeRequest.FuelGrade newFuelGrade = new PumpPriceChangeRequest.FuelGrade();
                    newFuelGrade.setId(fuelGrade.getId());
                    newFuelGrade.setName(fuelGrade.getName());
                    newFuelGrade.setExpansionCoefficient(fuelGrade.getExpansionCoefficient());

                    String currentFuelGradeId = String.format("%02d", fuelGrade.getId());
                    int gradeIndex = fuelGradeIds.indexOf(currentFuelGradeId);
                    //if we don't find the grade in the current change request data - then maintain the current price
                    if (gradeIndex == -1) {
                        newFuelGrade.setPrice(fuelGrade.getPrice());
                    } else {
                        newFuelGrade.setPrice(Float.parseFloat(priceGroups.get(gradeIndex)) / 10.0F);
                    }
                    newGradePriceList.add(newFuelGrade);

                });
                if (newGradePriceList.size() != 0) {
                    PumpPriceChangeRequest newPriceRequest = new PumpPriceChangeRequest(newGradePriceList);
                    try {
                        PumpPriceChangeResponse response = ApiServiceHelper.executeControllerAPI(pts2ApiService.setPumpPriceChange(newPriceRequest));
                        if (!response.isError()) {
                            FuelGradesConfigurationResponse newFuelGradeConfig = ApiServiceHelper.executeControllerAPI(pts2ApiService.getFuelGradesConfiguration(new ConfigurationRequest("GetFuelGradesConfiguration")));
                            if (newFuelGradeConfig != null && !newFuelGradeConfig.isError()) {
                                setConfiguredGrades(newFuelGradeConfig.Packets.get(0).getData().getFuelGrades());
                                this.callbackHandler.ForecourtControllerReady();
                            }
                        }
                    } catch (ApiException e) {
                        Log.e("PTS2Exception", e);
                        throw new RuntimeException(e);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
                //this.callbackHandler.ForecourtControllerReady();
            } catch (
                    ApiException e) {
                Log.e("PTS2Exception", e);
                throw new RuntimeException(e);
            }
        }

    }
}
