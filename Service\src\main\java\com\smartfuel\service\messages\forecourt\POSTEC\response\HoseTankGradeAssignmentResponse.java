package com.smartfuel.service.messages.forecourt.POSTEC.response;

import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;

public class HoseTankGradeAssignmentResponse extends BaseResponse{
    private int dispenserNumber;
    private int hoseNumber;
    private int gradeNumber;
    private int tankNumber;

    public HoseTankGradeAssignmentResponse(String response) throws RuntimeException{
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenserNumber = Integer.parseInt(super.responseData[5]);
        this.hoseNumber = Integer.parseInt(super.responseData[6]);
        this.gradeNumber = Integer.parseInt(super.responseData[7]);
        this.tankNumber = Integer.parseInt(super.responseData[8]);
    }

    public int getGradeNumber() {
        return gradeNumber;
    }

    public int getDispenserNumber() {
        return dispenserNumber;
    }

    public int getHoseNumber() {
        return hoseNumber;
    }

    public int getTankNumber() {
        return tankNumber;
    }
}
