package com.smartfuel.service.forecourtcontroller;

import java.util.Map;

public interface IForecourtController<T> {


    void CancelFuelPointAuthorisation(String posId, String fuelpointId);
    void GetFuelPriceData();
    void Heartbeat();
    void Logon(String posId, String ApplicationId, String clientVersion) throws InterruptedException;
    void PreparePrepaidTransaction(String posId, String fuelpointId, String gradeId, String prepaidAmount);
    void setFuelGradeNames(Map<String, String> fuelGradeNames);
}
