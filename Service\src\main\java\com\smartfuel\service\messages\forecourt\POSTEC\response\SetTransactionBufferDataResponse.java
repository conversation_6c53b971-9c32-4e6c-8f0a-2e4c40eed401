package com.smartfuel.service.messages.forecourt.POSTEC.response;


import com.smartfuel.service.messages.forecourt.POSTEC.enums.NetworkMessageType;


public class SetTransactionBufferDataResponse extends BaseResponse {
    private int dispenser;
    private char bufferType;
    private int byteIndex;
    private int byteCount;
    private String bufferData;

    public SetTransactionBufferDataResponse(String response)  throws RuntimeException {
        super(response, NetworkMessageType.TCP);
        super.read();
        this.dispenser = Integer.parseInt(super.responseData[5]);
        this.bufferType = super.responseData[6].toCharArray()[0];
        this.byteIndex = Integer.parseInt(super.responseData[7]);
        this.byteCount = Integer.parseInt(super.responseData[8]);
        this.bufferData = super.responseData[9];
    }

    public int getDispenser() {
        return dispenser;
    }
    public char getBufferType() { return bufferType; }
    public int getByteIndex() { return byteIndex; }
    public int getByteCount() { return byteCount; }
    public String getBufferData() { return bufferData; }
}
